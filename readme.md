<!--
 * @description:
 * @Author: lexy
 * @Date: 2022-07-08 11:20:44
 * @LastEditors: lexy
 * @LastEditTime: 2023-01-30 15:16:35
-->

# gruul-mall-view-platform

宠有灵犀 商城模块 平台端后台管理系统

# Vue 3 + TypeScript + Vite 宠有灵犀开发规范

# 1.1、CSS 命名规范 BEM 规范

BEM 代表 块(block)，元素(element)，修饰符(modifier)，我们常用这三个实体开发组件。

-   中划线 ：仅作为连字符使用，表示某个块或者某个子元素的多单词之间的连接记号；
    \_\_ 双下划线：双下划线用来连接块和块的子元素；
    --双短横线：双短横线用来描述一个块或者块的子元素的一种状态；

示例：type-block\_\_element_modifier
CSS 中只能使用类名（不能是 ID）。
每一个块名应该有一个命名空间（前缀）
每一条 CSS 规则必须属于一个块。
BEM 解决问题:
组件之间的完全解耦，不会造成命名空间的污染，如：.mod-xxx ul li 的写法带来的潜在的嵌套风险。

# 1.2 JavaScript 编写规范 Eslint 规范

（1）本项目已配置 eslint 规范并在提交代码时做了校验，开发者必须复合规范开发。（利于团队维护）
（2）html 注释规范 <!-- 内容--start --> <!-- 内容--end -->
（3）函数注释规范
/\*\*

-   @LastEditors: 开发者
-   @description: 描述
-   @params: 参数
-   @returns {_} 返回值
    _/
    （4）变量注释规范 变量上方双下划线 //
    （5）变量命名规范 变量名使用驼峰法来命名 常亮使用大写以\_做分割
    （6）页面事件函数以 hadle 开头，用于函数区分。（便于维护）
    （7）工具类置于 utils 目录下，能用工具类就不要用第三方。（优势：体积轻便。）
    （8）定义的公共方法必须合理抽离，置于 hooks 文件目录下。（便于复用维护）

# 1.3 VUE 编写规范

（1）组件名 页面级别组件大写驼峰法命名，其他组件小写短横线连接（组件清晰，利于维护）
（2）公共组件置于 src 下的 components 目录下。
（3）首页面尽量精简。（结构清晰）
（4）引入资源顺序 vueAPi>vue 组件>工具函数>类型声明
（5）新建 vue 文件使用 Vue3.2+初始化 对应注释模块顺序

# 1.4 接口 编写规范

（1）接口定义 src 下的 apis 目录中，其接口文件名须与模块命名一致（便于维护）
（2）封装的请求接口必须以 "do" + "请求方法" + "具体说明" 命名。（函数区分和阐述语义）

# 1.5 提交规范

（1）代码前 run lint 无 error 项后再走提交流程
（2）代码提交遵循 commitlint 中配置对应内容 格式如:提交内容标签 提交头部描述 提交详细内容 是否存在破坏更改 是否存在副作用

# 1.6 目录格式

(1) libs 与 utils 界定为 libs 与业务逻辑相关复用，utils 只针对程序本身，外部程序一样可以使用不受影响
