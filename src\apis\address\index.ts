import { get } from '../http'
export enum ADDRESS_TYPE {
    PROVINCE = 'PROVINCE',
    CITY = 'CITY',
    DISTRICT = 'DISTRICT',
}
/**
 * 查询区域编码(省市区)
 */
export const doGetTypeAddressList = (type?: keyof typeof ADDRESS_TYPE, code?: string) => {
    let params: { type?: string; code?: string } = {}
    if (type) {
        params = { ...params, type }
    }
    if (code) {
        params = { ...params, code }
    }
    return get({ url: `gruul-mall-shop/shop/city/childes`, params })
}
