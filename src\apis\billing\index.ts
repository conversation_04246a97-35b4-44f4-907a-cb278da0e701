import { get, post, put, del, patch } from '../http'
import { ConfirmStatus } from './model/type'

/**
 * 分页查询三方计费记录
 * @param data
 * @returns
 */
export const doGetBillingRecordsList = (data?: any) => {
    return get({
        url: 'gruul-mall-search/search/api-cost/queryApiCostRecord',
        params: data,
    })
}

/**
 * 获取业务线、服务商等字典
 */
export const doGetBillingCostCode = (codeType: 'BUSINESS' | 'SERVICE') => {
    return get({
        url: 'gruul-mall-search/search/api-cost/queryApiCostCode',
        params: { codeType },
    })
}
/**
 * 导出查询的三方计费记录
 * @param data
 * @returns
 */
export const doPostExportBillingRecords = (data?: any) => {
    return post({
        url: 'gruul-mall-search/search/api-cost/exportApiCostRecord',
        data,
    })
}

/**
 * 分页查询三方计费账单
 * @param data
 * @returns
 */
export const doGetCostBillingList = (data?: any) => {
    return get({
        url: 'gruul-mall-search/search/api-cost/queryApiCostBill',
        params: data,
    })
}

/**
 * 确定三方计费账单
 * @param id string
 * @param confirmStatus string
 * @returns
 */
export const doPostConfirmCostBilling = (id: string, confirmStatus: ConfirmStatus) => {
    return post({
        url: `gruul-mall-search/search/api-cost/confirmCostBill/${id}`,
        params: { confirmStatus },
    })
}

/**
 * 导出查询的三方计费账单
 * @param data
 * @returns
 */
export const doPostCostBillingList = (data?: any) => {
    return post({
        url: 'gruul-mall-search/search/api-cost/exportApiCostBill',
        data,
    })
}
