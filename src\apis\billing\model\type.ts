export type CallStatus = 'FAIL' | 'SUCCESS'
export type CostType = 'ONCE' | 'MONTH' | 'YEAR' | 'SET_MENU' | 'BUY'
export type DictType = {
    code: string
    desc: string
}

export enum CallStatusEnum {
    FAIL = '失败',
    SUCCESS = '成功',
}

export enum CostTypeEnum {
    ONCE = '按次计费',
    MONTH = '按月计费',
    YEAR = '按年计费',
    SET_MENU = '按套餐计费',
    BUY = '买断',
}

export const callStatusMap: Record<CallStatus, string> = {
    FAIL: CallStatusEnum.FAIL,
    SUCCESS: CallStatusEnum.SUCCESS,
}

export const costTypeMap: Record<CostType, string> = {
    ONCE: CostTypeEnum.ONCE,
    MONTH: CostTypeEnum.MONTH,
    YEAR: CostTypeEnum.YEAR,
    SET_MENU: CostTypeEnum.SET_MENU,
    BUY: CostTypeEnum.BUY,
}

export type ConfirmStatus = 'PRE' | 'YES' | 'ERROR'

export enum ConfirmStatusEnum {
    PRE = '待确认',
    YES = '确认无误',
    ERROR = '确认异常',
}

export const confirmStatusMap: Record<ConfirmStatus, string> = {
    PRE: ConfirmStatusEnum.PRE,
    YES: ConfirmStatusEnum.YES,
    ERROR: ConfirmStatusEnum.ERROR,
}
