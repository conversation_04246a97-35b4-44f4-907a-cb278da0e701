/*
 * @description: 装修接口
 * @Author: lexy
 * @Date: 2022-09-05 14:27:02
 * @LastEditors: lexy
 * @LastEditTime: 2024-03-26 09:58:47
 */
import { get, post, put, del } from '../http'
import { SeckillProductParams, ShopListParams, OnlyPromotionGoodsParams } from './type'
import { FUNCTIONTYPE } from '@/views/decoration/platformDecoration/mobile/types/index'
/**
 * @LastEditors: lexy
 * @description: 获取聚合首页名称
 */
export const doGetEnumOfDecoration = (list: string[]) => {
    return get({
        url: `gruul-mall-addon-platform/platform/decoration/aggregation/decorate?aggregationPlatforms=${list.join(',')}`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取非页面装修数据
 */
export const doGetNotPageInfo = (platform: string, functionType: keyof typeof FUNCTIONTYPE) => {
    return get({
        url: `gruul-mall-addon-platform/platform/decoration/not/page/info?aggregationPlatform=${platform}&functionType=${functionType}`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 保存装修控件和页面
 * @returns {*}
 */
export const doSubmitControl = (data: any) => {
    return post({
        url: 'gruul-mall-addon-platform/platform/decoration/edit',
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取自定义页面列表
 */
export const doGetCustomPageList = (type: string) => {
    return get({
        url: `gruul-mall-addon-platform/platform/decoration/page/list?aggregationPlatform=${type}`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 根据id获取页面组件信息
 * @param {string} componentId
 */
export const doGetCustomPageComponent = (componentId: string) => {
    return get({
        url: `gruul-mall-addon-platform/platform/decoration/get?id=${componentId}`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 修改装修自定义页面名称
 * @param {string} name
 * @param {string} componentId
 * @returns {*}
 */
export const doChangeCutomPageName = (name: string, componentId: string) => {
    return put({
        url: `gruul-mall-addon-platform/platform/decoration/page/update/${name}?id=${componentId}`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 设置装修自定义页面为首页展示
 */
export const doSetDefault = (componentId: string, platforms: string) => {
    return put({
        url: `gruul-mall-addon-platform/platform/decoration/set/home/<USER>/${componentId}?aggregationPlatform=${platforms}`,
    })
}

/**
 * @LastEditors: lexy
 * @description: 设置装修为默认模版
 */
export const doSetShopDefault = (tempId: string) => {
    return put({
        url: `gruul-mall-addon-platform/templates/default/${tempId}`,
    })
}

/**
 * @LastEditors: lexy
 * @description: 设置装修自定义页面为首页展示
 */
export const doSetLocal = (componentId: string, platforms: string) => {
    return put({
        url: `gruul-mall-addon-platform/platform/decoration/set/local/page/${componentId}?aggregationPlatform=${platforms}`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 删除自定义页面
 */
export const doDelCustomPage = (componentId: string, platforms?: string) => {
    return del({
        url: `gruul-mall-addon-platform/platform/decoration/del/${componentId}?aggregationPlatform=${platforms}`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取平台一级类目和对应商品数量
 * @returns {*}
 */
export const doGetPlatformLevelAndComNum = (params: any) => {
    return get({
        url: 'gruul-mall-addon-platform/platform/category/platformCategoryFirstIdWithProductNum',
        params,
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取分类页
 */
export const doGetCommodityClass = () => {
    // return get({
    //     url: '/gruul-mall-shop/decoration/classify/page/list?platform=H5',
    // })
}
/**
 * @LastEditors: lexy
 * @description: 装修分类页查询分类
 * @param {string} ids
 * @param {string} categoryLevel
 */
export const doGetCategoryLevel = (ids: string[], categoryLevel: string) => {
    return get({
        url: '/gruul-mall-addon-platform/platform/category/by/ids',
        params: { ids: ids.join(','), categoryLevel },
    })
}
/**
 * @LastEditors: lexy
 * @description:根据二级分类id查询商品
 */
export const doGetCommodityBySecCateId = (ids: string, categoryLevel: string) => {
    return get({
        url: '/gruul-mall-addon-platform/platform/category/by/platformCategoryId',
        params: {
            ids,
            categoryLevel,
        },
    })
}

/**
 * @LastEditors: lexy
 * @description:分页获取平台装修模板
 */
export const doGetPlatformTemplates = (params: any) => {
    return get({
        url: '/gruul-mall-addon-platform/templates',
        params,
    })
}
/**
 * @LastEditors: lexy
 * @description:获取平台装修模板详情
 */
export const doGetPlatformTemplatesDetail = (id: string) => {
    return get({
        url: `/gruul-mall-addon-platform/templates/${id}`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 创建平台装修模板
 * @returns {*}
 */
export const doPostPlatformTemplates = (data: any) => {
    return post({
        url: 'gruul-mall-addon-platform/templates',
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 删除模板
 */
export const doDelPlatformTemplates = (id: string) => {
    return del({
        url: `gruul-mall-addon-platform/templates/${id}`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 复制模板
 * @returns {*}
 */
export const doPostPlatformTemplatesClone = (id: string) => {
    return post({
        url: `gruul-mall-addon-platform/templates/clone`,
        data: { templateId: id },
    })
}
/**
 * @LastEditors: lexy
 * @description: 编辑模板
 */
export const doPutPlatformTemplates = (data: any) => {
    return put({
        url: `gruul-mall-addon-platform/templates`,
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 修改模板启用状态
 */
export const doPutPlatformTemplatesState = (data: any) => {
    return put({
        url: `gruul-mall-addon-platform/templates/default-template`,
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description:获取全部平台装修页面列表
 */
export const doGetPlatformTemplatesPages = (params: any) => {
    return get({
        url: `gruul-mall-addon-platform/templates/pages`,
        params,
    })
}

/**
 * @LastEditors: lexy
 * @description:获取平台装修页面详情
 */
export const doGetPlatformPagesDetail = (id: string) => {
    return get({
        url: `/gruul-mall-addon-platform/pages/${id}`,
    })
}
/**
 * @LastEditors: lexy
 * @description:分页获取平台装修页面
 */
export const doGetPlatformPages = (params: any) => {
    return get({
        showLoading: false,
        url: `gruul-mall-addon-platform/pages`,
        params,
    })
}
/**
 * @LastEditors: lexy
 * @description: 删除页面
 */
export const doDelPlatformPages = (id: string) => {
    return del({
        url: `gruul-mall-addon-platform/pages/${id}`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 复制页面
 * @returns {*}
 */
export const doPostPlatformPagesClone = (id: string) => {
    return post({
        url: `gruul-mall-addon-platform/pages/clone/${id}`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 保存页面
 * @returns {*}
 */
export const doPostPlatformPagesSave = (data: any) => {
    return post({
        showLoading: false,
        url: `gruul-mall-addon-platform/pages`,
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 编辑页面
 */
export const doPutPlatformPages = (data: any) => {
    return put({
        url: `gruul-mall-addon-platform/pages`,
        data,
    })
}

/**
 * @: 查询PC装修页面数据
 */
export const doGetOpeningUp = (pageType = 'RECOMMENDED_MALL_HOME_PAGE') => {
    return get({
        url: `gruul-mall-addon-platform/pages/opening-up/PC_MALL/${pageType}`,
    })
}

/**
 * @description: 查询秒杀场次
 */
export const doGetSecondsKill = (params = { shopId: '' }) => {
    return get({ url: `addon-seckill/seckillPromotion/consumer/sessions`, params })
}

/**
 * @description: 根据秒杀场次查询秒杀场次的商品
 */
export const doGetSeckillSessionsProduct = (params: SeckillProductParams) => {
    return get({ url: `addon-seckill/seckillPromotion/consumer/seckillSessionsProduct`, params })
}

// gruul-mall-shop/decoration-pages

/**
 * @description: 获取店铺
 */
export const doGetShopList = (data: ShopListParams) => {
    return post({ url: `gruul-mall-shop/shop/info/search/shop`, data })
}

/**
 * @description: 查询会员专享商品
 */
export const doGetOnlyPromotionGoods = (data: OnlyPromotionGoodsParams) => {
    data.platformVisit = true
    return post({ url: `addon-member-only/onlyPromotion/consumer/onlyProduct`, data })
}

/**
 * 获取参与会员类型
 * @returns
 */
export const doGetMemberType = () => {
    return get({ url: `addon-member/paid/member/all/type` })
}
