/**
 * @: 秒杀商品
 */
export interface SeckillProductParams {
    shopId: string
    startTime: string
    size: number
    current: number
}

/**
 * @: 秒杀商品
 */
export interface SeckillProductRes {
    price: string
    productId: string
    productName: string
    productPic: string
    productStock: string
    secKillId: string
    secKillPrice: string
    shopId: string
    stockStatus: string
}

/**
 * @: 获取店铺查询参数
 */
export interface ShopListParams {
    name?: string
    shopIds?: string[]
    shopType?: ShopType
    size?: number
    current?: number
    productIsNotEmpty: boolean
}
export type ShopType = 'SELF_OWNED' | 'PREFERRED' | 'ORDINARY' | ''

// 参与会员
export type JoinMemberType = {
    id: string
    name: string
    include: boolean // true参与  false限制
    labelJson: {
        fontColor: string
        labelColor: string
    }
}

// 活动状态 [0未开始 1进行中 2已结束 3暂停中 4商家关闭]
export type OnlyStatusType = keyof typeof ONLY_STATUS_TYPES
export enum ONLY_STATUS_TYPES {
    NOT_STARTED, //未开始
    PROCESSING, //进行中
    OVER, //已结束
    ILLEGAL_SELL_OFF, //违规下架
    SHOP_SELL_OFF, //下架
}

/**
 * @: 会员专享商品
 */
export interface OnlyPromotionGoodsParams {
    shopId?: string
    keyword?: string
    memberType?: JoinMemberType
    onlyStatus?: OnlyStatusType
    productParams?: {
        productId?: string
        shopId?: string
        onlyId?: string
    }
    [key: string]: any
}
