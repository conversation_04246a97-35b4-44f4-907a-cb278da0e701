/*
 * @description:
 * @Author: lexy
 * @Date: 2022-03-15 09:24:01
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-09 14:23:14
 */
import axios, { AxiosResponse } from 'axios'
import router from '../router/index'
import { R } from './http.type'
import storage from '@/utils/Storage'
import { useAdminInfo } from '@/store/modules/admin'
import { CRUD_ERROR_CODE, TOKEN_OVERDUE } from './http.type'
import { ElMessage } from 'element-plus'
import createUuid from '@/utils/uuid'
import { signByUser } from '@/apis/sign'
import { useLastModified } from '@/store/modules/lastModified'
// 取消请求
import { AxiosCanceler } from '@/utils/http/axiosCancel'
const axiosCance = new AxiosCanceler()
let isCance = true
// const LastModified = ref('')
// const $useLastModified = useLastModified()
const TOKEN_TYPE = 'Bearer '
axios.defaults.headers.post['Content-Type'] = 'application/json'
axios.defaults.headers.put['Content-Type'] = 'application/json'
const request = axios.create({
    baseURL: import.meta.env.VITE_BASE_URL,
    timeout: Number(import.meta.env.VITE_REQUEST_TIME_OUT),
    withCredentials: false,
    headers: {
        'Device-Id': uuidHandle(),
        'Client-Type': 'PLATFORM_CONSOLE',
        'Shop-Id': '0',
        Platform: 'PC',
    },
})
//是否是单体应用
const isSingle = import.meta.env.VITE_IS_SINGLE && import.meta.env.VITE_IS_SINGLE.toLowerCase() === 'true'
//单体应用矫正正则
const singleUrlCorrectRegex = /\/?.*?\//
//url矫正函数
const urlCorrect = (currentUrl: undefined | string) => {
    return !currentUrl ? currentUrl : isSingle ? currentUrl.replace(singleUrlCorrectRegex, '/') : currentUrl
}
// 是否正在刷新的标记
let isRefreshing = false
// 重试队列
let requests: any = []
//请求拦截器
request.interceptors.request.use(
    (config) => {
        const token = useAdminInfo().getterToken
        // @ts-ignore
        if (!isRefreshing && token) {
            config.headers.Authorization = TOKEN_TYPE + token
        }
        config.url = urlCorrect(config.url)
        axiosCance.addPending(config)
        isCance = true
        return config
    },
    (error) => {
        return Promise.reject(error)
    },
)

//响应拦截器
// request.interceptors.response.use(
//     async (response: AxiosResponse<R>) => {
//         const result = response.data
//         if (result.data?.total) {
//             result.data.total = Number(result.data.total)
//             result.data.size = Number(result.data.size)
//             result.data.pages = Number(result.data.pages)
//             result.data.current = Number(result.data.current)
//         }
//         if (response.status !== 200 || !result) {
//             return Promise.reject({
//                 msg: '服务器异常',
//             })
//         }
//         const code = result.code
//         // 2 需要登陆 3 token不可用 4 token已过期 reFreshToken 换 token
//         if (code === 4) {
//             if (!isRefreshing) {
//                 isRefreshing = true
//                 refreshingFn()
//             }
//             // 返回未执行 resolve 的 promise
//             return new Promise((resole) => {
//                 requests.push((token: string) => {
//                     response.headers.Authorization = TOKEN_TYPE + token
//                     resole(request(response.config))
//                 })
//             })
//         }
//         if (TOKEN_OVERDUE.includes(code)) {
//             useAdminInfo().REMOVE_ADMIN_INFO()
//             router
//                 .push({
//                     path: '/login',
//                     query: {
//                         redirect: router.currentRoute.value.fullPath,
//                     },
//                 })
//                 .catch((fail) => {
//                     console.log('跳转失败', fail)
//                 })
//             return result
//         }
//         if ([200, 304].includes(code)) {
//             return Promise.resolve(response)
//         }
//         // 错误捕获
//         if (CRUD_ERROR_CODE.includes(code)) {
//             ElMessage.error(response.data.msg)
//         }

//         axiosCance.removePending(response.config)
//         return Promise.resolve(response)
//     },
//     (error) => {
//         if (error.response) {
//             return Promise.reject(error)
//         } else if (error.response === undefined && isCance) {
//             isCance = false
//             return Promise.reject(error)
//         }
//     },
// )

export function uuidHandle() {
    const $storage = new storage()
    let uuid = $storage.getItem('UUID')
    if (!uuid) {
        uuid = createUuid(8)
        $storage.setItem('UUID', uuid, 60 * 60 * 24)
    }
    return uuid
}
// const refreshingFn = async () => {
//     const { value: refresh_token } = useAdminInfo().adminInfo
//     try {
//         isRefreshing = true
//         const { data, code } = await signByUser({ refresh_token, grant_type: 'refresh_token' })
//         const token = useAdminInfo().SET_ADMIN_INFO(data)
//         requests.forEach((cd: (t: string) => any) => cd(token))
//         requests = []
//         isRefreshing = false
//     } catch (error) {
//         console.log('error', error)
//     } finally {
//         isRefreshing = false
//     }
// }
export default request
