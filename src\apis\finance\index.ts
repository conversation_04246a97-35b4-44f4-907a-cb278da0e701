/*
 * @description:
 * @Author: lexy
 * @Date: 2022-10-11 10:46:11
 * @LastEditors: lexy
 * @LastEditTime: 2022-12-22 10:16:30
 */
import { get, post, put, del, patch } from '../http'

export const doGetFinance = (data: any) => {
    return post({
        url: 'gruul-mall-overview/overview/statement',
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取提现工单
 */
export const doGetWithdrawList = (params: any) => {
    return get({
        url: 'gruul-mall-overview/overview/withdraw',
        params,
    })
}

/**
 * @LastEditors: lexy
 * @description: 结算/拒绝申请
 */
export const doGetCheckWithdraw = (orderNo: string, data: any) => {
    return put({
        url: `gruul-mall-overview/overview/withdraw/audit/${orderNo}`,
        data,
    })
}

/**
 * @LastEditors: zrb
 * @description: 查询余额
 */
export const doGetShopBalance = () => {
    return get({
        url: 'gruul-mall-overview/overview/withdrawYts/platform',
    })
}

/**
 * @LastEditors: zrb
 * @description: 提现申请
 */
export const doPostWithdraw = (amount: number, type: string) => {
    return post({
        url: 'gruul-mall-overview/overview/withdrawYts/withdraw',
        data: {
            amount,
            type,
        },
    })
}

/**
 * @LastEditors: zrb
 * @description: 获取平台提现申请列表
 * @returns {*}
 */

export const doGetPlatformWithdrawList = (params: any) => {
    return get({
        url: 'gruul-mall-overview/overview/withdrawYts/platform/page',
        params,
    })
}
