/*
 * @description:
 * @Author: lexy
 * @Date: 2022-08-12 13:38:38
 * @LastEditors: lexy
 * @LastEditTime: 2022-10-22 13:08:50
 */
import { get, post, put, del } from '../http'
/**
 * @LastEditors: lexy
 * @description:打印机信息新增
 * @param {string} deviceNo
 * @param {string} printName
 * @returns {*}
 */
export const doAddPrint = (deviceNo: string, printName: string, defSelfShop: string, defSelfSupplier: string) => {
    return post({
        url: 'gruul-mall-freight/logistics/print/save',
        data: { deviceNo, printName, defSelfShop, defSelfSupplier },
    })
}
/**
 * @LastEditors: lexy
 * @description: 打印机信息修改
 * @param {string} deviceNo
 * @param {string} printName
 * @param {string} id
 * @returns {*}
 */
export const doUpdatePrint = (deviceNo: string, printName: string, id: string, defSelfShop: string, defSelfSupplier: string) => {
    return post({
        url: 'gruul-mall-freight/logistics/print/update',
        data: { deviceNo, printName, id, defSelfShop, defSelfSupplier },
    })
}
/**
 * @LastEditors: lexy
 * @description: 打印机列表
 * @param {*} data
 * @returns {*}
 */
export const doGetPrintList = (params) => {
    return get({
        url: 'gruul-mall-freight/logistics/print/list',
        params,
    })
}
/**
 * @LastEditors: lexy
 * @description: 打印机删除
 * @param {*} id
 * @returns {*}
 */
export const doDelPrintById = (id: string) => {
    return del({
        url: `gruul-mall-freight/logistics/print/del/${id}`,
    })
}
