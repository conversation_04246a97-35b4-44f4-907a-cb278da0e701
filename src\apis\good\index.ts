/*
 * @description:
 * @Author: lexy
 * @Date: 2022-03-21 10:28:56
 * @LastEditors: lexy
 * @LastEditTime: 2024-04-01 13:22:17
 */

import { get, post, put } from '../http'
import { http } from '@/utils/http'
import type { RetrieveParam, ApiRetrieve } from './model'
/**
 * @LastEditors: lexy
 * @description:分页获取商品和规格信息
 * @param {any} data
 * @returns {*}
 */
export const doGetProductSkus = (data?: any) => {
    return get({
        url: 'gruul-mall-goods/manager/product/getProductSkus',
        params: data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 检索商品接口
 */
export const doGetRetrieveProduct = (retrieveParams: Partial<RetrieveParam>) => {
    return http.post<ApiRetrieve>({
        url: 'gruul-mall-search/search/product',
        data: retrieveParams,
        headers: {
            unRemovePending: true,
        },
    })
}

/**
 * @LastEditors: lexy
 * @description: 批量查询店铺信详情
 * @param {string} shopIds
 * @returns {*}
 */
export const doPostShopInfo = (shopIds: string[]) => {
    return post({ url: `gruul-mall-shop/shop/info/batch`, data: shopIds })
}

/**
 * 获取所有专区
 */
export const getAllRegionList = (data: any) => {
    return get({
        url: '/gruul-mall-goods/manager/sale/mode/list',
        params: data,
    })
}

/**
 * 获取所有展示分类
 * @param data
 */
export const getAllCategory = (data: any) => {
    return get({
        url: '/goods-open/manager/show/category/get/all',
        params: data,
    })
}
/**
 * 获取展示分类列表
 * @param data
 */
export const getProList = (data: any) => {
    return get({
        url: '/goods-open/manager/product/list',
        params: data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取商品列表
 * @param {any} params
 * @returns {*}
 */
export const doGetProductList = (params: any) => {
    return get({
        url: 'gruul-mall-addon-platform/platform/product/get/all',
        params,
    })
}

/**
 * @LastEditors: lexy
 * @description: 更新商品状态
 * @param {string} ids
 * @param {string} status
 */

export const doUpdateSellStatus = (ids: any, status: string) => {
    return put({
        url: `gruul-mall-goods/manager/product/updateStatus/${status}`,
        data: ids,
    })
}
/**
 * @LastEditors: lexy 获取单个商品信息
 * @description: 获取单个商品信息
 * @param {string} id
 */
export const doGetSingleCommodity = (id: any, shopId: string) => {
    return get({
        url: `gruul-mall-goods/manager/product/get/${shopId}/${id}`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 查询商品规格组与sku列表
 */
export const doGetCommoditySku = (shopId: string, productId: any) => {
    return get({
        url: `gruul-mall-storage/storage/shop/${shopId}/product/${productId}`,
    })
}
export const doGetCategory = () => {}
export const doGetHighestCategoryLevel = (params: any) => {
    return get({
        url: `gruul-mall-goods/goods/product/category/categoryLevel1WithProductNum`,
        params,
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取属性列表
 * @param {any} params
 */
export const getAttsList = (params: any) => {
    return get({
        url: 'gruul-mall-goods/manager/attribute/template/list',
        params,
    })
}
export const doGetSupplierList = (params: any) => {
    return get({
        url: '/addon-supplier/supplier/manager/product/list',
        params,
    })
}
/**
 * @description 供应商商品下架
 * @param data 下架参数
 * @param status 状态
 * @returns
 */
export const doUpdateSupplierSellStatus = (data: any, status = '') => {
    return put({
        url: `addon-supplier/supplier/manager/product/updateStatus/${status}`,
        data,
    })
}

export const doGetSeachSupplierSearchList = (params: any = {}) => {
    return get({ url: 'gruul-mall-shop/shop/info/getSupplierInfo', params })
}

/**
 * @description 获取商品详情接口
 * @param id 商品id号
 * @returns
 */
export const doGetCommodityDetails = (id: any, params: any) => {
    return get({ url: `gruul-mall-goods/manager/product/show/${id}`, params })
}
/**
 * @description 获取供应商商品详情接口
 * @param id 商品id号
 * @param params 查询参数信息
 * @returns
 */
export const doGetSupplierCommodityDetails = (id: any, params: any = {}) => {
    return get({ url: `addon-supplier/supplier/manager/product/show/${id}`, params })
}

/**
 * @description 获取平台端商家待审核商品
 * @param params 查询参数信息
 * @returns
 */
export const doGetShopExamineGoods = (params: any = {}) => {
    return get({ url: 'gruul-mall-addon-platform/platform/product/get/audit/all', params })
}

export const doPutShopExamineGoods = (status: string, data: any) => {
    return put({ url: `gruul-mall-goods/manager/product/updateStatus/${status}`, data })
}

export const doGetSupplierExamineGoods = (params: any = {}) => {
    return get({ url: 'addon-supplier/supplier/manager/product/audit', params })
}

export const doPutSupplierExamineGoods = (status: string, data: any) => {
    return put({ url: `gruul-mall-goods/manager/product/updateStatus/${status}`, data })
}

/**
 * @LastEditors: zrb
 * @description: 供应商商品恢复
 * @param {string} ids
 */

export const recoverSupplierGoods = (ids: any) => {
    return put({
        url: 'addon-supplier/supplier/manager/product/violationRecover',
        data: ids,
    })
}
/**
 * @LastEditors: zrb
 * @description: 商家商品恢复
 * @param {string} ids
 */

export const recoverShopGoods = (ids: any) => {
    return put({
        url: 'gruul-mall-goods/manager/product/violationRecover',
        data: ids,
    })
}
