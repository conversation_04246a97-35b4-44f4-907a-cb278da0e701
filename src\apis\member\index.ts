/*
 * @description: 会员接口
 * @Author: lexy
 * @Date: 2022-11-25 16:21:39
 * @LastEditors: lexy
 * @LastEditTime: 2023-05-29 16:21:33
 */
import { get, post, put, del } from '../http'
import type { LocationQueryValue } from 'vue-router'

/**
 * @LastEditors: lexy
 * @description: 获取会员列表
 */
export const doGetMemberBenefit = (params: any) => {
    return get({
        url: 'gruul-mall-user/user/member/rights/',
        params,
    })
}
/**
 * @LastEditors: lexy
 * @description: 新增会员权益
 */
export const doPostMemberBenefit = (data: any) => {
    return post({
        url: 'gruul-mall-user/user/member/rights/save',
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 修改会员权益
 */
export const doPutMemberBenefit = (data: any) => {
    return post({
        url: 'gruul-mall-user/user/member/rights/update',
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 开启关闭会员权益
 * @param {boolean} rightsSwitch
 * @param {string} id
 * @returns {*}
 */
export const doPutMemberBenefitStatus = (switchType: boolean, id: string) => {
    return put({
        url: `gruul-mall-user/user/member/rights/${switchType}`,
        params: {
            id,
        },
    })
}
/**
 * @LastEditors: lexy
 * @description: 删除会员权益
 * @param {string} ids
 * @returns {*}
 */
export const doDelMemberBenefit = (ids: string[]) => {
    return del({
        url: `gruul-mall-user/user/member/rights/del/${ids} `,
    })
}
/**
 * @LastEditors: lexy
 * @description: 恢复默认
 */
export const doGetMemberBenefitDefault = (type: string) => {
    return get({
        url: `gruul-mall-user/user/member/rights/default?rightsType=${type}`,
    })
}

/**
 * @LastEditors: lexy
 * @description: 获取免费会员列表
 */
export const doGetFreeMemberList = () => {
    return get({
        url: 'gruul-mall-user/user/free/member/list',
    })
}

/**
 * @LastEditors: lexy
 * @description: 获取会员权益配置
 */
export const doGetAvailableMemberConfig = () => {
    return get({
        url: 'gruul-mall-user/user/member/rights/usable',
    })
}
/**
 * @LastEditors: lexy
 * @description: 保存免费会员配置
 * @param {*} data
 */
export const doPostAvailableMemberConfig = (data: any) => {
    return post({
        url: 'gruul-mall-user/user/free/member/save',
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 删除免费会员
 * @param {string} id
 */
export const doPostAvailableMember = (id: string) => {
    return del({
        url: `gruul-mall-user/user/free/member/${id}`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取免费会员配置
 */
export const doGetAvailableMember = (id: string) => {
    return get({
        url: `gruul-mall-user/user/free/member/info?id=${id}`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 更新免费会员
 * @param  data
 */
export const doPutAvailableMember = (data: any) => {
    return post({
        url: 'gruul-mall-user/user/free/member/update',
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取会员详情
 */
export const doGetMemberDetail = (id: string) => {
    return get({
        url: `gruul-mall-user/user/info?userId=${id}`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取会员详情余额支付信息
 * @returns {*}
 */
export const doGetPaymentInfo = (userId: string) => {
    return get({
        url: `gruul-mall-payment/user/payment/history/savings/info?userId=${userId}`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取会员详情余额明细
 */
export const doGetBalanceHistory = (userId: string, params: any) => {
    return get({
        url: `gruul-mall-payment/user/payment/history/`,
        params: {
            userId,
            ...params,
        },
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取会员详情交易明细
 */
export const doGetTradeList = (userId: string | LocationQueryValue[], params: any) => {
    return get({
        url: `gruul-mall-user/user/deal/detail/list/${userId}`,
        params: {
            ...params,
        },
    })
}
/**
 * @LastEditors: lexy
 * @description: 会员详情修改用户信息
 */
export const doPostMemberInfo = (data: any) => {
    return post({
        url: 'gruul-mall-uaa/uaa/user/data',
        data,
    })
}
/**
 * @description 获取会员导入模板
 * @returns {*}
 */
export const doGetImportMemberTemplate = () => {
    return get({ url: 'gruul-mall-uaa/uaa/user/data/downloadExcelTemplate' })
}
/**
 * @description 免费会员设置会员标签
 * @param data 参数
 * @returns
 */
export const doPostFreeMemberSetLabel = (data: any) => {
    return post({ url: 'gruul-mall-user/user/free/member/saveLabel', data })
}
