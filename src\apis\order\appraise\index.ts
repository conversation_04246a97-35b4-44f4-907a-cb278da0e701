/*
 * @description:
 * @Author: lexy
 * @Date: 2022-09-13 14:54:02
 * @LastEditors: lexy
 * @LastEditTime: 2022-09-13 16:50:06
 */
import { get, put, post } from '../../http'
/**
 * @LastEditors: lexy
 * @description:评价管理列表
 * @returns {*}
 */
export const doGetEvaluate = (params: any) => {
    return get({
        url: 'gruul-mall-order/order/evaluate',
        params,
    })
}
/**
 * @LastEditors: lexy
 * @description: 批量设为精选/取消精选
 * @param {boolean} isExcellent
 * @param {string} data id[]
 * @returns {*}
 */
export const doPutIsExcellentEvaluate = (isExcellent: boolean, data: string[]) => {
    return put({
        url: `gruul-mall-order/order/evaluate/excellent/${isExcellent}`,
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 批量设为隐藏/取消隐藏
 * @param {boolean} isHide
 * @param {string} data id[]
 * @returns {*}
 */
export const doHideEvaluate = (isHide: boolean, data: string[]) => {
    return put({
        url: `gruul-mall-order/order/evaluate/hide/${isHide}`,
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 平台回复
 * @param {boolean} evaluateId 评估id
 * @param {string} data 回复内容
 * @returns {*}
 */
export const doPostShopReplyEvaluate = (evaluateId: string, reply: string) => {
    return post({
        url: `gruul-mall-order/order/evaluate/reply`,
        data: {
            evaluateId,
            reply,
        },
    })
}
/**
 * @LastEditors: lexy
 * @description: 删除评论
 * @param {string} evaluateId id
 * @returns {*}
 */
export const doDelIsEvaluate = (evaluateId: string) => {
    return post({
        url: `gruul-mall-order/order/evaluate/delete`,
        data: { evaluateId },
    })
}

/**
 * @LastEditors: lexy
 * @description: 获取评价回复信息
 * @param {string} evaluateId id
 * @returns {*}
 */
export const doGetEvaluateDetail = (evaluateId: string) => {
    return get({
        url: `gruul-mall-order/order/evaluate/reply/list/${evaluateId}`,
    })
}

/**
 * @LastEditors: lexy
 * @description: 删除评价回复信息
 * @param {string} evaluateReplyId id
 * @returns {*}
 */
export const doDelEvaluateReply = (evaluateReplyId: string) => {
    return post({
        url: `gruul-mall-order/order/evaluate/reply/delete`,
        data: { evaluateReplyId },
    })
}

/**
 * @LastEditors: lexy
 * @description: 隐藏评价回复信息
 * @param {string} evaluateReplyId id
 * @param {boolean} isDisplay 是否隐藏
 * @returns {*}
 */
export const doHideEvaluateReply = (evaluateReplyId: string, isDisplay: boolean) => {
    return put({
        url: `gruul-mall-order/order/evaluate/reply/hide/${isDisplay}`,
        data: evaluateReplyId,
    })
}
