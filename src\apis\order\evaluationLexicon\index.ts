import { get, put, post, del } from '../../http'

export enum LEXICON_TYPE {
    USER = 1,
    MERCHANT = 2,
}

export enum LEXICON_RELATE_TYPE {
    PRODUCT = 1,
    CATEGORY = 2,
}

/**
 * @LastEditors: lexy
 * @description:词库管理列表
 * @returns {*}
 */
export const doGetEvaluationLexicon = (params: any) => {
    return get({
        url: 'gruul-mall-order/order/evaluate/library',
        params,
    })
}

/**
 * @LastEditors: lexy
 * @description: 获取评价词库详情
 * @param {string} id
 * @returns {*}
 */
export const doGetEvaluateLexiconDetail = (id: string) => {
    return get({
        url: `gruul-mall-order/order/evaluate/library/detail/${id}`,
    })
}

/**
 * @LastEditors: lexy
 * @description: 删除词库
 * @param {Array} ids
 * @returns {*}
 */
export const doDelEvaluateLexicon = (data: number[]) => {
    return del({
        url: `gruul-mall-order/order/evaluate/library/delete`,
        data
    })
}

/**
 * @LastEditors: lexy
 * @description: 更新词库详情
 * @param {string} id
 * @returns {*}
 */
export const doPutEvaluateLexiconDetail = (
    id: string,
    type: keyof typeof LEXICON_TYPE,
    relateType: keyof typeof LEXICON_RELATE_TYPE,
    content: string,
    idKey: 'productId' | 'categoryId',
    idValue?: string,
) => {
    return put({
        url: `gruul-mall-order/order/evaluate/library/update/${id}`,
        data: {
            type,
            relateType,
            content,
            [idKey]: idValue,
        },
    })
}

/**
 * @LastEditors: lexy
 * @description: 新增词库
 * @param {string} id
 * @returns {*}
 */
export const doPostEvaluateLexicon = (
    type: keyof typeof LEXICON_TYPE,
    relateType: keyof typeof LEXICON_RELATE_TYPE,
    content: string,
    idKey: 'productId' | 'categoryId',
    idValue?: string,
) => {
    return post({
        url: `gruul-mall-order/order/evaluate/library/save `,
        data: {
            type,
            relateType,
            content,
            [idKey]: idValue,
        },
    })
}

/**
 * 导入词库文件
 * @param file       文件
 * @param type       用户/商家
 * @param relateType 商品/类目
 * @returns 
 */
export const doImportEvaluationLexicon = (file: File, type: keyof typeof LEXICON_TYPE, relateType: keyof typeof LEXICON_RELATE_TYPE) => {
    return post({
        url: `gruul-mall-order/order/evaluate/library/import`,
        data: {
            file,
            type,
            relateType
        }
    })
}
