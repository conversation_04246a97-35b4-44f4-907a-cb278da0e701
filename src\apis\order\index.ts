/*
 * @description: 订单相关接口
 * @Author: lexy
 * @Date: 2022-06-30 17:09:02
 * @LastEditors: lexy
 * @LastEditTime: 2023-02-02 16:55:18
 */
import { get, post, put } from '../http'
/**
 * @LastEditors: lexy
 * @description: 获取订单列表
 * @returns {*}
 */
export const doGetOrderList = (params: any, ids?: string) => {
    if (ids) {
        return get({ url: `gruul-mall-order/order/${ids}`, params })
    }
    return get({ url: `gruul-mall-order/order`, params })
}
/**
 * @LastEditors: lexy
 * @description: 通过orderNo获取未发货订单
 * @param {string} orderNo
 * @param {string} shopOrderNo
 * @returns {*}
 */
export const dogetOrderNotDetailsByOrderNo = (orderNo: string, shopOrderNo: string, shopId: string) => {
    return get({ url: `/gruul-mall-order/order/${orderNo}/shopOrder/${shopOrderNo}/undelivered`, params: { shopId } })
}
/**
 * @LastEditors: lexy
 * @description: 商品批量发货
 * @param {string} orderNo
 * @returns {*}
 */
export const doPostOrderBatchDeliver = (data: any) => {
    return put({ url: `gruul-mall-order/order/batch/deliver`, data })
}
/**
 * @LastEditors: lexy
 * @description: 商品发货
 * @param {string} orderNo
 * @returns {*}
 */
export const doPutOrderDetailsByOrderNo = (orderNo: string, data) => {
    return put({ url: `/gruul-mall-order/order/${orderNo}/deliver`, data })
}

export const importNeedDeliveryOrders = (orderNos?: string[]) => {
    return post({ url: '/gruul-mall-order/order/getPlatFormOrder', data: { orderNos } })
}
