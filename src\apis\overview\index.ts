/*
 * @description:
 * @Author: lexy
 * @Date: 2022-10-22 19:36:00
 * @LastEditors: lexy
 * @LastEditTime: 2023-09-06 13:25:51
 */
import { get, del, post } from '../http'
export enum DATE_TYPE {
    TODAY,
    NEARLY_A_WEEK,
    NEARLY_A_MONTH,
    CUSTOM,
}
export enum ShopMode {
    COMMON,
    SUPPLIER,
    O2O,
}
type TradeStatisticsParams = {
    startDate: string
    endDate: string
    dateType: keyof typeof DATE_TYPE
    shopMode: keyof typeof ShopMode
}
/**
 * @LastEditors: lexy
 * @description: 获取热门店铺top5
 */
export const doGetShopStatistics = (params: Partial<TradeStatisticsParams>) => {
    return get({
        url: '/gruul-mall-overview/overview/deal/shop',
        params,
    })
}
/**
 * @LastEditors: lexy
 * @description:获取热门商品top5
 * @param {Partial} params
 * @returns {*}
 */
export const doGetCommodityStatistics = (params: Partial<TradeStatisticsParams>) => {
    return get({
        url: '/gruul-mall-overview/overview/deal/product/sales',
        params,
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取交易统计
 */
export const doGetTradeStatistics = (params: Partial<TradeStatisticsParams>) => {
    return get({
        url: '/gruul-mall-overview/overview/deal/statistics',
        params,
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取商品数量/违规商品
 */
export const doGetCommodityNumber = () => {
    return get({
        url: '/gruul-mall-goods/manager/product/quantity',
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取新增商品数量
 * @returns {*}
 */
export const doGetNewCommodityNumber = () => {
    return get({
        url: '/gruul-mall-goods/manager/product/today/quantity',
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取店铺数量和待审核数量
 */
export const doGetShopNumber = () => {
    return get({
        url: '/gruul-mall-shop/shop/shopQuantity',
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取今日新增店铺数量
 * @returns {*}
 */
export const doGetNewShopNumber = () => {
    return get({
        url: 'gruul-mall-shop/shop/today/shopQuantity',
    })
}

/**
 * @LastEditors: lexy
 * @description: 获取访客数量
 */
export const doGetVisitNumber = () => {
    return get({
        url: 'gruul-mall-user/user/uv',
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取订单数量
 */
export const doGetOrderCount = () => {
    return get({
        url: 'gruul-mall-order/order/overview/platform',
    })
}
/**
 * @description 获取下载中心列表
 * @param params 参数
 * @returns
 */
export const doGetDownloadCenterList = (params: any = {}) => {
    return get({ url: 'gruul-mall-overview/export/list', params })
}
/**
 * @description 删除下载中心列表
 * @param id 下载中心列表id
 * @returns
 */
export const doDeleteDownloadCenterFile = (id: string) => {
    return del({ url: `gruul-mall-overview/export/${id}/remove` })
}
/**
 * @description 批量删除下载中心列表
 * @param data 数据信息
 * @returns
 */
export const doDeleteBatchDownloadCenterFiles = (data: any) => {
    return del({ url: 'gruul-mall-overview/export/batchRemove', data })
}
/**
 * @description 获取下载中的数量
 * @returns
 */
export const doGetDownloadingFileCount = () => {
    return get({ url: 'gruul-mall-overview/export/count' })
}

/**
 * @description 导出对账单
 * @param data
 * @returns
 */
export const doPostExportStatementData = (data: any) => {
    return post({ url: 'gruul-mall-overview/overview/statement/export', data })
}
/**
 * @description 导出提现工单
 * @param data
 * @returns
 */
export const doPostExportWithdrawData = (data: any) => {
    return post({ url: 'gruul-mall-overview/overview/withdraw/export', data })
}

/**
 * @description 导出平台结算列表
 * @param data
 * @returns
 */
export const doPostExportShopWithdrawData = (data: any) => {
    return post({ url: 'gruul-mall-overview/overview/withdraw/platform/export', data })
}
