/*
 * @description:
 * @Author: lexy
 * @Date: 2022-12-23 13:11:09
 * @LastEditors: lexy
 * @LastEditTime: 2022-12-23 13:13:28
 */
import { get, post, put, del, patch } from '../http'
/**
 * @LastEditors: lexy
 * @description:
 * @param 开始时间
 * @param 结束时间
 * @param 会员昵称
 */
interface PaymentParams {
    startTime: string
    endTime: string
    userNickname: string
    size: number
    current: number
}
/**
 * @LastEditors: lexy
 * @description:储值订单列表
 * @param {any} data
 * @returns {*}
 */
export const doGetPaymentHistory = (data?: PaymentParams) => {
    return get({
        url: 'gruul-mall-payment/user/payment/history/savings/order',
        params: data,
    })
}
