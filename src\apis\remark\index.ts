/*
 * @description:
 * @Author: lexy
 * @Date: 2022-12-21 18:08:52
 * @LastEditors: lexy
 * @LastEditTime: 2023-02-09 13:07:24
 */
import { get, put, post } from '../http'
/**
 * @LastEditors: lexy
 * @description: 订单备注
 * @param {string} ids  总订单号 或者 店铺订单号 平台端 为总订单号列表 商家端 为店铺订单号列表
 * @param {string} status
 */
export const doPutOrderRemark = (nos: string[], remark: string) => {
    return put({
        url: `gruul-mall-order/order/remark/batch`,
        data: {
            nos,
            remark,
        },
    })
}
/**
 * @LastEditors: lexy
 * @description: 售后订单备注
 * @param {string} ids
 * @param {string} status
 */
export const doPutAfsOrderRemark = (nos: string[], remark: string) => {
    return put({
        url: `gruul-mall-afs/afs/order/remark/batch`,
        data: {
            nos,
            remark,
        },
    })
}
/**
 * @LastEditors: lexy
 * @description: 提现工单备注
 */
export const doPutWithdrawRemark = (nos: string[], remark: string) => {
    return put({
        url: 'gruul-mall-overview/overview/withdraw/batch/remark',
        data: {
            nos,
            remark,
        },
    })
}
/**
 * @LastEditors: lexy
 * @description: 储值订单批量备注
 */
export const doPostpaymentHistoryRemark = (ids: string[], remark: string) => {
    return put({
        url: 'gruul-mall-payment/user/payment/history/savings/order/remark',
        data: {
            ids,
            remark,
        },
    })
}
/**
 * @LastEditors: lexy
 * @description: 积分订单批量备注
 */
export const doPostIntegralOrderRemark = (nos: string[], remark: string) => {
    return put({
        url: 'addon-integral/integral/order/remark/batch',
        data: {
            nos,
            remark,
        },
    })
}
