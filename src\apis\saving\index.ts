/*
 * @description:
 * @Author: lexy
 * @Date: 2022-10-12 13:40:06
 * @LastEditors: lexy
 * @LastEditTime: 2022-10-12 14:30:43
 */
import { get, post, put, del, patch } from '../http'
import type { BalancePayInfoItem, SavingManageItem } from '@/views/vipMoney/types'
/**
 * @LastEditors: lexy
 * @description: 获取储值管理信息
 */
export const doGetSavingManage = (params?: any) => {
    return get({
        url: '/gruul-mall-user/user/saving/manage/get',
        params,
    })
}
/**
 * @LastEditors: lexy
 * @description: 修改储值管理信息开关
 */
export const doGPutSavingManage = (status: boolean) => {
    return put({
        url: `/gruul-mall-user/user/saving/manage/update/${status}`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 编辑储值管理信息
 * @param {boolean} discountsState 优惠状态 0无优惠 1有优惠
 * @param {SavingManageItem} ruleJson  储值管理列表不许传递id
 * @returns {*}
 */
export const doPostSavingManage = (id: string, discountsState: boolean, ruleJson: SavingManageItem[], balancePayInfo: BalancePayInfoItem[]) => {
    return post({
        url: `/gruul-mall-user/user/saving/manage/edit`,
        data: {
            id,
            discountsState,
            ruleJson,
            balancePayInfo,
        },
    })
}
//
