
import { RateJobType } from '@/views/set/components/RateJobSet'
import { get, put, post, del } from '../../http'

/**
 * @LastEditors: lexy
 * @description:初始化扣率变更任务列表
 * @returns {*}
 */
export const doGetRateJobList = (params: any) => {
  return get({ url: 'gruul-mall-addon-platform/platform/job/list', params })
}
/**
* @LastEditors: lexy
* @description: 添加扣率变更任务
* @param {RateJobType} data
*/
export const doAddRateJob = (data: RateJobType) => {
  return post({ url: 'gruul-mall-addon-platform/platform/job/create', data })
}
/**
* @LastEditors: lexy
* @description: 编辑扣率变更任务
* @param {RateJobType} data
* @returns {*}
*/
export const doEditRateJob = (data: RateJobType) => {
  return post({ url: 'gruul-mall-addon-platform/platform/job/edit', data })
}
/**
* @LastEditors: lexy
* @description: 查看扣率变更任务详情
* @param {id} id 
* @returns {*}
*/
export const doGetRateJobDetail = (id: string) => {
  return get({ url: 'gruul-mall-addon-platform/platform/job/detail', params: { id } })
}

/**
 * 
* @LastEditors: lexy
* @description: 删除扣率变更任务
* @param {id} id 
* @returns {*}
 */
export const doDelRateJob =  (id: string) => {
  return post({ url: 'gruul-mall-addon-platform/platform/job/remove', data: { id } })
}