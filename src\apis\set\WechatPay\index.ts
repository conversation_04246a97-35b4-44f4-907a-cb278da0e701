/*
 * @description:
 * @Author: lexy
 * @Date: 2022-07-28 16:41:32
 * @LastEditors: lexy
 * @LastEditTime: 2022-07-28 18:05:35
 */
import { get, put, post, del } from '@/apis/http'
import type { ApiParameters } from '@/views/set/components/WechatPay'
/**
 * @LastEditors: lexy
 * @description:编辑(新增/修改) 支付商户信息
 * @returns {*}
 */
export const doEditmerchant = (data: ApiParameters) => {
    return post({ url: 'gruul-mall-payment/merchant/edit', data })
}
/**
 * @LastEditors: lexy
 * @description: 获取商户配置信息
 * @param {string} payType
 * @returns {*}
 */
export const doGetMerchant = (payType: string) => {
    return get({ url: `gruul-mall-payment/merchant/get/`, params: { payType } })
}
