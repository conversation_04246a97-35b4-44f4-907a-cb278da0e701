/*
 * @description:
 * @Author: lexy
 * @Date: 2022-06-21 15:41:35
 * @LastEditors: lexy
 * @LastEditTime: 2023-02-17 09:24:49
 */
import { get, put, post, del } from '../http'
type Typedata = {
    id: string
    logisticsCompanyName: string
    logisticsCompanyCode: string
    logisticsCompanyStatus: string
}
type TypeupdateData = {
    logisticsCompanyName: string
    logisticsCompanyCode: string
    logisticsCompanyStatus: string
}
// export const doUpdateSellStatus = (ids: string[], status: string) => {
//   return put({
//       url: `gruul-mall-goods/manager/product/updateStatus/${status}`,
//       data: ids,
//   })
// }
/**
 * @LastEditors: lexy
 * @description:初始化物流公司列表
 * @returns {*}
 */
export const doGetLogisticsList = (params: any) => {
    return get({ url: 'gruul-mall-freight/fright/list', params })
}
/**
 * @LastEditors: lexy
 * @description: 添加物流公司
 * @param {Typedata} data
 */
export const doUpdateLogistics = (data: TypeupdateData) => {
    return post({ url: 'gruul-mall-freight/fright/add', data })
}
/**
 * @LastEditors: lexy
 * @description: 编辑物流公司
 * @returns {*}
 */
export const doEditLogistics = (data: Typedata) => {
    return post({ url: 'gruul-mall-freight/fright/update', data })
}

/**
 * @LastEditors: lexy
 * @description: 批量删除物流信息
 * @param {string} arr
 * @returns {*}
 */
export const doDelLogistics = (arr: string[]) => {
    return del({ url: `gruul-mall-freight/fright/del/${arr.join(',')}` })
}
/**
 * @LastEditors: lexy
 * @description:批量禁用物流信息
 * @param {*} state
 * @param {string} ids
 * @returns {*}
 */
export const doForbiddenLogistics = (state: string, ids: string[]) => {
    return put({ url: `gruul-mall-freight/fright/batch/${state}`, data: ids })
}
/**
 * @LastEditors: lexy
 * @description: 隐私协议新增/修改
 */
export const doPostPrivateAgreement = (platformPrivacyAgreementText: string, agreementType: string, id?: string) => {
    return post({
        url: 'gruul-mall-addon-platform/platform/privacyAgreement',
        data: {
            platformPrivacyAgreementText,
            agreementType,
            id,
        },
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取隐私协议
 */
export const doGetPrivateAgreement = (agreementType: string) => {
    return get({
        url: 'gruul-mall-addon-platform/platform/privacyAgreement',
        params: { agreementType },
    })
}
