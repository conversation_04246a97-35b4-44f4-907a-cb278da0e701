import { get, put, post, del } from '../../http'

export const doGetShopDeliveryConfig = () => {
    return get({ url: '/gruul-mall-shop/shop/deliver' })
}

export const doPutShopDeliveryConfig = (data: any) => {
    return put({ url: '/gruul-mall-shop/shop/deliver', data })
}

export const doGetLogisticsList = (data: any) => {
    return get({
        url: 'gruul-mall-shop/shop/logistics/address/list',
        params: data,
    })
}

//修改或者新增地址
export const setLogisticsDddress = (data: any) => {
    return post({
        url: 'gruul-mall-shop/shop/logistics/address/set',
        data,
    })
}

export const delLogisticsById = (id: string) => {
    return del({
        url: `gruul-mall-shop/shop/logistics/address/del/${id}`,
    })
}

/**
 * @LastEditors: lexy
 * @description:快递设置信息获取
 * @returns {*}
 */
export const doGetCourierInfo = () => {
    return get({ url: 'gruul-mall-freight/logistics/settings/get' })
}
interface PrintSetForm {
    id: string
    customer: string
    key: string
    secret: string
}
/**
 * @LastEditors: lexy
 * @description:快递设置新增/修改
 * @returns {*}
 */
export const doCourierUpdateAndEdit = (data: PrintSetForm) => {
    return post({ url: 'gruul-mall-freight/logistics/settings/edit', data })
}
