/*
 * @description:
 * @Author: lexy
 * @Date: 2022-04-06 17:32:50
 * @LastEditors: lexy
 * @LastEditTime: 2023-07-31 15:33:02
 */
import { get, post, put, del, patch } from '../http'

export const getAuth = () => {
    return get({ url: '/gruul-mall-uaa/uaa/menu/navigate' })
}
/**
 * @LastEditors: lexy
 * @description: 新增权限
 * @param {name:string,path:string,perm:string} data
 */
export const doNewPermissions = (data: any) => {
    return post({
        url: '/gruul-mall-uaa/uaa/menu',
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 编辑权限
 * @param {menuId string}
 * @param {name:string,path:string,perm:string} data
 */
export const doEditPermissions = (menuId: string, data: any) => {
    return put({
        url: `/gruul-mall-uaa/uaa/menu/${menuId}`,
        data,
    })
}

/**
 * @LastEditors: lexy
 * @description: 删除权限
 * @param {string} menuId
 */
export const doDelPermissions = (menuId: string) => {
    return del({
        url: `/gruul-mall-uaa/uaa/menu/${menuId}`,
    })
}

/**
 * @LastEditors: lexy
 * @description: 上传图片
 * @param {File} data
 */
export const doUpload = (data: any) => {
    return post({
        url: 'gruul-mall-carrier-pigeon/oss/upload',
        data,
    })
}

/**
 * @LastEditors: lexy
 * @description: 设置oss
 */

export const doSaveOSS = (data: any) => {
    return post({
        url: 'gruul-mall-carrier-pigeon/oss/edit',
        data,
    })
}

/**
 * @LastEditors: lexy
 * @description: 获取oss服务商配置
 * @param {string} type oss服务商
 */

export const doGetOSS = (type: string) => {
    return get({
        url: `gruul-mall-carrier-pigeon/oss/current/config/?type=${type}`,
    })
}

/**
 * @LastEditors: lexy
 * @description: 设置基础信息
 */

export const doSaveBasicSet = (data: any) => {
    return post({
        url: 'gruul-mall-order/order/config/timeout',
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取基础信息
 */
export const doGetBasicSet = () => {
    return get({
        url: 'gruul-mall-order/order/config/timeout',
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取采购时间信息
 */
export const doGetSupplierBasicSet = () => {
    return get({
        url: 'addon-supplier/supplier/order/config/timeout',
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取采购时间信息
 */
export const doPutSupplierBasicSet = (data: { payTimeout: string }) => {
    return put({
        url: 'addon-supplier/supplier/order/config/timeout',
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 短信设置
 */
export const doSmsSet = (data: any) => {
    return post({
        url: 'gruul-mall-carrier-pigeon/sms/saveAndEdit/current/config',
        data,
    })
}

/**
 * @LastEditors: lexy
 * @description: 获取短信配置
 */
export const doGetSms = (type: string) => {
    return get({
        url: `gruul-mall-carrier-pigeon/sms/current/config?type=${type}`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取消息配置列表
 */
export const doGetNewsList = (params: any) => {
    return get({
        url: '/gruul-mall-carrier-pigeon/pigeon/notice/platform',
        params,
    })
}

/**
 * @LastEditors: lexy
 * @description: 消息新增
 */
export const doPostNews = (data: any) => {
    return post({
        url: '/gruul-mall-carrier-pigeon/pigeon/notice',
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 消息编辑
 */
export const doPutNews = (noticeId: any, data: any) => {
    return put({
        url: `/gruul-mall-carrier-pigeon/pigeon/notice/${noticeId} `,
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 消息推送
 */
export const doPatchNews = (noticeId: any) => {
    return patch({
        url: `/gruul-mall-carrier-pigeon/pigeon/notice/${noticeId} `,
    })
}
/**
 * @LastEditors: lexy
 * @description: 消息删除
 */
export const doDelNews = (noticeId: any) => {
    return del({
        url: `/gruul-mall-carrier-pigeon/pigeon/notice/${noticeId} `,
    })
}
/**
 * @description 获取商品审核的配置信息
 * @returns
 */
export const doGetGoodsAuditSetting = () => {
    return get({ url: '/gruul-mall-goods/manager/product/audit/setting' })
}
/**
 * @description 修改商品审核配置信息
 * @param productAuditType
 * @returns
 */
export const doPutGoodsAuditSetting = (productAuditType = '') => {
    return put({ url: `/gruul-mall-goods/manager/product/audit/setting/${productAuditType}` })
}
/**
 * @description 获取积分订单超时时间
 * @returns
 */
export const doGetIntergalTimeoutSetting = () => {
    return get({ url: '/addon-integral/integral/order/time' })
}
/**
 * @description 修改积分订单超时时间
 * @param time 修改的时间
 * @returns
 */
export const doPutIntergalTimeoutSetting = (time: any) => {
    return put({ url: `addon-integral/integral/order/time/${time}` })
}

/**
 * @description 获取设置默认推荐的店铺
 * @returns
 */
export const doGetDefaultStore = () => {
    return get({ url: `/gruul-mall-shop/shop/config/guide` })
}
/**
 * @description 设置默认推荐店铺
 * @returns
 */
export const doSetDefaultStore = (shopId: string) => {
    return post({ url: `/gruul-mall-shop/shop/config/default`, data: { shopId } })
}

/**
 * @description 查询随机注水值
 * @returns
 */
export const doGetRandomInjection = () => {
    return get({ url: '/addon-supplier/supplier/config/salesNum' })
}

/**
 * @description 设置随机注水值
 * @returns
 */
export const doSetRandomInjection = (min: number, max: number, showSaleNum: boolean) => {
    return post({ url: '/addon-supplier/supplier/config/salesNum', data: { max, min, showSaleNum } })
}

/**
 * @description 查询会员专享活动预告配置
 * @returns
 */
export const doGetOnlyPromotionData = () => {
    return get({ url: '/addon-member-only/onlyPromotion/onlyList', params: { onlyStatus: 'NOT_STARTED', platformVisit: true } })
}

/**
 * @description 查询会员专享活动预告配置
 * @returns
 */
export const doGetOnlyPromotionConfigSet = () => {
    return get({ url: '/addon-member-only/onlyPromotion/config/get' })
}

/**
 * @description 配置会员专享活动预告
 * @returns
 */
export const doSetOnlyPromotionConfigSet = (open: boolean, preTime: number, infos?: { onlyId: string; name: string }[]) => {
    return post({ url: '/addon-member-only/onlyPromotion/config/save', data: { infos, open, preTime } })
}

/**
 * 查询 商家托管开关
 * @param data
 */
export const doGetTrusteeshipConfig = () => {
    return get({
        url: 'gruul-mall-shop/shop/config/trusteeship',
    })
}

/**
 * 配置商家托管功能开关
 * @param data
 */
export const doPostTrusteeshipConfig = (isOpen: boolean) => {
    return post({
        url: 'gruul-mall-shop/shop/config/trusteeship',
        data: { isOpen },
    })
}
