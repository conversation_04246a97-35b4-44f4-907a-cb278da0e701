/*
 * @description:
 * @Author: lexy
 * @Date: 2022-04-19 13:06:34
 * @LastEditors: lexy
 * @LastEditTime: 2024-03-05 09:44:57
 */
import { get, post, put, del, patch } from '../http'

/**
 * @LastEditors: lexy
 * @description: 获取商铺列表
 */
export const doGetShopList = (params: any) => {
    return get({
        url: '/gruul-mall-shop/shop',
        params,
    })
}
/**
 * @LastEditors: lexy
 * @description: 新增商户
 * @param {*} data
 * @returns {*}
 */
export const doAddShops = (data: any) => {
    return post({
        url: '/gruul-mall-shop/shop',
        data,
    })
}

export const doDelShop = (params: string[]) => {
    const toStr = params.join()
    return del({
        url: `/gruul-mall-shop/shop`,
        params: {
            shopIds: toStr,
        },
    })
}
export const doEditShop = (data: any) => {
    return put({
        url: `/gruul-mall-shop/shop/${data.id}`,
        data,
    })
}

export const doChangeStatus = (params: string[], isEnable: boolean) => {
    const toStr = params.join()
    return patch({
        url: `/gruul-mall-shop/shop/${isEnable}`,
        params: {
            shopIds: toStr,
        },
    })
}

/**
 * 审核店铺
 * @param shopId 店铺id
 * @param isPass 是否通过
 */
export const doShopAudit = (shopId: string, isPass: boolean, refuseReason?: string) => {
    return put({
        url: `/gruul-mall-shop/shop/${shopId}/${isPass}`,
        params: {
            refuseReason,
        },
    })
}

/**
 * 重新审核店铺
 * @param shopId 店铺id
 */
export const doShopReAudit = (shopId: string) => {
    return put({
        url: `/gruul-mall-shop/shop/restartAudit/${shopId}`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取类目列表
 */
export const doGetCategory = (params: any) => {
    return get({
        url: 'gruul-mall-addon-platform/platform/category/list',
        params,
    })
}
export const doNewCategory = (data: any) => {
    return post({
        url: 'gruul-mall-addon-platform/platform/category/save',
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 根据父级数据获取类目列表
 */
export const doGetCategoryTableList = (parentIds: string) => {
    return get({
        url: 'gruul-mall-addon-platform/platform/category/child/category',
        params: { parentIds },
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取select类目选择项
 * @param {string} level
 * @param {string} parentId
 */
export const doGetCategoryLevel = (level: string | number, parentId: string | number) => {
    return get({
        url: 'gruul-mall-addon-platform/platform/category/list/',
        params: {
            level: `LEVEL_${Number(level) + 1}`,
            parentId,
            size: 1000,
        },
        showLoading: false,
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取分类
 * @param {string} level
 * @param {number} parentId 父级分类id
 * @param {number} size
 * @param {number} current
 * @returns {*}
 */
export const doGetCategoryLevelByParentId = (level: 'LEVEL_2' | 'LEVEL_1' | 'LEVEL_3', parentId: number | string, size: number, current: number) => {
    return get({ url: 'gruul-mall-addon-platform/platform/category/list/level', params: { level, parentId, size, current } })
}
/**
 * @LastEditors: lexy
 * @description: 类目排序
 * @param {string} parentId
 */
export const doSortCategory = (ids: string[], parentId: string) => {
    return put({
        url: 'gruul-mall-addon-platform/platform/category/order',
        data: {
            sortedIds: ids,
            parentId,
        },
        showLoading: false,
    })
}
/**
 * @LastEditors: lexy
 * @description: 删除类目
 * @param {string} id
 */
export const doDelCategory = (id: string) => {
    return del({
        url: `gruul-mall-addon-platform/platform/category/delete/${id}`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 更新类目
 * @param {any} data
 */
export const doUpdateCategory = (id: string, data: any) => {
    return put({
        url: `gruul-mall-addon-platform/platform/category/update/${id}`,
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取用户列表
 */
export const doGetUserList = (current: number, size: number, keywords?: string) => {
    return get({
        url: '/gruul-mall-uaa/uaa/shop/admin/available',
        params: {
            current,
            size,
            keywords,
        },
        showLoading: false,
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取单个用户信息
 * @param {string} userId 用户id
 */

export const doGetSingleUser = (userId: string) => {
    return get({
        url: `/gruul-mall-uaa/uaa/shop/admin/${userId}`,
        params: {},
    })
}

/**
 * @description 获取平台端单个商铺关联的签约类目
 * @param { string | number } params.shopId 店铺id
 * @returns { Promise<any> }
 */
export const doGetShopSigningCategoryList = (params: any = {}) => {
    return get({
        url: `/gruul-mall-addon-platform/platform/shop/signing/category/list`,
        params,
    })
}

/**
 * @LastEditors: lexy
 * @description: 获取小程序码
 * @param path  扫码进入的小程序页面路径，最大长度 128 字节，不能为空,可页面参数,请求头需带上shopId
 * @param envVersion 默认"release" 要打开的小程序版本。正式版为 "release"，体验版为 "trial"，开发版为 "develop"
 * @param width 二维码的宽度，单位 px。最小 280px，最大 1280px,非必填,默认430px
 * @param autoColor 默认true 自动配置线条颜色，如果颜色依然是黑色，则说明不建议配置主色调
 * @param lineColor autoColor 为 false 时生效，使用 rgb 设置颜色 例如 {"r":"xxx","g":"xxx","b":"xxx"}
 * @param isHyaline 是否需要透明底色
 */
export const doGetWxChat = (params: {
    path: string
    envVersion?: string
    width?: string
    autoColor?: boolean
    lineColor?: boolean
    isHyaline?: boolean
}) => {
    return get({ url: `gruul-mall-uaa/uaa/wx/chat/`, params })
}

/**
 * 识别营业执照信息
 * @param url  营业执照照片地址
 * @returns
 */
export const doGetBusinessInfoByOSS = (url: string) => {
    return get({ url: 'gruul-mall-carrier-pigeon/ocr/recognizeBusiness', params: { url } })
}

/**
 * 识别身份证信息
 * @param url  身份证照片地址
 * @returns
 */
export const doGetIDCardInfoByOSS = (url: string) => {
    return get({ url: 'gruul-mall-carrier-pigeon/ocr/recognizeIdCard', params: { url } })
}

/**
 * 根据地区码获取银行行号
 * @param areaCode 地区码
 * @param bankName 银行名称
 * @returns
 */
export const doGetBankNumByAreaCode = (
    current: number,
    size: number,
    areaCode: string,
    bankName = '',
    otherCode: { bankcode?: string; netBankCode?: string },
) => {
    return get<any>({
        url: `gruul-mall-search/search/netBank-code/page/${areaCode}`,
        params: { current, size, bankName, ...otherCode },
        showLoading: false,
        errorImmediately: false,
    })
}

/**
 * OCR校验结果
 * @param shopId  店铺ID
 * @returns
 */
export const doCheckOCR = (shopId: string) => {
    return post({ url: `gruul-mall-shop/shop/updateUserInfo/${shopId}` })
}
