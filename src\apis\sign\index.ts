/*
 * @description:
 * @Author: lexy
 * @Date: 2022-03-22 13:13:59
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-25 15:31:32
 */
import { R } from '@apis/http.type'
import { get, post, put } from '../http'
import { useMenuList } from '@/store/modules/menu'

/**
 * @LastEditors: lexy
 * @description: login
 * @param {any} data
 * @returns {*}
 */
export const signByUser = (data: any) => {
    return post({
        showLoading: true,
        url: '/gruul-mall-uaa/uaa/auth/oauth/token',
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取验证码
 * @param {} data
 */
export const doPostSmsCode = (data: any) => {
    return post({ url: `gruul-mall-uaa/uaa/auth/captcha/sms`, data })
}
/**
 * @LastEditors: lexy
 * @description: 无感刷新
 * @param {any} data
 * @returns {*}
 */
export const doReFreshObtainToken = (data: any) => {
    return post({
        showLoading: true,
        url: '/gruul-mall-uaa/uaa/oauth/token',
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 平台中心 查询账户信息
 */
export const doGetUserDataAccount = () => {
    return get({
        url: `gruul-mall-uaa/uaa/user/data/account`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 重置密码
 * @param {string} code
 * @param {string} password
 * @param {string} confirmPassword
 * @param {string} mobile
 * @returns {*}
 */
export const doPutResetPassword = (code: string, password: string, confirmPassword: string, mobile: string) => {
    return put({
        url: `gruul-mall-uaa/uaa/auth/reset/password`,
        data: {
            code,
            password,
            confirmPassword,
            mobile,
        },
    })
}
/**
 * 获取滑块验证码
 */
export const doGetCaptchaSlider = () => {
    return get({ url: `gruul-mall-uaa/uaa/auth/captcha/slider` })
}
/**
 * @LastEditors: lexy
 * @description: 重置密码 发送重置密码短信验证码
 */
export const doGetResetPasswordSms = (mobile: string) => {
    return get({
        url: `gruul-mall-uaa/uaa/auth/reset/${mobile}/password/sms`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 平台中心 发送重置密码短信验证码
 */
export const doGetMyResetPasswordSms = () => {
    return get({
        url: `gruul-mall-uaa/uaa/auth/reset/my/password/sms`,
    })
}
/**
 * @LastEditors: lexy
 * @description: 平台中心 重置密码
 * @param {string} code
 * @param {string} password
 * @param {string} confirmPassword
 * @returns {*}
 */
export const doPutMyResetPassword = (code: string, password: string, confirmPassword: string) => {
    return put({
        url: `gruul-mall-uaa/uaa/auth/reset/my/password`,
        data: {
            code,
            password,
            confirmPassword,
        },
    })
}

/**
 * 获取个人资料
 */
export const myData = () => {
    return get({
        url: 'gruul-mall-uaa/uaa/shop/admin/mine',
    })
}

/**
 * 查询用户菜单导航
 */
export const doGetUserMenu = (): Promise<R<any>> => {
    return new Promise((resolve, reject) => {
        get({
            url: 'gruul-mall-uaa/uaa/menu/navigate',
        })
            .then((res: any) => {
                if (res.code !== 200) {
                    resolve(res)
                    return
                }
                const data = res.data
                const menuMap = new Map<string, Set<string>>()
                const menuConf = data.menuConfig
                for (const key in menuConf) {
                    menuMap.set(key, new Set(menuConf[key]))
                }
                data.menuConfig = menuMap
                resolve(res)
            })
            .catch((err: any) => reject(err))
    })
}

/**
 * 保存用户菜单配置
 *
 * @param data 菜单配置
 */
export const doPostSetUserMenuConfig = (data: Map<string, Set<string>>) => {
    const object = {}
    for (const [key, set] of data) {
        object[key] = [...set]
    }
    return post({
        url: 'gruul-mall-uaa/uaa/menu/navigate',
        data: object,
        showLoading: false,
    })
}

/**
 * 退出登录
 */
export const doPostLogout = () => {
    // 清除缓存
    useMenuList().REMOVE_MENU_DATA()
    location.replace(location.pathname + '#/sign')
    // return post({
    //     url: 'gruul-mall-uaa/logout',
    //     showLoading: false,
    // })
}
