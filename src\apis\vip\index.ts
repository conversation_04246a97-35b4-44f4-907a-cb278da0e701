/*
 * @description:
 * @Author: lexy
 * @Date: 2022-10-14 09:32:29
 * @LastEditors: lexy
 * @LastEditTime: 2023-02-15 17:28:22
 */
import { get, post } from '../http'

/**
 * @LastEditors: lexy
 * @description: 请求参数
 * @returns {*}
 */
export interface ParamsUserTag {
    addTagList?: TagItemA[]
    updateTagList?: TagItem[]
    delUserTagIdList?: string[]
    userIdList: string[]
}
/**
 * @LastEditors: lexy
 * @description: 添加标签
 * @param {*}id 会员标签id
 * @param {*}tagName 会员标签名称
 * @param {*} option 是否选中true选中,false未选中
 */
interface TagItemA {
    tagName: string
    option: boolean
}
/**
 * @LastEditors: lexy
 * @description: 添加标签
 * @param {*}id 会员标签id
 * @param {*}tagName 会员标签名称
 * @param {*} option 是否选中true选中,false未选中
 */
interface TagItem extends TagItemA {
    id: string
}
/**
 * @LastEditors: lexy
 * @description: 分页查询会员信息
 */
export const doGetBaseVipList = (params: any) => {
    return get({
        url: '/gruul-mall-user/user/list',
        params,
    })
}
/**
 * @LastEditors: lexy
 * @description: 更新标签
 */
export const doPostUserTag = (data: ParamsUserTag) => {
    return post({
        url: '/gruul-mall-user/user/userTag/',
        data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 查询会员所有标签
 */
export const doGetUserTag = (params = {}) => {
    return get({
        url: '/gruul-mall-user/user/userTag/',
        params,
    })
}
/**
 * @LastEditors: lexy
 * @description: 会员余额调整(充值/扣除)
 * @param {string} userId
 * @param {number} value
 * @param {*} changeType
 * @param {string} extendInfo
 * @returns {*}
 */
export const doPostbalanceChange = (userId: string, value: number, changeType: 'INCREASE' | 'REDUCE', extendInfo = '', remark?: string) => {
    return post({
        url: '/gruul-mall-user/user/balance/change',
        data: { userId, value, changeType, extendInfo, remark },
    })
}
/**
 * @LastEditors: lexy
 * @description: 获取黑名单列表
 */
export const doGetBlackList = (params: any) => {
    return get({
        url: 'gruul-mall-user/user/blacklist',
        params,
    })
}
/**
 * @LastEditors: lexy
 * @description: 拉入或者移除
 */
export const doPutLimitPermission = (userIds: string[], roleList: string[], explain?: string) => {
    return post({
        url: `gruul-mall-uaa/uaa/user/data/update/authority`,
        data: {
            userIds,
            roleList,
            explain,
        },
    })
}
/**
 * @description 获取成长值配置信息
 * @returns { Promise<AxiosResponse<R>> }
 */
export const doGetGrowthValueSettings = () => {
    return get({
        url: 'gruul-mall-user/user/growthValue/settings',
    })
}
/**
 * @description 修改成长值配置信息
 * @returns { Promise<AxiosResponse<R>> }
 */
export const doPostGrowthValueSettings = (data: any) => {
    return post({
        url: 'gruul-mall-user/user/growthValue/settings',
        data,
    })
}
/**
 * @description 调整成长值
 * @param { string } userId 用户id号
 * @param { string } growthValue 被调整的成长值
 * @param { 'INCREASE' | 'REDUCE' } changeType 调整类型
 * @returns { Promise<AxiosResponse<R>> }
 */
export const doPostGrowthValueChange = (userId: string, growthValue: number, changeType: 'INCREASE' | 'REDUCE') => {
    return post({
        url: '/gruul-mall-user/user/growthValue/change',
        data: { userId, growthValue, changeType },
    })
}
/**
 * @description 获取会员记录信息
 * @param params
 * @returns
 */
export const doGetMemberPurchaseList = (params: any = {}) => {
    return get({ url: 'gruul-mall-user/member/purchase/list', params })
}
/**
 * @description 导出会员记录
 * @param data
 * @returns
 */
export const doPostExportMemberPurchaseList = (data: any = {}) => {
    return post({ url: 'gruul-mall-user/member/purchase/export', data })
}
/**
 * @description 获取储值流水列表
 * @param params
 * @returns
 */
export const doGetUserBalanceList = (params: any = {}) => {
    return get({ url: 'gruul-mall-user/user/balance/list', params })
}
/**
 * @description 导出储值流水列表
 * @param data
 * @returns
 */
export const doPostExportUserBalanceList = (data: any = {}) => {
    return post({ url: 'gruul-mall-user/user/balance/export', data })
}
/**
 * @description 批量备注储值流水
 * @param data
 * @returns
 */
export const doPostBatchRemarkUserBalance = (data: any = {}) => {
    return post({ url: 'gruul-mall-user/user/balance/remark', data })
}
