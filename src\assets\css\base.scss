@import "./variable.scss";
@import "./element/var.scss";
@import "./transition";
@import "./layout";
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html,
body,
#app {
    width: 100%;
    height: 100%;
    // font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-light",
    //   "Helvetica Neue", "Hiragino Sans GB", Helvetica, "Microsoft YaHei", Arial,
    //   sans-serif;
    font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial,
        sans-serif;
    font-size: 14px;
    color: #000;
}

/*滚动条基础设置（必须）*/
div::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

/*滚动条的滑块按钮*/
div::-webkit-scrollbar-thumb {
    background-color: rgba(50, 50, 50, 0.2);
    border-radius: 40px;
}

.el-dropdown-menu__item {
    padding: 3px 15px !important;
}
@include b(el-dialog) {
    @include e(title) {
        font-size: 18px;
        font-weight: bold;
        color: #333333;
    }
}
@include b(hover) {
    cursor: pointer;
    &:hover {
        color: #2e99f3;
    }
}
/**
 * 淡入淡出vue动画
 */
.active-enter-from,
.active-leave-to {
    opacity: 0;
}
.active-enter-active,
.active-leave-active {
    transition: opacity 2s;
}
.el-dropdown-menu__item {
    padding: 0;
    height: 37px;
    display: flex;
    justify-content: center;
}
.el-dropdown-menu__item:not(.is-disabled):focus {
    color: #555cfd !important;
    background-color: rgb(85, 92, 253, 0.1) !important;
}
.el-dropdown-menu__item:not(.is-disabled):hover {
    color: #555cfd !important;
    background-color: rgb(85, 92, 253, 0.1) !important;
}
