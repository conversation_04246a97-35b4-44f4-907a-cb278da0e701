@import '../mixins/mixins.scss';
@import 'goods.scss';
@import 'goodGroup.scss';
@import 'information.scss';
@import 'search.scss';

.editorWrapper {
    height: 100%;
    width: 100%;
    min-width: 1280px;
    overflow: hidden;
}

.change_text_box {
    cursor: pointer;
    color: #1296da;
    font-size: 14px;
    height: 64px;
    width: 150px;
    display: flex;
    justify-content: center;
    align-items: center;
    .change_text {
        margin-left: 2px;
    }
}
.wraper_top {
    box-shadow: 0px 4px 9px 0px rgba(199, 199, 199, 0.35);
    height: 62px;
    display: flex;
    position: relative;
    background: #ffffff;
    z-index: 2;

    .editorPage_right_con {
        flex: 1;
        margin-bottom: 10px;
        overflow: hidden;
    }
    .editor_tab_icon1 {
        background: url(https://ma.faisys.com/image/mbg01.png?v=202003041237) -1718px -40px no-repeat;
        width: 19px;
        height: 21px;
        margin-right: 12px;
        margin-top: 0;
    }
    .editor_tab_icon2 {
        background: url(https://ma.faisys.com/image/mbg01.png?v=202003041237) -1754px -40px no-repeat;
        width: 19px;
        height: 21px;
        margin-right: 12px;
        margin-top: 0;
    }
}
.editor_tab_title_top {
    cursor: pointer;
    font-size: 14px;
    width: 126px;
    height: 65px;
    cursor: pointer;
    color: #2e3c45;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    display: flex;
    border-right: 1px solid rgba(237, 241, 246, 1);
}
.dec_footer {
    margin-top: 16px;
    width: 120px;
    position: absolute;
    margin-left: -60px;
    left: 46%;
    button {
        padding: 12px 20px;
    }
}
.editorPage {
    position: relative;
    height: calc(100% - 139px);
    z-index: 1;
    background-color: #f0f0f0;
    .editorPage_left {
        position: absolute;
        left: 0;
        height: 100%;
        margin: 0;
        padding: 0;
        transition: all 0.5s;
        z-index: 1;
        display: flex;
        background: #ffffff;
        .editor_view {
            position: relative;
            width: 339px;
            height: 100%;
        }
    }
    .editorPage_main {
        .tab_con {
            flex: 1;
            display: flex;
            flex-direction: row;
            justify-content: center;
        }
    }
    .editorPage_right {
        position: absolute;
        top: 0;
        right: 0;
        height: 100%;
        padding: 0;
        background-color: #fff;
        z-index: 3;
    }
    &__preview {
        width: 375px;
        height: 667px;
        border: 1px solid #ccc;
        overflow: hidden;
        background-color: #eeeeee;
    }

    &__from {
        width: 435px;

        .banner {
            @mixin b-w {
                width: 390px;
                border: 1px solid #e4e4e4;
            }
            &--upload {
                @include flex;
                @include b-w;

                flex-direction: column;

                height: 100px;

                span {
                    color: #3088f0;
                    cursor: pointer;
                }
                p {
                    font-size: 12px;
                    color: #a7a7a7;
                }
            }
            &--form {
                @include b-w;
                margin-top: 15px;
                padding: 15px 10px 0;
            }
        }
    }

    .component_box {
        display: flex;
        flex-wrap: wrap;
    }

    &__component {
        width: 300px;

        .component--list {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
        }

        .component--item {
            width: 100%;
            height: 130px;
            line-height: 130px;
            border-radius: 4px;
            border: 1px solid #e4e4e4;
            text-align: center;
            margin-bottom: 15px;
            cursor: pointer;
        }

        .el-tabs__nav {
            width: 100%;
            display: flex;
            justify-items: center;
            justify-content: space-between;
            .el-tabs__header {
                background: none;
            }
            .el-tabs__item {
                flex: 1;
                text-align: center;
                background: none;
            }
        }
    }
}
.editor__from {
    width: 435px;
    height: 667px;
    overflow-y: scroll;
    box-sizing: border-box;
    padding-bottom: 50px;
    .banner {
        @mixin b-w {
            width: 390px;
            border: 1px solid #e4e4e4;
        }
        &--upload {
            @include flex;
            @include b-w;
            flex-direction: column;
            height: 100px;
            span {
                color: #3088f0;
                cursor: pointer;
            }
            p {
                font-size: 12px;
                color: #a7a7a7;
            }
        }
        &--form {
            @include b-w;
            margin-top: 15px;
            padding: 15px 10px 0;
        }
    }
}

.editor__component {
    width: 100%;
    height: calc(100vh - 70px);
    overflow: auto;
    color: #333;
    box-sizing: border-box;
    padding-bottom: 50px;
    .component--list {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
    }
    .component--item {
        width: 93px;
        height: 93px;
        border-radius: 3px;
        text-align: center;
        cursor: pointer;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: #686867;

        img {
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
        }

        span {
            width: 100%;
            text-align: center;
            font-size: 14px;
        }
    }
    .component--item:hover {
        box-shadow: 0px 0px 13px 0px rgba(64, 158, 255, 0.14);
    }
    .el-tabs__nav {
        width: 100%;
        display: flex;
        justify-items: center;
        justify-content: space-between;
        .el-tabs__header {
            background: none;
        }
        .el-tabs__item {
            flex: 1;
            text-align: center;
            background: none;
        }
    }
}

.editor_tab {
    height: 100%;
    width: 75px;
    display: flex;
    flex-direction: column;
    background: #403f3e;
    .editor_tab_title {
        background: #403f3e;
        width: 75px;
        height: 65px;
    }
    .left_menu {
        width: 75px;
        background: #403f3e;
        flex: 1;
        position: relative;

        .slider—span {
            position: absolute;
            top: 0;
            right: 0;
            width: 0;
            height: 0;
            border: 7px solid transparent;
            border-right-color: #fff;
            -webkit-transition: top 0.3s;
            transition: top 0.3s;
        }
    }
    .tab_item {
        width: 75px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        height: 96px;
        text-align: center;
        color: #a6a6a6;
        .tab--icon {
            width: 32px;
            height: 32px;
            color: #a6a6a6;
            text-align: center;
        }

        .tab_item_title {
            color: #a6a6a6;
            font-size: 14px;
            margin-top: 3px;
        }

        .tab-bg__icon1 {
            background-position: -144px 1px;
        }

        .tab-bg__icon2 {
            background-position: -145px -42px;
        }

        .tab-bg__icon3 {
            background-position: -144px -87px;
        }
    }

    .tab-bg__icon {
        display: block;
        width: 24px;
        height: 24px;
        margin: 30px auto 12px;
        background: url(https://ma.faisys.com/image/mbg01.png?v=202003041237) no-repeat;
        position: relative;
    }

    .tab_item_active {
        position: relative;
        background: #ffffff;
        &::after {
            content: '';
            width: 10px;
            height: 10px;
            background: #403f3e;
            position: absolute;
            top: -10px;
            right: 0;
            border-radius: 0 0 10px 0;
        }
        &::before {
            content: '';
            width: 10px;
            height: 10px;
            background: #fff;
            position: absolute;
            top: -10px;
            right: 0;
        }
        .tab--icon {
            font-size: 35px;
            color: #409eff;
            text-align: center;
        }
        .tab_item_title {
            color: #409eff;
        }

        .tab-bg__icon1 {
            background-position: -99px 1px;
        }

        .tab-bg__icon2 {
            background-position: -100px -42px;
        }

        .tab-bg__icon3 {
            background-position: -99px -87px;
        }

        .decorator {
            &::before {
                content: '';
                width: 10px;
                height: 10px;
                background: #fff;
                position: absolute;
                bottom: -10px;
                right: 0;
            }
            &::after {
                content: '';
                width: 10px;
                height: 10px;
                background: #403f3e;
                position: absolute;
                bottom: -10px;
                right: 0;
                border-radius: 0 10px 0 0;
            }
        }
    }
}

.editor__preview {
    border-radius: 10px;
    position: relative;
    width: 375px;
    height: calc(100vh - 150px) !important;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
    background-color: #fff; // #eeeeee
    box-sizing: border-box;
    .preNavBar {
        width: 373px;
        position: absolute;
        bottom: 0;
        left: 0;
    }
    .el-scrollbar__view {
        div[group='custom'] {
            min-height: 300px;
        }
    }
    .component--item {
        position: relative;
        border: 1px dotted #fff;
        box-sizing: border-box;
    }

    .iscur__component--item {
        border: 1.5px dashed red;
    }
    .select__component--item {
        border: 1.5px dashed rgba(45, 140, 240, 0.7);
    }
    .component--item__icon {
        width: 30%;
        text-align: center;
        cursor: pointer;
    }
    .component--item__icon:hover {
        color: #409eff;
    }
    .component--item__tan {
        position: absolute;
        right: 7px;
        top: 0px;
        z-index: 999;
        font-size: 14px;
        width: 106px;
        height: 39px;
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0px 0px 13px 0px rgba(0, 0, 0, 0.14);
        display: flex;
        justify-content: center;
        align-items: center;
        color: #686868;
    }
    .component--item__text {
        width: 40%;
        text-align: center;
        cursor: pointer;
    }
    .component--item__text:hover {
        color: #409eff;
    }

    .component--item__draghelp {
        width: 30px;
        height: 30px;
        position: absolute;
        right: 0;
        top: 0;
        border-radius: 0 0 0 30px;
        cursor: move;
        background-color: #dddee2;
        z-index: 999;
        i {
            font-size: 20px;
            color: #fff;
            position: absolute;
            top: 4px;
            right: 1px;
        }
    }
}

.editor__component_new {
    height: 100% !important;
    padding-bottom: 0 !important;
    display: flex;
    flex-direction: column;
    .el-tabs__content {
        display: none;
    }
    .el-tabs--border-card {
        border: none !important;
        box-shadow: none !important;
    }
    .editor_component_top {
        width: 100%;
        height: 100px;
        display: flex;
        justify-content: center;
        align-items: center;
        .top_button {
            width: 86.4px;
            height: 33px;
            border-radius: 16.5px;
            color: #333333;
            background-color: #f5f5f5;
            margin: 0 14px;
            text-align: center;
            line-height: 33px;
            font-size: 14px;
        }
        .top_button_change {
            color: #ffffff;
            background-color: #409eff;
        }
    }
    .editor_component_wrap {
        flex: 1;
        overflow: hidden;
    }
    .editor_component_wrap_main {
        width: 100%;
        padding: 0 30px;
        .component_title {
            width: 100%;
            height: 36px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-bottom: 10rpx;
            .jiantou_bot {
                width: 0;
                height: 0;
                border: 5px solid transparent;
                border-top-color: #a2a2a2;
                margin-top: 5px;
            }
            .jiantou_right {
                width: 0;
                height: 0;
                border: 5px solid transparent;
                border-left-color: #a2a2a2;
            }
            .component_title_text {
                margin-left: 5px;
                font-size: 14px;
                color: #a2a2a2;
            }
        }
    }
}
