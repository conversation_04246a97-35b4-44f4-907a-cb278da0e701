.app_main {
    // width: calc(100% - 180px - 12px);
    display: flex;
    flex-direction: column;
    height: 100%;
    flex: 1;
    .admin__main--content {
        min-height: calc(100% - 40px);
        background: white !important;
        z-index: 10;
        display: flex;
        flex-direction: column;
        overflow: scroll;
        &.no--padding {
            padding: 0;
        }
    }
    .grey_bar {
        width: 100%;
        height: 12px;
        flex-shrink: 0;
        background: $bg-grey;
    }
    .tab_container {
        background: white;
        padding-left: 16px;
        padding-right: 16px;
    }
    .handle_container {
        padding-left: 16px;
        padding-right: 16px;
        padding-bottom: 16px;
    }
    &:has(.handle_container + .table_container),
    &:has(.handle_container + .m__table--container) {
        .handle_container {
            padding-bottom: 0;
        }
    }
    .m__table--container {
        border-top: 16px solid transparent;
        border-left: 16px solid transparent;
        border-right: 16px solid transparent;
        overflow: auto;
        transition: height 0.5s;
    }
    .table_container {
        padding-left: 16px;
        padding-right: 16px;
        border-top: 16px solid transparent;
        // table滚动方案1
        flex: 1;
        overflow-y: scroll;
        position: relative;
        .el-table {
            height: 100%;
        }
        &:has(> .el-table) {
            overflow: hidden;
        }
        // table滚动方案2
        // overflow: auto;
        // border-top: 16px solid transparent;
        // flex: 1;
        // .el-table {
        //     overflow: visible !important;
        //     .el-scrollbar__bar {
        //         display: none;
        //     }
        // }
        // .el-table__header-wrapper {
        //     position: sticky;
        //     top: 0;
        //     left: 0;
        //     z-index: 10;
        // }
    }
    .pagination {
        display: flex;
        justify-content: flex-end;
        margin-top: auto;
        margin-left: auto;
        width: 100%;
        background-color: white;
        padding: 12.23px 13px;
    }
    .content_container {
        padding: 30px;
    }
    .q_plugin_container {
        height: 100%;
        background: white;
        z-index: 10;
        display: flex;
        flex-direction: column;
    }
    .flex {
        display: flex;
    }
    .fcenter {
        display: flex;
        align-items: center;
    }
    .ccenter {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .fdc {
        display: flex;
        flex-direction: column;
    }
    .fdc1 {
        display: flex;
        flex-direction: column;
        flex: 1;
    }
    .f1 {
        flex: 1;
    }
    .df {
        display: flex;
    }
    .overh {
        overflow: hidden;
    }
    .cup {
        cursor: pointer;
    }
}
