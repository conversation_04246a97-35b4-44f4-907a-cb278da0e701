<template>
    <el-image :src="avatar" v-bind="$attrs" :style="{ width: `${props.size}px`, height: `${props.size}px` }" />
</template>

<script lang="ts" setup>
const props = defineProps({
    name: {
        type: String,
        default: '店',
    },
    maxLength: {
        type: Number,
        default: 2,
    },
    size: {
        type: Number,
        default: 100,
    },
})
const emit = defineEmits(['update:name', 'refreshAvatar', 'handleAvatarUrl'])
const getInitials = (name: string) => {
    if (!name) return '店'
    return name.substring(0, props.maxLength).toUpperCase()
}

const avatar = computed(() => refreshAvatar())

const refreshAvatar = () => {
    const cutName = getInitials(props.name)
    const url = getAvatarUrl(cutName, props.size)
    emit('handleAvatarUrl', url)
    return url
}

// 预定义柔和的颜色范围
const colorRanges = [
    { hue: [10, 40], sat: [35, 45], light: [80, 90] }, // 暖橙色系
    { hue: [180, 220], sat: [35, 45], light: [80, 90] }, // 青蓝色系
    { hue: [280, 320], sat: [35, 45], light: [80, 90] }, // 紫色系
    { hue: [140, 160], sat: [35, 45], light: [80, 90] }, // 绿色系
    { hue: [45, 65], sat: [35, 45], light: [80, 90] }, // 黄色系
]

const getRandomInRange = (min: number, max: number) => {
    return Math.floor(Math.random() * (max - min + 1)) + min
}

const getAvatarUrl = (text: string, size = 100) => {
    console.log('getAvatarUrl =>', text)
    // 获取文字的第一个字符
    const firstChar = text.charAt(0).toUpperCase()

    // 生成柔和的随机背景色
    // 随机选择一个颜色范围
    const range = colorRanges[Math.floor(Math.random() * colorRanges.length)]

    // 在选定范围内随机生成 HSL 值
    const hue = getRandomInRange(range.hue[0], range.hue[1])
    const saturation = getRandomInRange(range.sat[0], range.sat[1])
    const lightness = getRandomInRange(range.light[0], range.light[1])

    const backgroundColor = `hsl(${hue}, ${saturation}%, ${lightness}%)`

    // 创建 canvas
    const canvas = document.createElement('canvas')
    canvas.width = size
    canvas.height = size
    const context = canvas.getContext('2d')

    if (context) {
        // 绘制背景
        context.fillStyle = backgroundColor
        context.fillRect(0, 0, size, size)

        // 绘制文字
        context.fillStyle = '#333333'
        // 根据文本长度动态调整字体大小
        const fontSize = text.length > 2 ? size * 0.3 : size * 0.4
        context.font = `${fontSize}px Arial`
        context.textAlign = 'center'
        context.textBaseline = 'middle'
        context.fillText(text, size / 2, size / 2)
    }

    return canvas.toDataURL()
}
</script>
<style scoped lang="scss"></style>
