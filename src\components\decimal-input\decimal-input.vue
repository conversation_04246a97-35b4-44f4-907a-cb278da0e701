<template>
  <el-input :model-value="displayValue" @input="handleInput" v-bind="$attrs">
    <template v-for="(_, name) in $slots" #[name]="slotData">
      <slot :name="name" v-bind="slotData" />
    </template>
  </el-input>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'

const props = defineProps<{
  modelValue: string | number | null
  decimalPlaces?: number
  min?: number
  max?: number
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: number | null): void
  (e: 'change', value: number | null): void
}>()

const internalValue = ref('')
const decimalPlaces = computed(() => props.decimalPlaces ?? 2)

const displayValue = computed(() => internalValue.value)

const isValidInput = (value: string): boolean => {
  if (value === '' || value === '-') return true

  const numValue = Number(value)
  if (isNaN(numValue)) return false

  if (props.min !== undefined && numValue < props.min) return false
  if (props.max !== undefined && numValue > props.max) return false

  if (decimalPlaces.value === 0) {
    // 只允许整数
    return /^-?\d*$/.test(value)
  } else {
    // 允许小数
    const regex = new RegExp(`^-?\\d*\\.?\\d{0,${decimalPlaces.value}}$`)
    return regex.test(value)
  }
}

const handleInput = (value: string) => {
  if (isValidInput(value)) {
    internalValue.value = value
    emitValue(value)
  } else {
    // 如果输入无效，回退到上一个有效值
    const el = document.activeElement as HTMLInputElement
    if (el) {
      el.value = internalValue.value
      const cursorPosition = el.selectionStart || 0
      el.setSelectionRange(cursorPosition, cursorPosition)
    }
  }
}

const emitValue = (value: string) => {
  const numValue = value === '' || value === '-' ? null : Number(value)
  if (numValue === null || !isNaN(numValue)) {
    emit('update:modelValue', numValue)
    emit('change', numValue)
  }
}


const clampValue = (value: number): number => {
  if (props.min !== undefined) {
    value = Math.max(props.min, value)
  }
  if (props.max !== undefined) {
    value = Math.min(props.max, value)
  }
  return value
}

watch(() => props.modelValue, (newValue) => {
  if (newValue === null) {
    internalValue.value = ''
  } else if (typeof newValue === 'number' && !isNaN(newValue)) {
    const clampedValue = clampValue(newValue)
    internalValue.value = String(clampedValue)
    if (clampedValue !== newValue) {
      emit('update:modelValue', clampedValue)
    }
  } else if (typeof newValue === 'string' && !isNaN(Number(newValue))) {
    const numValue = Number(newValue)
    const clampedValue = clampValue(numValue)
    internalValue.value = String(clampedValue)
    if (clampedValue !== numValue) {
      emit('update:modelValue', clampedValue)
    }
  } else {
    internalValue.value = ''
  }
}, { immediate: true })

</script>