<template>
    <span class="current" :style="{color: props.color}">
        <span class="symbol">¥</span>
        <template v-if="isMoreThanTenThousand">
            <span class="value"  :style="{'font-size': `${props.unitSize}px`}">{{ tenThousandPrice }}万</span>
        </template>
        <template v-else>
            <span class="integer" :style="{'font-size': `${props.integerSize}px`}">{{ priceInteger }}</span>
            <span v-if="priceDecimal" class="decimal" :style="{'font-size': `${props.decimalSize}px`}">.{{ priceDecimal }}</span>
        </template>
    </span>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import useConvert from '@/composables/useConvert'

const { divTenThousand } = useConvert()

const props = defineProps({
    price: {
        type: String,
        required: true,
    },
    color: {
        type: String,
        default: '#f53f3f'
    },
    unitSize: {
        type: Number,
        default: 12
    },
    integerSize: {
        type: Number,
        default: 20
    },
    decimalSize: {
        type: Number,
        default: 12
    }
})
// 处理价格显示
const currentPrice = computed(() => Number(props.price).toFixed(2))

// 处理价格整数和小数部分
const priceInteger = computed(() => currentPrice.value.split('.')[0])
const priceDecimal = computed(() => currentPrice.value.split('.')[1])

// 处理万元以上价格显示
const isMoreThanTenThousand = computed(() => Number(priceInteger.value) > 10000)
const tenThousandPrice = computed(() => {
    if (!isMoreThanTenThousand.value) return ''
    return divTenThousand(currentPrice.value)
        .toFixed(2)
        ?.replace(/\.?0+$/, '')
})
</script>
<style lang="scss" scoped>
.current {
    color: #f53f3f;
    font-weight: 500;
    line-height: 24px;
    .symbol {
        font-size: 12px;
        margin-right: 2px;
    }
    .integer {
        font-size: 20px;
    }
    .decimal {
        font-size: 16px;
    }
}
</style>
