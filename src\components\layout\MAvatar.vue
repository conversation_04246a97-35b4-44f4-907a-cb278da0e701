<template>
    <el-dropdown
        ref="dropMenu"
        style="margin-left: 8px"
        :disabled="showConfig"
        :hide-on-click="false"
        placement="bottom-start"
        trigger="hover"
        @command="commandHandle"
    >
        <div style="position: relative; top: 2px">
            <QIcon prefix-font-class="cylx" name="cylx-shezhi" size="20px" />
        </div>
        <template #dropdown>
            <el-dropdown-menu style="width: 130px">
                <el-dropdown-item command="setting">
                    <div class="dorp-cell">
                        <span>平台中心</span>
                    </div>
                </el-dropdown-item>
                <el-dropdown-item command="logout">
                    <div class="dorp-cell noborder">
                        <span>退出登录</span>
                        <i class="el-icon-switch-button"></i>
                    </div>
                </el-dropdown-item>
            </el-dropdown-menu>
        </template>
    </el-dropdown>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useAdminInfo } from '@/store/modules/admin'
import { doPostLogout } from '@/apis/sign'

const router = useRouter()
const useAdmin = useAdminInfo()
const showConfig = ref(false)
const dropMenu = ref(null)

const commandHandle = async (command: string) => {
    // 退出登录
    if (command === 'logout') {
        useAdmin.REMOVE_ADMIN_INFO()
        doPostLogout()
        router.push('/sign').then((_) => {})
    }
    // 账号信息
    if (command === 'setting') {
        router.push('/business').then((_) => {})
    }
    if (command === 'index') {
        const appBaseUrl = process.env.VUE_APP_BASEURL
        if (!appBaseUrl) return
        const url = appBaseUrl.replace(/\/api/, '')
        open(`${url}`, '_top')
    }
}
</script>

<style scoped lang="scss">
.dorp-cell {
    width: 100%;
    line-height: 40px;
    text-align: center;
}
.noborder {
    color: #555cfd;
}
:deep(.el-dropdown-menu__item:not(.is-disabled):hover) {
    background: #fff;
    color: #555cfd;
}
</style>
