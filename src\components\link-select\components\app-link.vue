<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-08-04 19:04:16
 * @LastEditors: lexy
 * @LastEditTime: 2022-11-04 13:33:22
-->
<script setup lang="ts">
import { useVModel } from '@vueuse/core'
import type { LinkSelectItem } from '../linkSelectItem'
import type { PropType } from 'vue'
/*
 *variable
 */
const $props = defineProps({
    link: {
        type: Object as PropType<LinkSelectItem>,
        default() {
            return {
                id: null,
                type: null,
                name: '',
                url: '',
                append: '',
            }
        },
    },
})
const $emit = defineEmits(['update:link'])
const linkSelectItem = useVModel($props, 'link', $emit)
const appmodel = ref('')
watch(appmodel, (newVal) => {
    const currentItem = {
        id: 999,
        type: 7,
        name: '小程序',
        url: `${newVal}`,
        append: 'appmodel',
    }
    Object.assign(linkSelectItem.value, currentItem)
})
/*
 *lifeCircle
 */
onMounted(() => {
    if (linkSelectItem.value.type === 7) {
        appmodel.value = linkSelectItem.value.url
    }
})
/*
 *function
 */
</script>

<template>
    <div>
        <span style="color: #9797a1">AppId</span>
        <el-input v-model="appmodel" maxlength="40" placeholder="请输入小程序appId" class="input-with-select" style="width: 180px; margin-left: 20px">
        </el-input>
    </div>
</template>

<style lang="scss" scoped></style>
