<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-08-04 18:39:00
 * @LastEditors: lexy
 * @LastEditTime: 2023-10-26 20:21:55
-->
<script setup lang="ts">
import type { PropType } from 'vue'
import { ElMessage } from 'element-plus'
import { useVModel } from '@vueuse/core'
import { doGetPlatformPages } from '@/apis/decoration'
import { useDecorationStore } from '@/store/modules/decoration'
import type { LinkSelectItem } from '../linkSelectItem'
/*
 *variable
 */
const $props = defineProps({
    link: {
        type: Object as PropType<LinkSelectItem>,
        default() {
            return {
                id: null,
                type: null,
                name: '',
                url: '',
                append: '',
            }
        },
    },
    visible: {
        type: Boolean,
        default: false,
    },
})
const $emit = defineEmits(['update:link'])
const linkSelectItem = useVModel($props, 'link', $emit)
const selectId = ref('')
const loading = ref(false)
const tableData = ref([])
const $decorationStore = useDecorationStore()
const pageConfig = reactive({
    current: 1,
    size: 999,
    type: 'CUSTOMIZED_PAGE',
    templateType: 'PLATFORM',
    businessType: '',
    endpointType: $decorationStore.getEndpointType,
})
watch(
    linkSelectItem,
    (newVal) => {
        selectId.value = newVal.id
    },
    {
        immediate: true,
    },
)
/*
 *lifeCircle
 */
onMounted(() => {
    getPageList()
})
/*
 *function
 */

const handleSelect = () => {
    const currentItem = tableData.value.find((item) => item.id === selectId.value)
    console.log(currentItem)
    Object.assign(linkSelectItem.value, currentItem)
}
const onClear = () => {
    getPageList()
}
async function getPageList() {
    loading.value = true
    const { code, data } = await doGetPlatformPages(pageConfig)
    if (code !== 200) {
        return ElMessage.error('获取自定页面失败')
    }
    const tempArr = data.records.map((item) => {
        const { id, name, shopId } = item
        return {
            id,
            type: 3,
            name,
            url: '/pages/modules/custom/custom',
            append: id,
            shopId,
        }
    })
    tableData.value = tempArr
    loading.value = false
}
</script>

<template>
    <div>
        <el-button @click="onClear">刷新</el-button>
        <el-table v-loading="loading" :data="tableData" height="369">
            <el-table-column label="页面名称" prop="name"></el-table-column>
            <el-table-column label="操作" width="100px">
                <template #default="scope">
                    <el-radio v-model="selectId" :value="scope.row.id" @change="handleSelect">
                        <span></span>
                    </el-radio>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<style lang="scss" scoped></style>
