<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-08-03 16:04:14
 * @LastEditors: lexy
 * @LastEditTime: 2023-10-28 14:17:44
-->
<script setup lang="ts">
import { useVModel } from '@vueuse/core'
import type { LinkSelectItem } from '../linkSelectItem'
import type { PropType } from 'vue'
/*
 *variable
 */
const $props = defineProps({
    link: {
        type: Object as PropType<LinkSelectItem>,
        default() {
            return {
                id: null,
                type: null,
                name: '',
                url: '',
                append: '',
            }
        },
    },
    visible: {
        type: Boolean,
        default: false,
    },
    noProTab: {
        type: Boolean,
        default: false,
    },
    limitProTab: {
        type: Boolean,
        default: false,
    },
})
const $emit = defineEmits(['update:link'])
const linkSelectItem = useVModel($props, 'link', $emit)
const selectId = ref()
let tableData = shallowReactive([
    {
        id: '20',
        type: 0,
        name: 'AI对话',
        url: '/AIPackage/chat-box?chatModel=CHAT',
        append: '',
    },
    {
        id: '27',
        type: 0,
        name: 'AI会话记录',
        url: '/AIPackage/chat-history-page',
        append: '',
    },
    {
        id: '21',
        type: 0,
        name: '品种识别',
        url: '/AIPackage/components/petPhotoguide/petPhotoGuide?chatModel=REG&regModel=BREED_REG',
        append: '',
    },
    {
        id: '22',
        type: 0,
        name: '情绪识别',
        url: '/AIPackage/components/petPhotoguide/petPhotoGuide?chatModel=REG&regModel=EMO_REG',
        append: '',
    },
    {
        id: '23',
        type: 0,
        name: '粪便识别',
        url: '/AIPackage/components/petPhotoguide/petPhotoGuide?chatModel=REG&regModel=SHIT_REG',
        append: '',
    },
    {
        id: '24',
        type: 0,
        name: '皮肤识别',
        url: '/AIPackage/components/petPhotoguide/petPhotoGuide?chatModel=REG&regModel=SKIN_REG',
        append: '',
    },
    {
        id: '25',
        type: 0,
        name: '尿液识别',
        url: '/AIPackage/components/petPhotoguide/petPhotoGuide?chatModel=REG&regModel=URINE_REG',
        append: '',
    },
    {
        id: '26',
        type: 0,
        name: '呕吐物识别',
        url: '/AIPackage/components/petPhotoguide/petPhotoGuide?chatModel=REG&regModel=VOMIT_REG',
        append: '',
    },
])

/*
 *lifeCircle
 */
watch(
    linkSelectItem,
    (newVal) => {
        selectId.value = newVal.id
    },
    { immediate: true },
)

/*
 *function
 */
const handleSelect = () => {
    const currentItem = tableData.find((item) => item.id === selectId.value)
    Object.assign(linkSelectItem.value, currentItem)
}
</script>

<template>
    <el-table :data="tableData" height="369">
        <el-table-column label="页面名称" prop="name"></el-table-column>
        <el-table-column label="操作" width="100px">
            <template #default="scope">
                <el-radio v-model="selectId" :value="scope.row.id" @change="handleSelect">
                    <div></div>
                </el-radio>
            </template>
        </el-table-column>
    </el-table>
</template>

<style lang="scss" scoped>
.search-wrap {
    display: flex;
    justify-content: space-between;
    justify-items: center;
}

.search-wrap-input {
    width: 180px;
}
</style>
