<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-08-04 19:04:16
 * @LastEditors: lexy
 * @LastEditTime: 2022-11-04 13:33:22
-->
<script setup lang="ts">
import { useVModel } from '@vueuse/core'
import type { LinkSelectItem } from '../linkSelectItem'
import type { PropType } from 'vue'
/*
 *variable
 */
const $props = defineProps({
    link: {
        type: Object as PropType<LinkSelectItem>,
        default() {
            return {
                id: null,
                type: null,
                name: '',
                url: '',
                append: '',
            }
        },
    },
})
const $emit = defineEmits(['update:link'])
const linkSelectItem = useVModel($props, 'link', $emit)
const toastmodel = ref('')
watch(toastmodel, (newVal) => {
    console.log('toastmodel:', newVal)
    const currentItem = {
        id: 999,
        type: 8,
        name: '未开放',
        url: `${newVal}`,
        append: '',
    }
    Object.assign(linkSelectItem.value, currentItem)
})
/*
 *lifeCircle
 */
onMounted(() => {
    if (linkSelectItem.value.type === 8) {
        toastmodel.value = linkSelectItem.value.url
    }
})

/*
 *function
 */
</script>

<template>
    <div>
        <span style="color: #9797a1">提示文本</span>
        <el-input
            v-model="toastmodel"
            maxlength="40"
            placeholder="研发中，敬请期待"
            class="input-with-select"
            style="width: 180px; margin-left: 20px"
        />
    </div>
</template>

<style lang="scss" scoped></style>
