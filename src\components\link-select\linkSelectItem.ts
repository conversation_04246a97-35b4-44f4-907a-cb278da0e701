/*
 * @description:
 * @Author: lexy
 * @Date: 2022-12-02 17:18:25
 * @LastEditors: lexy
 * @LastEditTime: 2023-02-03 13:53:38
 */
/**
 * @LastEditors: lexy
 * @description: 链接选择器
 * @param {number | string} id
 * @param {number | string} type 链接类型 0 -功能页面 1-商超商品 Goods 2活动营销 3自定义页面 4店铺主页
 * @param {string} name 链接名称
 * @param {string} url 链接地址
 * @param {number} append 附加参数
 */
export interface LinkSelectItem {
    id: string
    type: number
    name: string
    url: string
    append?: number
    shopId?: string
    productId?: string
}
export const typeNameMap = {
    0: {
        text: '功能页面',
        name: 'FunctionPage',
    },
    1: {
        text: '商品',
        name: 'Goods',
    },
    2: {
        text: '活动营销',
        name: 'ActivityMarket',
    },
    3: {
        text: '自定义页面',
        name: 'CustomPage',
    },
    4: {
        text: 'AI页面',
        name: 'AIPage',
    },
}
// id为字符串适配C端swiper item-id为string
// 此处默认数据勿改 改动需要与C端平台选择组件id对应
export const navBarDefaultData = [
    {
        id: '1',
        type: 0,
        name: '首页',
        url: '/',
        append: 0,
    },
    // {
    //     id: '2',
    //     type: 0,
    //     name: '分类',
    //     url: '/',
    //     append: 1,
    // },
    {
        id: '3',
        type: 0,
        name: '购物车',
        url: '/',
        append: 2,
    },
    {
        id: '4',
        type: 0,
        name: '个人中心',
        url: '/',
        append: 3,
    },
    {
        id: '5',
        type: 0,
        name: '店铺',
        url: '/',
        append: 4,
    },
]
