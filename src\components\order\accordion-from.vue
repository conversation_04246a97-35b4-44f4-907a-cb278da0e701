<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-06-09 18:42:45
 * @LastEditors: lexy
 * @LastEditTime: 2023-07-13 17:42:00
-->
<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import MCard from '@/components/MCard.vue'
import { doGetShopList } from '@/apis/shops'
// /*
//  *variable
//  */
const $emit = defineEmits(['search-data', 'changeShow', 'exportData'])
const ShowMCard = ref(false)
const shopSearchLoading = ref(false)
const shopSearchList = ref<any[]>([])
const props = defineProps({
    show: {
        type: Boolean,
        default: true,
    },
})
const platformList = [
    {
        label: '全部',
        value: '',
    },
    {
        label: '小程序',
        value: 'WECHAT_MINI_APP',
    },
    // {
    //     label: '公众号',
    //     value: 'WECHAT_MP',
    // },
    {
        label: 'H5商城',
        value: 'H5',
    },
    {
        label: 'IOS端',
        value: 'IOS',
    },
    {
        label: '安卓端',
        value: 'ANDROID',
    },
    {
        label: '鸿蒙端',
        value: 'HARMONY',
    },
    // {
    //     label: 'PC商城',
    //     value: 'PC',
    // },
]
// 下拉选择状态初始数据
const searchFromData = reactive({
    orderNo: '', // 订单号
    buyerNickname: '', // 买家名称
    clinchTime: '',
    productName: '', // 商品名称
    receiverName: '', // 收货人姓名
    distributionMode: '', // 配送方式
    shopType: '', // 店铺类型
    shopId: '',
    platform: '',
    supplierName: '', // 供应商名称
})

const defaultTime = computed(() => {
    // 日期取当天，开始时间为00:00:00，结束时间为当前时刻
    const current = new Date()
    const start = new Date(current.getFullYear(), current.getMonth(), current.getDate(), 0, 0, 0)
    const end = new Date(current.getFullYear(), current.getMonth(), current.getDate(), current.getHours(), current.getMinutes(), current.getSeconds())

    return [start, end]
})

/*
 *lifeCircle
 */
watch(
    () => ShowMCard.value,
    (val) => {
        $emit('changeShow', val)
    },
)
/*
 *function
 */
const HandleSearch = () => {
    const { orderNo, buyerNickname, productName, receiverName, distributionMode, shopType, shopId, platform, supplierName } = searchFromData
    const params = {
        orderNo,
        buyerNickname,
        productName,
        receiverName,
        startTime: '',
        endTime: '',
        distributionMode,
        shopType,
        shopId,
        platform,
        supplierName,
    }
    if (Array.isArray(searchFromData.clinchTime)) {
        params.startTime = searchFromData.clinchTime[0]
        params.endTime = searchFromData.clinchTime[1]
    }
    $emit('search-data', params)
}

const handleReset = () => {
    // @ts-ignore
    Object.keys(searchFromData).forEach((key) => (searchFromData[key] = ''))
    HandleSearch()
}
const shopSearchRemote = async (query: string) => {
    shopSearchLoading.value = true
    try {
        if (query) {
            const { data } = await doGetShopList({ name: query, current: 1, size: 999 })
            shopSearchList.value = data?.records || []
        } else {
            shopSearchList.value = []
        }
    } finally {
        shopSearchLoading.value = false
    }
}
const exportData = () => {
    $emit('exportData', searchFromData)
}
</script>

<template>
    <!-- 搜索部分s -->
    <div class="form">
        <MCard v-model="ShowMCard">
            <el-form class="form-flex">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="商品名称" label-width="90px">
                            <el-input v-model="searchFromData.productName" placeholder="请输入商品名称" maxlength="20" clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="买家昵称" label-width="90px">
                            <el-input v-model="searchFromData.buyerNickname" placeholder="请输入买家昵称" maxlength="20" clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="订单号" label-width="90px">
                            <el-input v-model="searchFromData.orderNo" placeholder="请输入订单号" maxlength="40" clearable />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="收货人姓名" label-width="90px">
                            <el-input v-model="searchFromData.receiverName" placeholder="请输入收货人姓名" maxlength="10" clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="成交时间" label-width="90px">
                            <el-date-picker
                                v-model="searchFromData.clinchTime"
                                format="YYYY/MM/DD HH:mm:ss"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                type="datetimerange"
                                start-placeholder="开始时间"
                                end-placeholder="结束时间"
                                clearable
                                :default-value="defaultTime"
                                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                            /> </el-form-item
                    ></el-col>
                    <el-col :span="8">
                        <el-form-item label="配送方式" label-width="90px">
                            <el-select v-model="searchFromData.distributionMode" placeholder="请选择配送方式" clearable>
                                <el-option label="全部" value="" />
                                <el-option value="EXPRESS" label="快递配送" />
                                <el-option value="INTRA_CITY_DISTRIBUTION" label="同城配送" />
                                <el-option value="SHOP_STORE" label="到店自提" />
                                <el-option value="VIRTUAL" label="无需物流" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="店铺类型" label-width="90px">
                            <el-select v-model="searchFromData.shopType" placeholder="请选择店铺类型" clearable>
                                <el-option label="全部" value="" />
                                <el-option label="自营" value="SELF_OWNED" />
                                <el-option label="优选" value="PREFERRED" />
                                <el-option label="普通" value="ORDINARY" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="店铺名称" prop="shopId" label-width="90px">
                            <el-select
                                v-model="searchFromData.shopId"
                                clearable
                                filterable
                                remote
                                reserve-keyword
                                placeholder="请输入店铺名称"
                                :remote-method="shopSearchRemote"
                                :loading="shopSearchLoading"
                            >
                                <el-option v-for="item in shopSearchList" :key="item.id" :label="item.name" :value="item.id" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item v-if="!props.show" label="所属渠道" label-width="90px">
                            <el-select v-model="searchFromData.platform" style="width: 100%" clearable>
                                <el-option v-for="(item, index) in platformList" :key="index" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="供应商名称" label-width="90px">
                            <el-input v-model="searchFromData.supplierName" placeholder="请输入供应商名称" maxlength="10" clearable />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-button class="from_btn" type="primary" round @click="HandleSearch">搜索</el-button>
                <el-button class="from_btn" round @click="handleReset">重置</el-button>
                <el-button type="primary" round @click="exportData">导出</el-button>
            </el-form>
        </MCard>
    </div>
    <!-- 搜索部分e -->
</template>

<style lang="scss" scoped>
@include b(form) {
    background: #f9f9f9;
}
</style>
