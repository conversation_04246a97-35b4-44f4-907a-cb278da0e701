<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-05-14 15:21:21
 * @LastEditors: lexy
 * @LastEditTime: 2023-06-08 14:08:24
-->
<script setup lang="ts">
import { ref, reactive, unref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { platforms } from '@/views/set/components/WechatPay'
import { doEditmerchant, doGetMerchant } from '@/apis/set/WechatPay'
import { useAdminInfo } from '@/store/modules/admin'
import { uuidHandle } from '@/apis/request'
import { elementUploadRequest } from '@/apis/upload'
import type { ApiParameters, PAYTYPE } from '@/views/set/components/WechatPay'
import { useTimeoutFn } from '@vueuse/core'
/*
 *variable
 */
const $props = defineProps({
    platformType: { type: String, default: 'WECHAT' },
})
const formRef = ref<FormInstance>()
const validateFile = (rule: any, value: any, callback: any) => (uploadText.value ? callback() : callback(new Error('请上传支付证书')))
const rules = reactive({
    appid: [{ required: true, message: '请填写Appid', trigger: 'blur' }],
    subjectName: [
        { required: true, message: '请填写商户名称', trigger: 'blur' },
        { max: 20, message: '输入长度20字以内', trigger: 'blur' },
    ],
    platforms: [{ required: true, message: '请选择渠道', trigger: 'change' }],
    mchId: [
        { required: true, message: '请填写商户号', trigger: 'blur' },
        { max: 20, message: '输入长度20字以内', trigger: 'blur' },
    ],
    keyPrivate: [{ required: true, message: '请填写商户密钥', trigger: 'blur' }],
    upload: [{ required: true, validator: validateFile, trigger: 'change' }],
    keyPublic: [{ required: true, message: '请填写支付宝公钥', trigger: 'blur' }],
})
/**
 * @LastEditors: lexy
 * @description:添加/编辑时携带的参数
 * @returns {*}
 */
const params = ref<ApiParameters>({
    subjectName: '',
    appid: '',
    detailsId: '',
    keyCert: '',
    keyPrivate: '',
    keyPublic: '',
    mchId: '',
    payType: $props.platformType as keyof typeof PAYTYPE,
    platforms: [],
})
const listParams = ref<ApiParameters[]>()
const dialogTableVisible = ref(false)
const isEdit = ref(false)
// 已上传提示文本
const uploadText = ref(false)
/*
 *lifeCircle
 */
initMerchant()
/*
 *function
 */
/**
 * @LastEditors: lexy
 * @description: 初始化商户配置信息
 * @returns {*}
 */

const platformsList = ref([])
async function initMerchant() {
    const { code, data } = await doGetMerchant($props.platformType)
    if (code === 200) {
        listParams.value = data
        platformsList.value = data.reduce((pre: string[], item: any) => {
            return [...pre, ...item.platforms]
        }, [])
        return
    }
    ElMessage.error('获取信息失败')
    console.log('listParams.value', listParams.value)
}
const handleSubmit = async () => {
    try {
        await formRef.value!.validate()
        const { code, msg } = await doEditmerchant(unref(params))
        if (code === 200) {
            ElMessage.success(msg || '更新成功')
            dialogTableVisible.value = false
            initMerchant()
        } else {
            ElMessage.error(msg || '更新失败')
        }
    } catch (error) {
        error
    }
}
const handelBeforeClose = async () => {
    try {
        await ElMessageBox.confirm('文本不会保留，确定要关闭吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '关闭',
            type: 'warning',
        })
        await formRef.value!.resetFields()
        useTimeoutFn(() => {
            // 防止画面闪动
            uploadText.value = false
            isEdit.value = false
            params.value = {
                subjectName: '',
                appid: '',
                detailsId: '',
                keyCert: '',
                keyPrivate: '',
                keyPublic: '',
                mchId: '',
                payType: $props.platformType,
                platforms: [],
            }
        }, 500)
        dialogTableVisible.value = false
    } catch (error) {
        error
    }
}
const handelEdit = (row: ApiParameters) => {
    isEdit.value = true
    uploadText.value = true
    params.value = Object.assign({}, row)
    dialogTableVisible.value = true
}
/**
 * @LastEditors: lexy
 * @description: 上传证书
 * @param {*} file
 * @returns {*}
 */
const token = useAdminInfo().getterToken
const TOKEN_TYPE = 'Bearer '
/**
 * @LastEditors: lexy
 * @description: 上传证书请求头参数
 * @returns {*}
 */
function merchantUploadHeaders() {
    return {
        'Shop-Id': 0,
        Authorization: TOKEN_TYPE + token,
        'Device-Id': uuidHandle(),
        'Client-Type': 'PLATFORM_CONSOLE',
        Platform: 'PC',
    }
}
const handleSuccess = (res: any) => {
    if (res.code !== 200) {
        ElMessage.error(res.msg || '上传失败')
        return
    }
    params.value.keyCert = res.data
    uploadText.value = true
    ElMessage.success('上传成功')
}
</script>

<template>
    <div class="handle_container">
        <el-button class="WechatPay-add" type="primary" round @click="dialogTableVisible = true">添加账号</el-button>
    </div>
    <el-table
        empty-text="暂无数据~"
        class="WechatPay-table"
        :data="listParams"
        style="width: 100%"
        size="large"
        :header-cell-style="{ color: '#515151' }"
    >
        <el-table-column prop="channelName" label="账号名称">
            <template #default="{ row }">
                <div>{{ row.subjectName }}</div>
            </template>
        </el-table-column>
        <el-table-column label="操作" align="right" fixed="right">
            <template #default="{ row }">
                <el-button link @click="handelEdit(row)">编辑</el-button>
            </template>
        </el-table-column>
    </el-table>
    <el-dialog v-model="dialogTableVisible" destroy-on-close :title="isEdit ? '编辑' : '添加'" width="30%" :before-close="handelBeforeClose">
        <el-form ref="formRef" :model="params" :label-width="$props.platformType === 'ALIPAY' ? '120px' : '90px'" :rules="rules">
            <el-form-item prop="subjectName" :label="$props.platformType === 'ALIPAY' ? '商户名称' : '账号名称'">
                <el-input v-model="params.subjectName" maxlength="20" />
            </el-form-item>
            <el-form-item prop="platforms" label="选择渠道">
                <el-checkbox-group v-model="params.platforms">
                    <el-checkbox v-for="plat in platforms" :key="plat.key" :disabled="platformsList.includes(plat.key) && !isEdit" :label="plat.key">
                        {{ plat.value }}
                    </el-checkbox>
                </el-checkbox-group>
            </el-form-item>
            <el-form-item prop="appid" label="Appid">
                <el-input v-model="params.appid" maxlength="40" />
            </el-form-item>
            <!-- 支付宝 -->
            <template v-if="$props.platformType === 'ALIPAY'">
                <el-form-item prop="keyPrivate" label="支付宝私钥">
                    <el-input v-model="params.keyPrivate" :rows="2" type="textarea" placeholder="请输入支付宝私钥" />
                </el-form-item>
                <el-form-item prop="keyPublic" label="支付宝公钥">
                    <el-input v-model="params.keyPublic" :rows="2" type="textarea" placeholder="请输入支付宝私钥" />
                </el-form-item>
            </template>
            <!-- 支付宝 -->
            <template v-else>
                <el-form-item prop="mchId" label="商户号">
                    <el-input v-model="params.mchId" maxlength="60" />
                </el-form-item>
                <el-form-item prop="keyPrivate" label="商户密钥">
                    <el-input v-model="params.keyPrivate" />
                </el-form-item>
                <el-form-item prop="upload" label="支付证书">
                    <el-upload
                        ref="upload"
                        :on-success="handleSuccess"
                        :http-request="elementUploadRequest"
                        :show-file-list="false"
                        :on-error="() => ElMessage.error('上传失败')"
                        :headers="merchantUploadHeaders()"
                        :action="`gruul-mall-payment/merchant/upload`"
                        accept=".p12"
                    >
                        <template #trigger>
                            <el-button type="primary" round>上传</el-button>
                        </template>
                    </el-upload>
                    <div class="el-upload__text" :class="{ 'el-upload__text-show': uploadText }">已上传</div>
                </el-form-item>
                <div style="color: #bcbcbc; font-size: 13px; margin-left: 90px">微信商户平台，微信支付API证书(.p12文件格式)</div>
            </template>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handelBeforeClose">取消</el-button>
                <el-button type="primary" @click="handleSubmit">确认</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<style lang="scss" scoped>
@include b(WechatPay-add) {
    font-size: 12px;
    width: 101px;
    height: 36px;
}
@include b(WechatPay-table) {
    margin-top: 10px;
}
@include b(el-upload) {
    @include e(text) {
        margin-left: 10px;
        display: none;
    }
    @include e(text-show) {
        display: block;
        color: #67c23a;
    }
}
</style>
