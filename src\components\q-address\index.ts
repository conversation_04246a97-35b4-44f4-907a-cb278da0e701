/*
 * @description:
 * @Author: lexy
 * @Date: 2022-08-10 11:25:53
 * @LastEditors: lexy
 * @LastEditTime: 2023-02-02 16:07:45
 */
type RegionData = {
    label: string
    value: string
    children?: RegionData[]
}[]
/**
 * @LastEditors: lexy
 * @description: 根据code返回地区名称
 * @param {string} arr code 数组
 * @returns {*}
 */
export function AddressFn(data: RegionData, arr: string[]): string {
    const newArr: string[] = []
    let index = 0
    function code(data, str) {
        const res = data.find((item) => item.value === str)
        if (res) {
            index++
            newArr.push(res.label)
            if (res.children) {
                code(res.children, arr[index])
            }
        }
    }
    code(data, arr[0])
    return newArr.join('')
}
