<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-08-09 10:21:32
 * @LastEditors: lexy
 * @LastEditTime: 2023-05-07 11:20:35
-->
<script setup lang="ts">
import type { PropType } from 'vue'
export type CommondType = Record<'name' | 'label', string>
/*
 *variable
 */
const dropdown = ref()
const $props = defineProps({
    title: {
        type: String,
        default: '审核',
    },
    option: {
        type: Array as PropType<CommondType[]>,
        default() {
            return [{ name: 'mishandle', label: '暂无操作' }]
        },
    },
    bgColor: { type: String, default: '#eaf5fe' },
    color: { type: String, default: '#fff' },
})
const $emit = defineEmits(['leftClick', 'rightClick'])
/*
 *lifeCircle
 */
/*
 *function
 */
defineExpose({ showClick })
function showClick() {
    dropdown.value.handleOpen()
}
</script>

<template>
    <span class="mybtn">
        <span class="mybtn__left" @click="$emit('leftClick')">
            <span class="mybtn__left--text">{{ $props.title }}</span>
        </span>
        |
        <el-dropdown ref="dropdown" class="mybtn__right" trigger="click" @command="$emit('rightClick', $event)">
            <span class="mybtn__right--span">更多设置</span>
            <template #dropdown>
                <el-dropdown-menu>
                    <el-dropdown-item v-for="item in $props.option" :key="item.label" :command="item.label">{{ item.name }}</el-dropdown-item>
                </el-dropdown-menu>
            </template>
        </el-dropdown>
    </span>
</template>

<style scoped lang="scss">
@include b(mybtn) {
    min-width: 86px;
    height: 36px;
    font-size: 12px;
    color: #309af3;
    background: #eaf5fe;
    border-radius: 50px;
    line-height: 36px;
    text-align: center;
    @include flex;
    @include e(left) {
        flex: 1;
        height: 36px;
        background: #eaf5fe;
        border-radius: 50px 0 0 50px;
        &:hover {
            cursor: pointer;
            color: #fff;
            background: #2e99f3;
        }
        &:active {
            color: #fff;
            background: #79bbff;
        }
        @include m(text) {
            padding: 0 5px;
        }
    }
    @include e(right) {
        margin-left: 3px;
        padding-right: 6px;
        height: 36px;
        line-height: 36px;
        font-size: 12px;
        color: #2e99f3;
        background: #eaf5fe;
        border-radius: 0 50px 50px 0;
        &:hover {
            color: #fff;
            cursor: pointer;
            background: #2e99f3;
        }
        &:active {
            color: #fff;
            background: #79bbff;
        }
        @include m(span) {
            display: block;
            text-align: center;
            /* 
            &:hover {
                color: #fff;
                cursor: pointer;
            } */
        }
    }
}
</style>
