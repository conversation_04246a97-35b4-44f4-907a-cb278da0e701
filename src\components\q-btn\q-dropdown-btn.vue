<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-10-12 13:18:36
 * @LastEditors: lexy
 * @LastEditTime: 2023-05-07 11:04:32
-->
<script setup lang="ts">
import type { PropType } from 'vue'

export type CommondType = Record<'name' | 'label', string>
/*
 *variable
 */
const dropdown = ref()
const $emit = defineEmits(['rightClick', 'leftClick'])
const dropdownVisible = ref(false)
const $props = defineProps({
    title: {
        type: String,
        default: '审核',
    },
    option: {
        type: Array as PropType<CommondType[]>,
        default() {
            return [{ label: '暂无操作', name: 'default' }]
        },
    },
    bgColor: { type: String, default: '#eaf5fe' },
    color: { type: String, default: '#fff' },
    size: { type: String as PropType<'large ' | 'default' | 'small'>, default: 'large' },
    leftDisabled: {
        type: Boolean,
        default: false,
    },
    slotIndex: {
        type: Number,
        default: -1,
    },
})

const computedSlot = computed(() => ($props.slotIndex === -1 ? $props.option.length : $props.slotIndex))
/*
 *lifeCircle
 */
/*
 *function
 */
const handleDropdownChange = () => {
    dropdownVisible.value ? dropdown.value.handleClose() : dropdown.value.handleOpen()
}
</script>

<template>
    <el-button-group :size="$props.size">
        <el-button type="primary" class="base-btn add-element" plain round @click="$emit('leftClick')">{{ $props.title }} </el-button>
        <el-dropdown
            ref="dropdown"
            class="mybtn__right"
            trigger="click"
            @command="$emit('rightClick', $event)"
            @visible-change="dropdownVisible = $event"
        >
            <el-button type="primary" class="base-btn" plain round>
                <span class="base-dropdown" @click="handleDropdownChange">更多设置</span>
            </el-button>
            <template #dropdown>
                <el-dropdown-menu>
                    <el-dropdown-item v-for="item in $props.option.slice(0, computedSlot)" :key="item.label" :command="item.name"
                        >{{ item.label }}
                    </el-dropdown-item>
                    <slot />
                    <el-dropdown-item v-for="item in $props.option.slice(computedSlot, $props.option.length)" :key="item.label" :command="item.name"
                        >{{ item.label }}
                    </el-dropdown-item>
                </el-dropdown-menu>
            </template>
        </el-dropdown>
    </el-button-group>
</template>

<style scoped lang="scss">
@include b(base-btn) {
    border-color: #ffff;
}

@include b(add-element) {
    position: relative;
    &::after {
        content: '|';
        position: absolute;
        right: 0;
        color: #409eff !important;
    }
    &:hover::after {
        color: #409eff !important;
        opacity: 0;
    }
}

@include b(base-dropdown) {
    cursor: pointer;
}
</style>
