<script setup lang="ts">
import type { PropType } from 'vue'
import BetterPageManage from '@/components/BetterPageManage/BetterPageManage.vue'
import { ElMessage } from 'element-plus'
import { doGetHighestCategoryLevel, doGetProductSkus } from '@/apis/good'

import { Search, Check } from '@element-plus/icons-vue'
import type { ApiGoodsRetrieve, GoodsListItem } from '@/components/q-choose-goods-popup/types'
type categoryItme = { id: string; name: string; productNum: string }

const $props = defineProps({
    pointGoodsList: {
        type: Array as PropType<GoodsListItem[]>,
        default() {
            return []
        },
    },
    goodsVisible: {
        type: Boolean,
        default: false,
    },
    shopId: {
        type: String,
        default: '',
    },
})
const { divTenThousand, mulTenThousand } = useConvert()
const pageConfig = reactive({
    size: 10,
    current: 1,
    total: 0,
})
// 弹框商品
const goodsList = ref<ApiGoodsRetrieve[]>([])
// 选择的商品列表
const tempGoods = ref<ApiGoodsRetrieve[]>([])
// 分类数组
const categoryList = ref<categoryItme[]>([])
// 分类选中值
const categoryVal = ref('')
const allChecked = ref(false)
const search = shallowReactive({
    maxPrice: '',
    minPrice: '',
    salePrice: '',
    keyword: '',
    categoryFirstId: '',
})
defineExpose({
    tempGoods,
    search,
    goodsList,
})
// 样式选择
const borderStyle = {
    borderGet: '2px solid #2D8CF0',
    borderNoGet: '2px solid #f2f2f2',
}

onMounted(() => {
    initCategoryList()
})

/**
 * 选择分类
 */
const handleSelectCateItem = (item: categoryItme) => {
    search.categoryFirstId = item.id
    retrieveCommodity()
}
const handleSearchByInput = () => {
    retrieveCommodity()
}
/**
 * 选择商品
 */
const handleChooseGood = (item: ApiGoodsRetrieve) => {
    item.isCheck = !item.isCheck
    const tempGoodsVal = tempGoods.value
    if (item.isCheck) {
        tempGoodsVal.push(item)
    } else {
        const idx = tempGoodsVal.findIndex((i) => i.productId === item.productId)
        if (idx !== -1) {
            tempGoodsVal.splice(idx, 1)
        }
    }
}
/**
 * 全选
 */
const handleGetAll = () => {
    const goodsListval = goodsList.value
    const tempGoodsval = tempGoods.value
    const allCheckedval = allChecked.value
    goodsListval.map((item) => {
        if (allCheckedval) {
            if (!tempGoodsval.find((t) => t.productId === item.productId)) {
                tempGoodsval.push(item)
            }
        }
        return (item.isCheck = allCheckedval)
    })
    if (!allCheckedval) {
        goodsListval.forEach((t) => {
            const idx = tempGoodsval.findIndex((i) => i.productId === t.productId)
            if (idx !== -1) {
                tempGoodsval.splice(idx, 1)
            }
        })
    }
}
const handleSizeChange = (val: number) => {
    pageConfig.current = 1
    pageConfig.size = val
    retrieveCommodity()
}
const handleCurrentChange = (val: number) => {
    pageConfig.current = val
    allChecked.value = false
    retrieveCommodity()
}
async function resetDate() {
    search.categoryFirstId = ''
    search.keyword = ''
    search.maxPrice = ''
    search.minPrice = ''
    search.salePrice = ''
    pageConfig.current = 1
    pageConfig.size = 10
    pageConfig.total = 0
    allChecked.value = false
}
/**
 * 编辑是获取已选择过的数据
 */
async function dealPointList() {
    let checkAll = true
    goodsList.value.forEach((item) => {
        const flag = checkIsSelected(item.productId)
        item.isCheck = flag
        checkAll = checkAll ? flag : checkAll
    })
    allChecked.value = checkAll
}
/**
 * 初始化分类列表
 */
async function initCategoryList() {
    const { code, data } = await doGetHighestCategoryLevel({ size: 1000 })
    if (code === 200) {
        categoryList.value = data.records
    } else {
        ElMessage.error('获取分类列表失败')
    }
}
/**
 * 检索商品列表
 */
async function retrieveCommodity() {
    if (search.minPrice && search.maxPrice) {
        search.salePrice = `${mulTenThousand(search.minPrice).toString()}_${mulTenThousand(search.maxPrice).toString()}`
    }
    if (!search.minPrice && search.maxPrice) {
        search.salePrice = `_${mulTenThousand(search.maxPrice).toString()}`
    }
    if (!search.maxPrice && search.minPrice) {
        search.salePrice = `${mulTenThousand(search.minPrice).toString()}_`
    }
    if (!search.maxPrice && !search.minPrice) {
        search.salePrice = ''
    }
    const { code, data } = await doGetProductSkus({
        name: search.keyword,
        salePrice: search.salePrice.toString(),
        size: pageConfig.size,
        current: pageConfig.current,
        shopId: $props.shopId,
    })
    if (code === 200) {
        let checkAll = true
        let pointGoodsListArr: string[] = []
        if ($props.pointGoodsList.length) {
            // 编辑前选中的商品 取出productId去重
            const set = new Set($props.pointGoodsList.map((item) => item.productId))
            pointGoodsListArr = Array.from(set)
        }
        data.records.forEach((item: ApiGoodsRetrieve) => {
            // 检测当前商品是否已经在选择过的列表中 或者编辑前已选中
            const flag = checkIsSelected(item.productId) || pointGoodsListArr.includes(item.productId)
            if (flag) {
                // 如果此商品默认选中 或 已选中 添加到选中列表
                handleChooseGood(item)
            }
            item.isCheck = flag
            checkAll = checkAll ? flag : checkAll
        })
        allChecked.value = checkAll
        goodsList.value = data.records
        pageConfig.total = data.total
    } else {
        ElMessage.error('获取商品失败')
    }
}

/**
 * 检测当前商品是否已经在选择过的列表中
 */
function checkIsSelected(id: string) {
    return tempGoods.value.findIndex((i) => i.productId === id) !== -1
}
const getGoodMainPics = (picStrs: string) => {
    if (!picStrs) {
        return []
    }
    const picArr = picStrs.split(',')
    return picArr
}
</script>

<template>
    <div class="title">选择商品</div>
    <div class="digGoods">
        <div class="digGoods__box">
            <div class="digGoods__box--top">
                <el-select v-model="categoryVal" style="width: 120px" placeholder="全部分类">
                    <el-option-group v-for="group in categoryList" :key="group.id">
                        <el-option :label="group.name" :value="group.name" @click="handleSelectCateItem(group)" />
                    </el-option-group>
                </el-select>
                <div>
                    <span style="margin: 0px 10px 0px 25px; color: #a1a1a1; width: 60px; line-height: 32px">价格</span>
                    <el-input v-model="search.minPrice" style="width: 60px" maxlength="20"></el-input>
                    <span style="margin: 0px 5px; line-height: 32px">-</span>
                    <el-input v-model="search.maxPrice" style="width: 60px" maxlength="20"></el-input>
                </div>
                <el-input
                    v-model="search.keyword"
                    placeholder="请输入关键词"
                    class="input-with-select"
                    maxlength="20"
                    style="width: 200px; margin-left: 10px"
                    @keypress.enter="handleSearchByInput"
                >
                    <template #append>
                        <el-button :icon="Search" @click="handleSearchByInput" />
                    </template>
                </el-input>
            </div>
            <div v-if="goodsList.length > 0" class="digGoods__box--content">
                <div
                    v-for="(item, index) in goodsList"
                    :key="index"
                    class="digGoods__box--content--good"
                    :style="{
                        border: item.isCheck ? borderStyle.borderGet : borderStyle.borderNoGet,
                    }"
                    @click="handleChooseGood(item)"
                >
                    <el-image
                        class="digGoods__box--content--good--img"
                        :src="getGoodMainPics(item.productPic)[0]"
                        :preview-src-list="getGoodMainPics(item.productPic)"
                    />
                    <div v-if="item.isCheck" class="digGoods__box--content--good--imgShadow">
                        <el-icon color="#fff" size="40px"><Check /></el-icon>
                    </div>
                    <div class="digGoods__box--content--good--shopName">
                        <div class="digGoods__box--content--good--shopName--name">{{ item.productName }}</div>
                        <div class="digGoods__box--content--good--shopName--price">
                            <span>￥{{ item.highestPrice && divTenThousand(item.highestPrice) }}</span>
                            ~
                            <span>￥{{ item.lowestPrice && divTenThousand(item.lowestPrice) }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <div
                v-if="goodsList.length === 0"
                class="digGoods__box--content"
                style="display: flex; justify-content: center; align-items: center; height: 250px"
            >
                暂无相关商品信息，请选择其他分类
            </div>
            <div class="bottom">
                <el-checkbox v-model="allChecked">全选</el-checkbox>
                <BetterPageManage
                    :page-num="pageConfig.current"
                    :page-size="pageConfig.size"
                    :total="pageConfig.total"
                    @reload="retrieveCommodity"
                    @handle-size-change="handleSizeChange"
                    @handle-current-change="handleCurrentChange"
                />
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.title {
    font-size: 15px;
    font-weight: bold;
    display: flex;
    margin-bottom: 20px;
    margin-top: -40px;
}

@include b(digGoods) {
    border-top: 1px solid #d7d7d7;
    padding-top: 10px;
    @include e(box) {
        background-color: #f2f2f2;
        padding: 10px;
        @include m(top) {
            display: flex;
            justify-content: space-between;
        }
        @include m(content) {
            margin-top: 10px;
            background-color: white;
            border-radius: 5px;
            display: flex;
            flex-wrap: wrap;
            padding: 5px;
            @include m(good) {
                width: 33%;
                margin-left: 2px;
                margin-bottom: 4px;
                height: 80px;
                position: relative;
                border-radius: 5px;
                padding: 5px;
                display: flex;
                @include m(img) {
                    width: 65px;
                    height: 65px;
                    position: relative;
                }
                @include m(imgShadow) {
                    width: 65px;
                    height: 65px;
                    position: absolute;
                    background-color: rgba(0, 0, 0, 0.6);
                    @include flex(center, center);
                }
                @include m(shopName) {
                    @include flex;
                    flex-direction: column;
                    justify-content: space-between;
                    align-items: flex-start;
                    margin-left: 10px;
                    padding: 5px;
                    font-size: 12px;
                    @include m(name) {
                        @include utils-ellipsis(2);
                    }
                    @include m(price) {
                        @include utils-ellipsis;
                    }
                }
            }
        }
    }
}
@include b(bottom) {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    margin-top: 10px;
    padding: 0 10px;
}
@include b(bottom-container) {
    @include flex;
    flex-direction: column;
}
</style>
