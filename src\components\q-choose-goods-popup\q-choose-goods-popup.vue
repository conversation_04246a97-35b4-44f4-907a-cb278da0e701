<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-03-01 17:15:17
 * @LastEditors: lexy
 * @LastEditTime: 2023-03-01 17:33:29
-->
<script setup lang="ts">
import ChooseGoodsPopup from '@/components/q-choose-goods-popup/chooseGoods.vue'
import { ref } from 'vue'
import { useVModel } from '@vueuse/core'
import { ElMessage } from 'element-plus'
/*
 *variable
 */
const props = defineProps({
    modelValue: {
        type: Boolean,
        default() {
            return false
        },
    },
})
const emit = defineEmits(['update:modelValue', 'onConfirm'])
const _isShow = useVModel(props, 'modelValue', emit)
const goodsData = ref([])
const chooseGoodsPopupRef = ref()
/*
 *lifeCircle
 */
/*
 *function
 */
const handleClose = () => {
    _isShow.value = false
}
const handleConfirm = () => {
    const tempGoods = chooseGoodsPopupRef.value.tempGoods
    const goodsList = chooseGoodsPopupRef.value.goodsList
    if (tempGoods.length === 0) {
        return ElMessage.error('请选择商品')
    }
    emit('onConfirm', { tempGoods, goodsList })
    _isShow.value = false
}
</script>

<template>
    <!-- 选择商品弹出 s-->
    <el-dialog v-model="_isShow" width="800px" :before-close="handleClose">
        <choose-goods-popup ref="chooseGoodsPopupRef" :point-goods-list="goodsData" />
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="_isShow = false">取消</el-button>
                <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
            </span>
        </template>
    </el-dialog>
    <!-- 选择商品弹出 e-->
</template>

<style scoped></style>
