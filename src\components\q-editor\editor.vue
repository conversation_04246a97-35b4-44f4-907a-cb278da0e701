<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-09-15 11:22:00
 * @LastEditors: lexy
 * @LastEditTime: 2023-02-20 17:38:57
-->
<script lang="ts" setup>
import { useVModel } from '@vueuse/core'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import '@wangeditor/editor/dist/css/style.css'
import type { IEditorConfig, SlateElement } from '@wangeditor/editor'
import { uploadFile } from './upload'
type VideoElement = SlateElement & {
    src: string
    poster?: string
}
type InsertFnType = (url: string, alt: string, href: string) => void
type ImageElement = SlateElement & {
    src: string
    alt: string
    url: string
    href: string
}
/*
 *variable
 */
const $props = defineProps({
    content: {
        type: String,
        default: '',
    },
})
console.log($props)

const emit = defineEmits(['update:content'])
const _content = useVModel($props, 'content', emit)

/*
 *variable
 */
const mode = 'simple'
const editorRef = shallowRef()
const mediaInsertionQueue = ref<number>(0)
const mediaUploadQueue = ref<{ type: 'image' | 'video'; url: string }[]>([])
const baseUrl = import.meta.env.VITE_BASE_URL
const editorConfig: Partial<IEditorConfig> = {
    MENU_CONF: {
        insertImage: {
            onInsertedImage(imageNode: ImageElement | null) {
                if (imageNode === null) return
                const { src, alt, url, href } = imageNode
            },
            checkImage: customCheckImageFn, // 也支持 async 函数
            parseImageSrc: customParseImageSrc, // 也支持 async 函数
        },
        editImage: {
            onUpdatedImage(imageNode: ImageElement | null) {
                // TS 语法
                // onUpdatedImage(imageNode) {                    // JS 语法
                if (imageNode === null) return

                const { src, alt, url } = imageNode
                console.log('updated image', src, alt, url)
            },
            checkImage: customCheckImageFn, // 也支持 async 函数
            parseImageSrc: customParseImageSrc, // 也支持 async 函数
        },
        uploadImage: {
            server: `${baseUrl}gruul-mall-carrier-pigeon/oss/upload`,
            // form-data fieldName ，默认值 'wangeditor-uploaded-image'
            fieldName: 'file',
            // 单个文件的最大体积限制，默认为 2M
            maxFileSize: 1 * 1024 * 1024, // 1M
            // 最多可上传几个文件，默认为 100
            maxNumberOfFiles: 10,
            // 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
            allowedFileTypes: [],
            // 自定义上传参数，例如传递验证的 token 等。参数会被添加到 formData 中，一起上传到服务端。
            meta: {},
            // 将 meta 拼接到 url 参数中，默认 false
            metaWithUrl: false,
            // 自定义增加 http  header
            headers: {},
            // 跨域是否传递 cookie ，默认为 false
            withCredentials: true,
            // 超时时间，默认为 10 秒
            timeout: 5 * 1000, // 5 秒
            // 自定义上传
            async customUpload(file: File, insertFn: InsertFnType) {
                mediaInsertionQueue.value++
                uploadFile('gruul-mall-carrier-pigeon/oss/upload', file)
                    .then((res) => {
                        mediaUploadQueue.value.push({ type: 'image', url: res })
                        nextTick(() => {
                            mediaInsertionQueue.value--
                            checkLastMediaInserted()
                        })
                    })
                    .catch((error) => {
                        console.error('Upload failed:', error)
                        mediaInsertionQueue.value--
                    })
            },
        },
        insertVideo: {
            onInsertedVideo(videoNode: VideoElement | null) {
                if (videoNode === null) return
                const { src } = videoNode
                console.log('inserted video', src)
            },
            checkVideo: customCheckVideoFn, // 也支持 async 函数
            parseVideoSrc: customParseVideoSrc, // 也支持 async 函数
        },
        uploadVideo: {
            // 自定义上传
            async customUpload(file: File, insertFn: InsertFnType) {
                mediaInsertionQueue.value++
                uploadFile('gruul-mall-carrier-pigeon/oss/upload', file).then((res) => {
                    mediaUploadQueue.value.push({ type: 'video', url: res })
                    nextTick(() => {
                        mediaInsertionQueue.value--
                        checkLastMediaInserted()
                    })
                })
            },
            // 自定义上传
            // file 即选中的文件
            // 自己实现上传，并得到视频 url poster
            // 最后插入视频
        },
    },
}

/*
 *lifeCircle
 */

// 组件销毁时，也及时销毁编辑器，重要！
onBeforeUnmount(() => {
    if (editorRef.value === null) return
    editorRef.value.destroy()
})
/*
 *function
 */
// 校验是否最后一个插入
function checkLastMediaInserted() {
    if (mediaInsertionQueue.value === 0 && mediaUploadQueue.value.length > 0) {
        insertMediaInOrder()
        scrollToLastInsertedPosition()
    }
}
// 按顺序插入上传完毕的媒体文件
function insertMediaInOrder() {
    if (!editorRef.value) return

    const editor = editorRef.value

    // 移除开头插入空段落的操作
    // editor.insertNode({ type: 'paragraph', children: [{ text: '' }] })

    while (mediaUploadQueue.value.length > 0) {
        const media = mediaUploadQueue.value.shift()
        if (media) {
            if (media.type === 'image') {
                editor.insertNode({
                    type: 'image',
                    src: media.url,
                    alt: '',
                    children: [{ text: '' }],
                })
            } else if (media.type === 'video') {
                editor.insertNode({
                    type: 'video',
                    src: media.url,
                    children: [{ text: '' }],
                })
            }
            // 只在非最后一个媒体元素后插入空段落
            if (mediaUploadQueue.value.length > 0) {
                editor.insertNode({ type: 'paragraph', children: [{ text: '' }] })
            }
        }
    }

    // 移动光标到最后插入的段落
    moveSelectionToEnd(editor)
}
// 移动到最后
function moveSelectionToEnd(editor) {
    if (!editor) return
    const lastIndex = editor.children.length - 1
    const lastNode = editor.children[lastIndex]

    if (lastNode && lastNode.type === 'paragraph') {
        editor.select({
            anchor: { path: [lastIndex, 0], offset: 0 },
            focus: { path: [lastIndex, 0], offset: 0 },
        })
    }
}
// 滚动到上传结束后的光标处
function scrollToLastInsertedPosition() {
    if (!editorRef.value) return

    const editor = editorRef.value

    // 使用 nextTick 确保 DOM 已更新
    nextTick(() => {
        // 将光标移动到最后一个段落
        moveSelectionToEnd(editor)

        // 尝试使用编辑器的 focus 方法
        editor.focus()

        // 如果编辑器有 scrollToSelection 方法，尝试使用它
        if (typeof editor.scrollToSelection === 'function') {
            editor.scrollToSelection()
        }

        // 额外的滚动尝试
        const editorElement = editor.getEditableContainer()
        if (editorElement) {
            const scrollElement = editorElement.querySelector('.w-e-scroll')
            if (scrollElement) {
                setTimeout(() => {
                    // 尝试滚动到底部
                    scrollElement.scrollTop = scrollElement.scrollHeight
                }, 100)
            }
        }
    })
}
// 自定义校验图片
function customCheckImageFn(src: string, alt: string, url: string): boolean | undefined | string {
    // TS 语法
    // function customCheckImageFn(src, alt, url) {                                                    // JS 语法
    if (!src) {
        return
    }
    if (src.indexOf('http') !== 0) {
        return '图片网址必须以 http/https 开头'
    }
    return true

    // 返回值有三种选择：
    // 1. 返回 true ，说明检查通过，编辑器将正常插入图片
    // 2. 返回一个字符串，说明检查未通过，编辑器会阻止插入。会 alert 出错误信息（即返回的字符串）
    // 3. 返回 undefined（即没有任何返回），说明检查未通过，编辑器会阻止插入。但不会提示任何信息
}

// 转换图片链接
function customParseImageSrc(src: string): string {
    // TS 语法
    // function customParseImageSrc(src) {               // JS 语法
    if (src.indexOf('http') !== 0) {
        return `http://${src}`
    }
    return src
}
// 自定义校验视频
function customCheckVideoFn(src: string, poster: string): boolean | string | undefined {
    // TS 语法
    // function customCheckVideoFn(src, poster) {                                             // JS 语法
    if (!src) {
        return
    }
    if (src.indexOf('http') !== 0) {
        return '视频地址必须以 http/https 开头'
    }
    return true
    // 返回值有三种选择：
    // 1. 返回 true ，说明检查通过，编辑器将正常插入视频
    // 2. 返回一个字符串，说明检查未通过，编辑器会阻止插入。会 alert 出错误信息（即返回的字符串）
    // 3. 返回 undefined（即没有任何返回），说明检查未通过，编辑器会阻止插入。但不会提示任何信息
}

// 自定义转换视频
function customParseVideoSrc(src: string): string {
    // 转换 bilibili url
    // TS 语法
    // if (src.includes('.bilibili.com')) {
    //     const arr = location.pathname.split('/')
    //     const vid = arr[arr.length - 1]
    //     return `<iframe src="//player.bilibili.com/player.html?bvid=${vid}" scrolling="no" border="0" frameborder="no" framespacing="0" allowfullscreen="true"> </iframe>`
    // }
    return src
}
/**
 *  @LastEditors: lexy
 * @description: 编辑器回调函数
 */
const handleCreated = (editor: any) => {
    editorRef.value = editor
}
</script>

<template>
    <Editor v-model="_content" :default-config="editorConfig" :mode="mode" @on-created="handleCreated" />
</template>

<style lang="scss" scoped></style>
