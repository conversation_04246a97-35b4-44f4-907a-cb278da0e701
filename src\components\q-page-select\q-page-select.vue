<template>
    <el-select
        ref="selectRef"
        :model-value="modelValue"
        v-bind="$attrs"
        :loading="loading"
        :remote-method="handleRemoteMethod"
        @update:model-value="handleModelValueUpdate"
        @visible-change="handleVisibleChange"
        @change="handleChange"
        @blur="handleBlur"
    >
        <el-option v-if="shouldShowSelectedOption" :key="selectedOption[optionKey]" :label="selectedOption[labelKey]" :value="selectedOption">
            <slot name="option" :item="selectedOption">
                {{ selectedOption[labelKey] }}
            </slot>
        </el-option>

        <el-option v-for="item in filteredOptions" :key="item[optionKey]" :label="item[labelKey]" :value="item">
            <slot name="option" :item="item">
                {{ item[labelKey] }}
            </slot>
        </el-option>

        <template #loading>
            <svg class="circular" viewBox="0 0 50 50">
                <circle class="path" cx="25" cy="25" r="20" fill="none" />
            </svg>
        </template>
        <template #footer>
            <!-- 修改：添加 @mousedown.prevent 防止点击分页时关闭下拉框 -->
            <div style="padding: 6px; float: right" @mousedown.prevent @click.stop>
                <el-pagination
                    v-if="total > pageSize"
                    v-model:current-page="currentPage"
                    v-model:page-size="pageSize"
                    :total="total"
                    layout="total, prev, pager, next"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div>
        </template>
    </el-select>
</template>

<script lang="ts" setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import type { SelectProps } from 'element-plus'

const selectRef = ref()

interface PageInfo {
    pageNum: number
    pageSize: number
    query?: string
}

// 继承 ElSelect 的属性并添加自定义属性
interface PageSelectProps {
    modelValue: any
    options: any[]
    optionKey?: string
    labelKey?: string
    loading?: boolean
    total: number
    pageInfo?: {
        current?: number
        size?: number
    }
    remoteMethod?: (pageInfo: PageInfo) => Promise<void> | void
}

const props = withDefaults(defineProps<PageSelectProps>(), {
    optionKey: 'value',
    labelKey: 'label',
    options: () => [],
    loading: false,
    total: 0,
    pageInfo: () => ({ current: 1, size: 10 }),
    remoteMethod: undefined,
})

const emit = defineEmits(['update:modelValue', 'change', 'visible-change', 'remove-tag', 'clear', 'blur', 'focus', 'page-change'])

// 分页相关状态
const currentPage = ref(props.pageInfo?.current || 1)
const pageSize = ref(props.pageInfo?.size || 10)

// 跟踪初始值是否在当前加载的数据中
const initialValueLoaded = ref(false)
const searchQuery = ref('')

// 计算是否需要显示被选中但不在当前options中的选项
const selectedOption = computed(() => {
    return props.modelValue || null
})

const shouldShowSelectedOption = computed(() => {
    if (!selectedOption.value) return false

    // Check if the selected option is not already in the current options list
    return !props.options.some((item) => item[props.optionKey] === selectedOption.value[props.optionKey])
})

// 过滤选项，确保不重复显示已选值
const filteredOptions = computed(() => {
    if (!selectedOption.value) return props.options

    return props.options.filter((item) => item[props.optionKey] !== selectedOption.value[props.optionKey])
})

// 处理分页大小变化
const handleSizeChange = async (val: number) => {
    pageSize.value = val
    currentPage.value = 1

    // 触发远程搜索
    await fetchData(searchQuery.value)

    // 阻止下拉框关闭
    await nextTick(() => {
        if (selectRef.value) {
            selectRef.value.visible = true
        }
    })

    emitPageChange()
}

// 远程搜索处理方法
const handleRemoteMethod = async (query: string) => {
    searchQuery.value = query
    await fetchData(query)
}

// 将远程搜索逻辑抽离出来
const fetchData = async (query: string) => {
    const pageInfo: PageInfo = {
        pageNum: currentPage.value,
        pageSize: pageSize.value,
        query,
    }

    if (props.remoteMethod) {
        // Set a header to prevent automatic cancellation
        // We do this by wrapping the original remoteMethod
        await props.remoteMethod(pageInfo)
    }
}

const handleCurrentChange = (val: number) => {
    event?.stopPropagation()
    currentPage.value = val
    nextTick(async () => {
        await fetchData(searchQuery.value)
        emitPageChange()
    })
}

const emitPageChange = () => {
    const pageInfo: PageInfo = {
        pageNum: currentPage.value,
        pageSize: pageSize.value,
    }
    emit('page-change', pageInfo)
}

// 可见性变化处理
const handleVisibleChange = async (visible: boolean) => {
    if (visible) {
        currentPage.value = 1 // 重置页码
        await fetchData('') // 触发初始搜索
    }
    emit('visible-change', visible)
}

// 值变化处理
const handleChange = (value: any) => {
    emit('change', value)
}

// 处理模型值更新
const handleModelValueUpdate = (value: any) => {
    emit('update:modelValue', value)
}

const handleBlur = () => {
    // 当选择框失去焦点时，确保下拉框关闭
    if (selectRef.value) {
        selectRef.value.visible = false
    }
}

// 启动时如果有初始值，确保能够显示
onMounted(async () => {
    if (props.modelValue && props.remoteMethod) {
        // 如果有初始值，先加载第一页数据看看初始值是否在其中
        await fetchData('')

        // 如果初始值不在当前加载的选项中，需要显示特殊处理
        const isValueInOptions = props.options.some((item) => item[props.optionKey] === props.modelValue[props.optionKey])

        initialValueLoaded.value = isValueInOptions

        // 如果需要，可以在这里添加逻辑去专门获取初始值的数据
        // 这取决于后端API是否支持按ID查询单个项目
    }
})
</script>
