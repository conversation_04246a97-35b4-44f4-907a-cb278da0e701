/**
 * @: 搜索栏配置
 */
export type RetrieveType = {
    maxPrice?: number
    minPrice?: number
    platformCategoryFirstId?: string
    keyword?: string
}

/**
 * @: 选择分类
 */
export type ClassItemType = {
    platformCategoryFirstId: string
    platformCategoryFirstName: string
    productNum: number
}

export interface LinkSelectItem {
    id: string
    type: number
    name: string
    url: string
    append?: string
    shopId?: string
}

export interface itemType {
    id: string
    productId: string
    shopId: string
    pic: string
    salePrices: string[]
    prices: string[]
    productName: string
    platformCategoryFirstId: string
}
