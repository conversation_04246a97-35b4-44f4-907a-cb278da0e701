<!--
 * @description: 上传组件
 * @Author: lexy
 * @Date: 2022-08-20 09:59:34
 * @LastEditors: lexy
 * @LastEditTime: 2023-07-10 09:43:43
-->
<script lang="ts" setup>
import { ref, PropType, withDefaults, defineExpose, StyleValue } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import Cropper from './cropper.vue'
import { ElMessage } from 'element-plus'
import { elementUploadRequest } from '@/apis/upload'
import { useVModel } from '@vueuse/core'
import { verifyFormat, httpRequest } from '@/components/q-upload/types/Upload'
import type { UploadConfig, CusUploadRawFile } from '@/components/q-upload/types/Upload'
import type { UploadFile, UploadRawFile } from 'element-plus'
import uuid from '@/utils/uuid'
import { dataURLtoFile } from '@/utils/util'
/*
 *variable
 */

interface QUploadProps {
    src: string
    uploadUrl?: string
    format?: UploadConfig
    width?: number
    height?: number
    isCropper?: boolean
    isAutoUpload?: boolean
    disabled?: boolean
    style?: StyleValue
}

const $props = withDefaults(defineProps<QUploadProps>(), {
    src: '',
    uploadUrl: 'gruul-mall-carrier-pigeon/oss/upload',
    format: {
        width: 10000,
        height: 10000,
        types: ['image/png', 'image/jpg', 'image/gif', 'image/jpeg', 'image/webp', 'image/bmp'],
        size: 10,
    },
    width: 120,
    height: 120,
    isAutoUpload: true,
    // 是否需要裁剪
    isCropper: true,
    disabled: false,
    style: '',
})
const $emit = defineEmits(['update:src', 'change', 'beforeUpdate'])
const imgSrc = useVModel($props, 'src', $emit)
const uploadRef = ref()
const cropperSrc = ref('')
const isShowCropper = ref(false)
// 暂存cropper的file
const tempCropperFile = ref()
/*
 *lifeCircle
 */
/*
 *function
 */
const handleChangeUpload = async (file: CusUploadRawFile, fileList: CusUploadRawFile[]) => {
    if (file.status !== 'ready') return
    const { src, success } = await verifyFormat(file, $props.format)
    if (success && src) {
        if ($props.isCropper) {
            cropperSrc.value = src
            isShowCropper.value = true
            if (fileList.length > 1) {
                fileList.splice(0, fileList.length - 1)
            }
        } else {
            // @ts-ignore
            const base64: string = await fileToBase64(file.raw)
            const files = dataURLtoFile(base64, uuid(10))
            // @ts-ignore
            handleCropperLoad(files)
        }
        // if (fileList.length > 5) {
        //     ElMessage.error('图片最多只能上传5张')
        // }
    } else {
        uploadRef.value.handleRemove(file)
    }
}
/**
 * @LastEditors: lexy
 * @description: cropper组件裁剪完成回调
 * @param {string} uploadStr
 */
const handleCropperLoad = (uploadStr: string) => {
    tempCropperFile.value = uploadStr
    uploadRef.value.submit()
}
const handleUploadRequest = async (request: any) => {
    try {
        $emit('beforeUpdate', request.file)
        if ($props.isAutoUpload) {
            let imgStr = await httpRequest(request, tempCropperFile.value)
            imgSrc.value = imgStr
            $emit('change', imgStr)
        } else {
            $emit('change', tempCropperFile.value)
        }
    } catch (e) {
        ElMessage.error('图片上传失败')
    }
}

function fileToBase64(file: any) {
    return new Promise((resolve) => {
        //file转bse64
        let reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = function (e) {
            resolve(e.target?.result)
        }
    })
}

const slots = useSlots()
</script>
<template>
    <div class="qupload">
        <el-upload
            ref="uploadRef"
            :disabled="$props.disabled"
            :action="$props.uploadUrl"
            :auto-upload="false"
            :http-request="handleUploadRequest"
            :on-change="handleChangeUpload"
            :show-file-list="false"
            class="avatar-uploader"
            :style="[{ width: `${$props.width}px`, height: `${$props.height}px` }, $props.style]"
        >
            <template v-if="!slots.default">
                <img
                    v-if="imgSrc"
                    class="avatar"
                    :src="imgSrc"
                    :style="{ width: `${$props.width}px`, height: `${$props.height}px`, objectFit: 'contain' }"
                />
                <el-icon v-else :style="{ width: `${$props.width}px`, height: `${$props.height}px` }" class="avatar-uploader-icon">
                    <i-ep-plus />
                </el-icon>
            </template>
            <slot></slot>
        </el-upload>
        <Cropper v-if="isShowCropper" v-model:cropper-show="isShowCropper" :cropper-src="cropperSrc" @upload-img="handleCropperLoad" />
    </div>
</template>

<style lang="scss" scoped>
.qupload {
    .avatar-uploader {
        .avatar {
            width: 178px;
            height: 178px;
            display: block;
        }
    }
}
</style>
<style>
.avatar-uploader .el-upload {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
    border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    text-align: center;
}
</style>
