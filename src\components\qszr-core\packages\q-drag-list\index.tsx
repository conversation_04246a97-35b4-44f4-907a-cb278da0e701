import type { PropType } from 'vue'
import { VueDraggableNext } from 'vue-draggable-next'
import '../styles/m-dragList.scss'

const buildProps = () => ({
    data: {
        type: Array as PropType<any[]>,
        default() {
            return []
        },
    },
    secondKey: {
        type: String,
        default: '',
    },
    thirdKey: {
        type: String,
        default: '',
    },
    onDragMove: {
        type: Function as PropType<(e: any) => void>,
    },
})
export default defineComponent({
    props: buildProps(),
    emits: ['dragMove'],
    setup(props, { slots, emit }) {
        const list = ref<any[]>([])
        watch(props, () => {
            list.value = props.data
        })
        const emitHandle = (e: { data: any }) => {
            emit('dragMove', e)
        }
        return () => (
            <div class="drag__list">
                <VueDraggableNext vModel={list.value} onEnd={() => emitHandle({ data: list.value })}>
                    {list.value.map((itemData, i) => {
                        return (
                            <el-collapse>
                                <el-collapse-item
                                    name={props.secondKey + i}
                                    disabled={itemData[props.secondKey] && !itemData[props.secondKey].length}
                                >
                                    {{
                                        title: () => (
                                            <div class="drag__list--item">
                                                {slots.item && slots.item({ childData: itemData, fLevelData: itemData, sLevelData: {}, index: i })}
                                            </div>
                                        ),
                                        default: () => (
                                            <VueDraggableNext
                                                vModel={itemData[props.secondKey]}
                                                onEnd={() => emitHandle({ data: itemData[props.secondKey] })}
                                            >
                                                {itemData[props.secondKey].map((item: any, j: number) => {
                                                    return (
                                                        <el-collapse-item
                                                            name={props.thirdKey + j}
                                                            disabled={item[props.thirdKey] && !item[props.thirdKey].length}
                                                        >
                                                            {{
                                                                title: () => (
                                                                    <div class="drag__list--item">
                                                                        {slots.item &&
                                                                            slots.item({
                                                                                childData: item,
                                                                                fLevelData: itemData,
                                                                                sLevelData: item,
                                                                                index: j,
                                                                            })}
                                                                    </div>
                                                                ),
                                                                default: () => (
                                                                    <VueDraggableNext
                                                                        vModel={item[props.thirdKey]}
                                                                        onEnd={() => emitHandle({ data: item[props.thirdKey] })}
                                                                    >
                                                                        {item[props.thirdKey].map((ite: any, k: number) => {
                                                                            return (
                                                                                <div>
                                                                                    {slots.child &&
                                                                                        slots.child({
                                                                                            childData: ite,
                                                                                            fLevelData: itemData,
                                                                                            sLevelData: item,
                                                                                            index: k,
                                                                                        })}
                                                                                </div>
                                                                            )
                                                                        })}
                                                                    </VueDraggableNext>
                                                                ),
                                                            }}
                                                        </el-collapse-item>
                                                    )
                                                })}
                                            </VueDraggableNext>
                                        ),
                                    }}
                                </el-collapse-item>
                            </el-collapse>
                        )
                    })}
                </VueDraggableNext>
            </div>
        )
    },
})
