/**
 * 基于elementPlus dropDown二次封装
 * @property {string} title dropDown 按钮文字
 * @property {Array<CommondType>} commandList  dropDown列表
 * @example <drop-down title='' commandList="{name,label}"></drop-down>
 */
import { withModifiers } from 'vue'
import { ElDropdown, ElDropdownMenu, ElDropdownItem } from 'element-plus'
import type { PropType } from 'vue'
import '../styles/m-dropDown.scss'

export type CommondType = Record<'name' | 'label', string>
const buildProps = () => ({
    title: {
        type: String,
        default() {
            return '批量操作'
        },
    },
    commandList: {
        type: Array as PropType<CommondType[]>,
        default() {
            return [{ name: 'mishandle', label: '暂无操作' }]
        },
    },
    onClick: {
        type: Function as PropType<(arg?: any) => void>,
    },
    onCommand: {
        type: Function as PropType<(arg: string) => void>,
    },
    onVisibleChange: {
        type: Function as PropType<(arg?: any) => void>,
    },
})
export default defineComponent({
    name: 'DropDown',
    props: buildProps(),
    emits: ['click', 'command', 'visibleChange'],
    setup(props, { slots, emit }) {
        const dropdownSlots = {
            dropdown: () => (
                <ElDropdownMenu>
                    {!props.commandList.length ? (
                        <ElDropdownItem disabled>暂无操作</ElDropdownItem>
                    ) : (
                        props.commandList.map((item) => {
                            return <ElDropdownItem command={item.name}>{item.label}</ElDropdownItem>
                        })
                    )}
                </ElDropdownMenu>
            ),
        }
        const handleCommand = (e: string) => {
            emit('command', e)
        }
        const visibleChange = () => {
            emit('visibleChange')
        }
        const clickHandle = () => {
            emit('click')
        }
        return () => (
            <div class="mDropDown">
                <div class="mDropDown__title" onClick={withModifiers(clickHandle, ['stop'])}>
                    {props.title}&nbsp;&nbsp;&nbsp;|
                </div>
                <div class="mDropDown__col"></div>
                <ElDropdown v-slots={dropdownSlots} onCommand={handleCommand} onVisibleChange={visibleChange} trigger="click">
                    <div class="mDropDown__multi">...</div>
                </ElDropdown>
            </div>
        )
    },
})
