<!--
 * @description: 备注旗帜  <remark-flag :content="" @see-remark="" />
 * @Author: lexy
 * @Date: 2022-12-21 17:57:06
 * @LastEditors: lexy
 * @LastEditTime: 2022-12-21 18:17:13
-->
<script setup lang="ts">
/*
 *variable
 */
defineProps({
    content: { type: String, default: '' },
})
const $emit = defineEmits(['SeeRemark'])
/*
 *lifeCircle
 */
/*
 *function
 */
</script>

<template>
    <el-popover :disabled="!content" trigger="hover" :content="content" :teleported="false">
        <template #reference>
            <el-link :underline="false" @click="$emit('SeeRemark')">
                <QIcon name="icon-qizhi" size="24px" :color="content ? 'red' : '#5b6982'" />
            </el-link>
        </template>
    </el-popover>
</template>

<style scoped></style>
