<template>
    <el-tree
        v-if="rv.treeList.length > 0"
        ref="treeRef"
        :data="rv.treeList"
        node-key="id"
        default-expand-all
        :expand-on-click-node="false"
        :props="{ class: customNodeClass }"
    >
        <template #default="{ node, data }">
            <span class="custom-tree-node">
                <span>
                    {{ node.label
                    }}<el-tag size="small" :type="data.type === 'CATALOG' ? 'success' : 'warning'">{{
                        data.type === 'CATALOG' ? '目录' : '菜单'
                    }}</el-tag>
                </span>
                <span>
                    <el-button type="primary" size="small" @click="editTreeForm(data)">编辑</el-button>
                    <el-button v-if="data.type === 'CATALOG'" type="primary" size="small" @click="showDialogHandle(data.id, data)">新增 </el-button>
                    <el-popconfirm
                        v-if="(data.type === 'CATALOG' && data.children.length === 0) || data.type === 'MENU'"
                        title="Are you sure to delete this?"
                        @confirm="delTreeForm(data)"
                    >
                        <template #reference>
                            <el-button type="danger" size="small">删除</el-button>
                        </template>
                    </el-popconfirm>
                </span>
            </span>
        </template>
    </el-tree>
    <el-button type="primary" @click="showDialogHandle('0')">新增菜单</el-button>
    <el-dialog v-model="newDialogVisible" :title="isNew ? '新增权限' : '编辑权限'" @close="closeDialog">
        <el-form ref="ruleFormRef" :model="rv.authTreeForm" :rules="actionRules()">
            <el-form-item v-if="!isCatalog" label="访问路径" :label-width="80" prop="path">
                <el-input v-model="rv.authTreeForm.path" autocomplete="off" maxlength="20" @input="generatePermHandle" />
            </el-form-item>
            <el-form-item v-if="!isCatalog" label="访问权限" :label-width="80" prop="perm">
                <el-input v-model="rv.authTreeForm.perm" autocomplete="off" maxlength="20" />
            </el-form-item>
            <el-form-item label="名称" :label-width="80" prop="name">
                <el-input v-model="rv.authTreeForm.name" autocomplete="off" maxlength="20" />
            </el-form-item>
            <el-form-item label="类型" :label-width="80" prop="type">
                <el-select v-model="rv.authTreeForm.type" placeholder="请选择类型" @change="selectChange">
                    <el-option label="目录" value="CATALOG" />
                    <el-option label="菜单" value="MENU" />
                </el-select>
            </el-form-item>
            <el-form-item label="排序" :label-width="80" prop="order">
                <el-input-number v-model="rv.authTreeForm.order" :min="1" :max="Infinity" />
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="newDialogVisible = false">关闭</el-button>
                <el-button
                    id="directTag"
                    v-debounce="{
                        func: submitForm,
                        wait: 1000,
                        type: 'click',
                    }"
                    type="primary"
                    >提交</el-button
                >
            </span>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import type { ElTree, FormInstance } from 'element-plus'
import { useRequest } from 'v3hooks'
import request from '@/apis/request'
import { getAuth, doNewPermissions, doEditPermissions, doDelPermissions } from '@/apis/setting'
interface Tree {
    id: number
    label: string
    children?: Tree[]
}
const ruleFormRef = ref<FormInstance>()
console.log(ruleFormRef.value)
// 新增弹窗
const newDialogVisible = ref(false)

const rv = reactive({
    authTreeForm: {
        parentId: '0',
        perm: '',
        name: '',
        type: 'MENU',
        order: '0',
        path: '',
        children: [],
    },
    treeList: [],
})
const constantRulePath = [
    {
        required: true,
        message: '请填写访问路径',
        trigger: 'blur',
    },
]
const constantRulePerm = [
    {
        required: true,
        message: '请填写权限信息',
        trigger: 'blur',
    },
]
const actionRules = () => {
    let tempObj = {
        name: [
            {
                required: true,
                message: '请填写菜单/目录名称',
                trigger: 'blur',
            },
        ],
    }
    if (!isCatalog.value) {
        tempObj.perm = constantRulePerm
        tempObj.path = constantRulePath
    }
    return tempObj
}
const test = (a, b) => {
    console.log(a)
    console.log(b)
    console.log(1111111)
}
const customNodeClass = (data: Tree) => {
    if (data.type === 'CATALOG') {
        return 'is-catalog'
    } else {
        return 'is-menu'
    }
}
const treeRef = ref<InstanceType<typeof ElTree>>()

// 编辑新增标识符
const isNew = ref(true)
// 编辑目录标识符
const isCatalog = ref(false)
initTreeHandle()

const showDialogHandle = (parentId: string, data?: any) => {
    rv.authTreeForm.parentId = parentId
    isCatalog.value = data && data.type === 'CATALOG' ? true : false
    isNew.value = true
    newDialogVisible.value = true
}

const generatePermHandle = () => {
    const authTreeForm = rv.authTreeForm
    if (authTreeForm.path === '') return
    authTreeForm.perm = authTreeForm.path.split('/').splice(1).join(':')
}

const submitForm = async () => {
    if (!ruleFormRef.value) return
    await ruleFormRef.value.validate((valid, fields) => {
        if (valid) {
            if (isNew.value) {
                doNewPermissions(rv.authTreeForm).then((res) => {
                    if (res.code === 200) {
                        initTreeHandle()
                    }
                })
            } else {
                doEditPermissions(rv.authTreeForm.id, rv.authTreeForm).then((res) => {
                    if (res.code === 200) {
                        initTreeHandle()
                    }
                })
            }
            newDialogVisible.value = false
        }
    })
}
const closeDialog = () => {
    rv.authTreeForm = {
        parentId: '0',
        perm: '',
        name: '',
        type: 'CATALOG',
        order: '0',
        children: [],
        path: '',
    }
}
const editTreeForm = (data) => {
    let isCatalogFlag = false
    isNew.value = false
    const rawData = JSON.parse(JSON.stringify(toRaw(data)))
    if (rawData.type === 'MENU') {
        rawData.perm = rawData.perms[0]
    } else {
        isCatalogFlag = true
    }
    rv.authTreeForm = rawData
    isCatalog.value = isCatalogFlag
    newDialogVisible.value = true
}
const delTreeForm = (data) => {
    doDelPermissions(data.id).then((res) => {
        if (res.code === 200) {
            initTreeHandle()
        }
    })
}
const selectChange = (val: string) => {
    isCatalog.value = val === 'CATALOG' ? true : false
}
function initTreeHandle() {
    getAuth().then((res) => {
        rv.treeList = replaceLabel(res.data)
    })
}

function replaceLabel(data: any) {
    for (let i = 0; i < data.length; i++) {
        data[i].label = data[i].name
        if (data[i].children && data[i].children.length > 0) {
            data[i].children = replaceLabel(data[i].children)
        }
    }
    return data
}
</script>

<style>
.custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
}
/* .is-catalog {
  color: red;
}
.is-menu {
  color: blue;
} */
</style>
