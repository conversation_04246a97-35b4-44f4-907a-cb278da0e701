export interface Captcha {
    backgroundImage: string
    backgroundImageHeight: number
    backgroundImageTag: 'default'
    backgroundImageWidth: number
    templateImage: string
    templateImageHeight: number
    templateImageWidth: number
    type: 'WORD_IMAGE_CLICK'
}
export interface CaptchaResponse {
    id: string
    captcha: Captcha
}

export enum TrackType {
    /** 抬起.*/
    UP = 'UP',
    /** 按下.*/
    DOWN = 'DOWN',
    /** 移动.*/
    MOVE = 'MOVE',
    /** 点击.*/
    CLICK = 'CLICK',
}

export interface Track {
    /** x. */
    x: number
    /** y. */
    y: number
    /** 时间. */
    t: number
    /** 类型. */
    type: TrackType
}

export interface ImageCaptchaTrack {
    backgroundImage: string
    backgroundImageHeight: number
    backgroundImageTag: 'default'
    backgroundImageWidth: number
    templateImage: string
    templateImageHeight: number
    templateImageWidth: number
    type: 'WORD_IMAGE_CLICK'
}

/**
 * 浏览器
 */
export interface CaptchaRequest<T> {
    mobile: T
    id: string
    smsType: string
}
