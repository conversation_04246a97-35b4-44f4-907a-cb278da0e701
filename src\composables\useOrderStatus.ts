/*
 * @description:
 * @Author: lexy
 * @Date: 2022-06-30 15:30:31
 * @LastEditors: lexy
 * @LastEditTime: 2023-07-12 13:42:36
 */
import { QUERYORDERSTATUS, ORDERSTATUS, SHOPORDERSTATUS, PACKAGESTATUS, ApiOrder, DISTRIBUTION } from '@/views/order/types/order'

const queryStatus: Record<keyof typeof QUERYORDERSTATUS, string> = {
    UNPAID: '待支付',
    UN_DELIVERY: '待发货',
    UN_RECEIVE: '待收货',
    COMPLETED: '已完成',
    CLOSED: '已关闭',
}
const orderStatus: Record<keyof typeof ORDERSTATUS, string> = {
    UNPAID: '未支付',
    PAID: '已支付',
    BUYER_CLOSED: '买家关闭订单',
    SYSTEM_CLOSED: '超时未支付 系统关闭',
    SELLER_CLOSED: '卖家关闭订单',
    TEAMING: '拼团中',
    TEAM_FAIL: '拼团失败',
}

const shopOrderStatus: Record<keyof typeof SHOPORDERSTATUS, string> = {
    OK: '正常状态',
    SYSTEM_CLOSED: '系统关闭',
    BUYER_CLOSED: '买家关闭订单',
    SELLER_CLOSED: '卖家关闭订单',
}
const distributionModeStatus: Record<keyof typeof DISTRIBUTION, string> = {
    MERCHANT: '快递配送',
    EXPRESS: '快递配送',
    INTRA_CITY_DISTRIBUTION: '同城配送',
    SHOP_STORE: '门店自提',
    VIRTUAL: '无需物流',
}
const packageStatus: Record<keyof typeof PACKAGESTATUS, string> = {
    WAITING_FOR_DELIVER: '未发货',
    WAITING_FOR_RECEIVE: '待收货',
    BUYER_WAITING_FOR_COMMENT: '待评价',
    SYSTEM_WAITING_FOR_COMMENT: '待评价',
    BUYER_COMMENTED_COMPLETED: '已完成',
    SYSTEM_COMMENTED_COMPLETED: '已完成',
}
// *---------------
/**
 * @LastEditors: lexy
 * @description: 将订单shopOrderItems合并
 * @param {ApiOrder} data
 * @returns {*}
 */
const getOrdercn = (order: ApiOrder) => {
    if (order.status !== 'PAID')
        return orderStatus[order.status] + (order.status === 'UNPAID' && order.shopOrders[0].status !== 'OK' ? '（已关闭）' : '')
    if (order.shopOrders[0].status !== 'OK') return shopOrderStatus[order.shopOrders[0].status]
    const shopOrderItems = order.shopOrders[0].shopOrderItems
    const closedNum = shopOrderItems.filter((item) => item.status !== 'OK').length
    const delivery = shopOrderItems.filter((item) => item.packageStatus !== 'WAITING_FOR_DELIVER').length
    if (closedNum === shopOrderItems.length) return '已关闭'
    if (closedNum + delivery === shopOrderItems.length) return '已发货'
    return delivery === 0 ? '待发货' : '部分发货'
}
/**
 * @LastEditors: lexy
 * @description: 订单状态转中文
 * @param {keyof} status
 * @returns {*}
 */

// *---------------
/**
 * @LastEditors: lexy
 * @description: 商品状态
 * @param {keyof} str
 * @returns {*}
 */
const getShopcn = (str: keyof typeof SHOPORDERSTATUS) => {
    return shopOrderStatus[str]
}
/**
 * @LastEditors: lexy
 * @description: tab状态
 * @param {keyof} str
 * @returns {*}
 */
const getDeliverycn = (str: keyof typeof queryStatus) => {
    return queryStatus[str]
}
/**
 * @LastEditors: lexy
 * @description: 是否发货状态
 * @returns {*}
 */
const isSendGoods = (str: keyof typeof queryStatus) => {
    return str !== 'UN_DELIVERY'
}
/**
 * @LastEditors: lexy
 * @description: TabsName
 * @returns {*}
 */
const TabsName = ['近三个月订单', '近一个月订单', '全部订单']

export {
    queryStatus,
    orderStatus,
    shopOrderStatus,
    packageStatus,
    getShopcn,
    getDeliverycn,
    isSendGoods,
    getOrdercn,
    TabsName,
    distributionModeStatus,
}
