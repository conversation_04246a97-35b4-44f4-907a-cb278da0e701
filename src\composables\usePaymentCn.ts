/*
 * @description:支付类型中文转换
 * @Author: lexy
 * @Date: 2022-07-22 19:01:16
 * @LastEditors: lexy
 * @LastEditTime: 2022-11-14 13:06:59
 */
import { ORDERPAYMENT, PAY_MODE } from '@/views/order/types/order'
const payType: Record<keyof typeof ORDERPAYMENT, string> = {
    WECHAT: '微信',
    ALIPAY: '支付宝',
    BALANCE: '储值',
    YST: '云商通',
}
export const payTypeFn = (str: keyof typeof ORDERPAYMENT, mode?: keyof typeof PAY_MODE) => {
    if (str === 'YST' && mode) {
        if (mode.includes('WECHAT')) {
            return '微信'
        }
        if (mode.includes('ALIPAY')) {
            return '支付宝'
        }
    }
    return payType[str]
}
