/*
 * @description:根据salePrices取区间价
 * @Author: lexy
 * @Date: 2022-12-07 15:24:33
 * @LastEditors: lexy
 * @LastEditTime: 2024-03-29 15:51:08
 */
import useConvert from './useConvert'
const { divTenThousand } = useConvert()
export default function usePriceRange() {
    return {
        range,
    }
}

function range(salePrices: string[] | string) {
    if (Array.isArray(salePrices)) {
        if (!salePrices.length) return '0'
        let priceArr: string[] = []
        salePrices.forEach((item) => {
            priceArr.push(String(divTenThousand(item)))
        })
        const delRepetPriceArr = Array.from(new Set(priceArr))
        return delRepetPriceArr.length > 1
            ? `${parseFloat(delRepetPriceArr[0]).toFixed(2)}-${parseFloat(delRepetPriceArr[delRepetPriceArr.length - 1]).toFixed(2)}`
            : parseFloat(delRepetPriceArr[0]).toFixed(2)
    } else {
        return divTenThousand(salePrices).toFixed(2)
    }
}
