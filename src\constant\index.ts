/*
 * @description:
 * @Author: lexy
 * @Date: 2023-01-30 17:03:48
 * @LastEditors: lexy
 * @LastEditTime: 2024-03-09 13:35:53
 */

export const REGEX = {
    DATE: /((\d{3}[1-9]|\d{2}[1-9]\d{1}|\d{1}[1-9]\d{2}|[1-9]\d{3})-(((0[13578]|1[02])-(0[1-9]|[12]\d|3[01]))|((0[469]|11)-(0[1-9]|[12]\d|30))|(02-(0[1-9]|[1]\d|2[0-8]))))|(((\d{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29)((\d{3}[1-9]|\d{2}[1-9]\d{1}|\d{1}[1-9]\d{2}|[1-9]\d{3})-(((0[13578]|1[02])-(0[1-9]|[12]\d|3[01]))|((0[469]|11)-(0[1-9]|[12]\d|30))|(02-(0[1-9]|[1]\d|2[0-8]))))|(((\d{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29)/,
    TIME: /\d{1,2}:\d{1,2}(:\d{1,2})?/,
    TIME_DATE:
        /((\d{3}[1-9]|\d{2}[1-9]\d{1}|\d{1}[1-9]\d{2}|[1-9]\d{3})-(((0[13578]|1[02])-(0[1-9]|[12]\d|3[01]))|((0[469]|11)-(0[1-9]|[12]\d|30))|(02-(0[1-9]|[1]\d|2[0-8]))))|(((\d{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29)((\d{3}[1-9]|\d{2}[1-9]\d{1}|\d{1}[1-9]\d{2}|[1-9]\d{3})-(((0[13578]|1[02])-(0[1-9]|[12]\d|3[01]))|((0[469]|11)-(0[1-9]|[12]\d|30))|(02-(0[1-9]|[1]\d|2[0-8]))))|(((\d{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29)\s\d{1,2}:\d{1,2}(:\d{1,2})?/,
    HTTP_URL: /^((https|http|ftp|rtsp|mms)?:\/\/)[^\s]+/,
    HTTPS_URL: /^https:\/\/[\w\.-]+\.[a-zA-Z]{2,6}$/,
    NUMBERS: /\d+/,
    BLANK: /[\s\S]*.*[^\s][\s\S]/,
    MOBILE: /(?:0|86|\+86)?1[3-9]\d{9}/,
    CITIZEN_ID: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
    EMAIL: /^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,
    PASSWORD: /^(?![0-9]+$)(?![a-z]+$)(?![A-Z]+$)(?!([^(0-9a-zA-Z)])+$)([^(0-9a-zA-Z)]|[a-z]|[A-Z]|[0-9]){6,20}$/,
    GROUP_CREDIT_CODE: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/,
    PROVINCE_CITY:
        /^((?<province>[^省]+省|.+自治区|上海市|北京市|天津市|重庆市|台湾省|香港特别行政区|澳门特别行政区)|)(?<city>[^市]+市|.+自治州|)(?<district>[^区]+区|.+县|.+旗|.+岛|.+镇|.+管委会|.+海域|.+林区|.+特区|.+街道|)(?<street>.+)$/,
}

// 待OSS采购后一致调整
type DEFAULT_SYSTEM = {
    LICENSE: string
    IDCARD_FRONT: string
    IDCARD_BACK: string
    IDCARD_HOLDON?: string
}

export const DEFAULT_SYSTEM_IMG: DEFAULT_SYSTEM = {
    LICENSE: 'https://devoss.chongyoulingxi.com/system-front/mobile/def_register_license.png',
    IDCARD_FRONT: 'https://devoss.chongyoulingxi.com/system-front/mobile/def_id_card_front.png',
    IDCARD_BACK: 'https://devoss.chongyoulingxi.com/system-front/mobile/def_id_card_back.png',
    IDCARD_HOLDON: 'https://devoss.chongyoulingxi.com/system-front/mobile/20241113-175504.jpeg',
}
