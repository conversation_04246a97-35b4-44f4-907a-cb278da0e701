/*
 * @description:
 * @Author: lexy
 * @Date: 2023-07-01 10:04:05
 * @LastEditors: lexy
 * @LastEditTime: 2023-07-01 10:38:16
 */
/// <reference types="vite/client" />

declare module 'element-plus'

declare module '*.vue' {
    import type { DefineComponent } from 'vue'

    // eslint-disable-next-line ts/ban-types
    const component: DefineComponent<{}, {}, any>
    export default component
}

interface ImportMeta {
    readonly env: ImportMetaEnv
}
