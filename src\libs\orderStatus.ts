/*
 * @description:
 * @Author: lexy
 * @Date: 2022-09-01 09:52:20
 * @LastEditors: lexy
 * @LastEditTime: 2023-06-29 11:35:14
 */
import { orderStatus, packageStatus, shopOrderStatus } from '@/composables/useOrderStatus'
import type { ShopOrder } from '@/views/order/types/order'
interface HandlerType {
    setNext: (handler: HandlerType) => HandlerType
    handle(request: ShopOrder, orderStatus?: string): string
}
abstract class AbstractHandler implements HandlerType {
    private nextHandler: HandlerType | undefined
    public setNext(handler: HandlerType): HandlerType {
        this.nextHandler = handler
        return handler
    }
    public handle(request: ShopOrder): string {
        if (this.nextHandler) {
            return this.nextHandler.handle(request)
        }
        return ''
    }
}

export class ShopClose extends AbstractHandler {
    public handle(request: ShopOrder, orderStatus = ''): string {
        if (orderStatus === 'UNPAID') {
            return shopOrderStatus[orderStatus]
        }
        if (request.status !== 'OK') {
            return shopOrderStatus[request.status]
        }
        return super.handle(request)
    }
}
class OrderClose extends AbstractHandler {
    public handle(request: ShopOrder): string {
        const shelvesNum = request.shopOrderItems.filter((itme) => itme.status !== 'OK').length
        if (shelvesNum === request.shopOrderItems.length) {
            return '交易失败'
        }
        return super.handle(request)
    }
}
class AwaitSendGoods extends AbstractHandler {
    public handle(request: ShopOrder): string {
        const shelvesNum = request.shopOrderItems.filter((itme) => itme.status !== 'OK').length
        const awaitDeliveryNum = request.shopOrderItems.filter((item) => item.packageStatus === 'WAITING_FOR_DELIVER').length
        if (awaitDeliveryNum + shelvesNum === request.shopOrderItems.length) {
            return `待发货`
        } else if (awaitDeliveryNum) {
            return `部分待发货`
        }
        return super.handle(request)
    }
}
class BeReceivedGoods extends AbstractHandler {
    public handle(request: ShopOrder): string {
        const shelvesNum = request.shopOrderItems.filter((itme) => itme.status !== 'OK').length
        const deliveryNum = request.shopOrderItems.filter((item) => item.packageStatus === 'WAITING_FOR_RECEIVE').length
        if (deliveryNum + shelvesNum === request.shopOrderItems.length) {
            return `已发货`
        }
        return super.handle(request)
    }
}

class SendGoods extends AbstractHandler {
    public handle(request: ShopOrder): string {
        //  待收货 0
        const deliveryNum = request.shopOrderItems.filter((item) => item.packageStatus === 'WAITING_FOR_RECEIVE').length
        if (deliveryNum !== 0) {
            console.log(request.shopOrderItems, 'shopOrderItems')
            return '订单异常订单异常'
        }
        // 现在是分包裹 只要是一个订单的包裹，那么他们的包裹状态就是一样的取【0】即可
        return request.shopOrderItems.map((item) => packageStatus[item.packageStatus])[0]
        // return deliveryNum === 0 ? '待发货' : '部分待收货'
        // return super.handle(request)
    }
}
// 执行方法
export function clientCode(handler: HandlerType, order: ShopOrder, orderStatus = '') {
    const result = handler.handle(order, orderStatus)
    if (result) {
        return result
    } else {
        return '订单异常'
    }
}
const shopClose = new ShopClose()
const orderClose = new OrderClose()
const beReceivedGoods = new BeReceivedGoods()
const awaitSendGoods = new AwaitSendGoods()
const sendGoods = new SendGoods()
shopClose.setNext(orderClose).setNext(awaitSendGoods).setNext(beReceivedGoods).setNext(sendGoods)
export const orderStatusResponsibility = (order: ShopOrder, orderStatus = '') => clientCode(shopClose, order, orderStatus)
