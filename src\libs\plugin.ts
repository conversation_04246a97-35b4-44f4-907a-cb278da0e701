/*
 * @description:
 * @Author: lexy
 * @Date: 2022-04-18 15:31:06
 * @LastEditors: lexy
 * @LastEditTime: 2023-01-30 16:57:00
 */
import directives from '@/directives'
import { VueDraggableNext } from 'vue-draggable-next'
import VueCropper from 'vue-cropperjs'
import My<PERSON>ropper from '@/components/my-cropper/my-cropper.vue'
import Vue3DraggableResizable from 'vue3-draggable-resizable'
import VueDOMPurifyHTML from 'vue-dompurify-html'
export const initPlugin = {
    install(Vue: import('vue').App<any>) {
        // 注册指令
        registerDirective(Vue)
        // 注册自定义UI库
        registerComponent(Vue)
        Vue.use(VueDOMPurifyHTML)
    },
}
const registerComponent = (Vue: import('vue').App<any>) => {
    Vue.component('VueDraggableNext', VueDraggableNext)
    Vue.component('VueCropper', VueCropper)
    Vue.component('My<PERSON>ropper', MyCropper)
    Vue.component('VueDragResize', Vue3DraggableResizable)
}
const registerDirective = (Vue: import('vue').App<any>) => {
    Object.keys(directives).forEach((item) => {
        Vue.directive(item, directives[item as keyof typeof directives])
    })
}
