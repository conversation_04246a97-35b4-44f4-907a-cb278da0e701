/*
 * @description:
 * @Author: lexy
 * @Date: 2023-02-02 11:34:55
 * @LastEditors: lexy
 * @LastEditTime: 2023-07-18 13:37:49
 */
import { setStompConfig } from './StompHandler'
import { useAdminInfo } from '@/store/modules/admin'

export class StompStarter {
    /**
     * 设置stomp配置参数
     * @param shopInfo 店铺信息 token 店铺id 用户id
     */
    static setConfig = (shopInfo: any) => {
        if (!shopInfo) {
            setStompConfig({ shopId: '', token: '', userId: '' })
            return
        }
        const { shopId, token, userId } = shopInfo
        setStompConfig({ shopId: shopId, token, userId })
    }
    /**
     * 启动 stomp 连接
     */
    static start = () => {
        useAdminInfo().$subscribe((mutation, state) => {
            const { value: access_token, userId, shopId } = state.adminInfo
            StompStarter.setConfig({ token: access_token, userId, shopId })
        })
        const {
            value: access_token,
            additionalInformation: { userId, shopId },
        } = useAdminInfo().getterAdminInfo
        setStompConfig({ token: access_token, userId, shopId })
    }
}
