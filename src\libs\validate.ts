import { REGEX } from '@/constant'
/**
 * @LastEditors: lexy
 * @description: 日期
 */
const REGEX_DATE = (str: string) => REGEX.DATE.test(str)
/**
 * @LastEditors: lexy
 * @description: 时间
 */
const REGEX_TIME = (str: string) => REGEX.TIME.test(str)
/**
 * @LastEditors: lexy
 * @description: 日期 + 时间
 */
const REGEX_TIME_DATE = (str: string) => REGEX.TIME_DATE.test(str)
/**
 * @LastEditors: lexy
 * @description: 图片
 */
const REGEX_HTTP_URL = (str: string) => REGEX.HTTP_URL.test(str)
/**
 * @LastEditors: lexy
 * @description: 图片
 */
const REGEX_HTTPS_URL = (str: string) => REGEX.HTTP_URL.test(str)
/**
 * @LastEditors: lexy
 * @description: 数字
 */
const REXGEX_NUMBERS = (str: string) => REGEX.NUMBERS.test(str)
/**
 * @LastEditors: lexy
 * @description: 文本
 */
const REGEX_BLANK = (str: string) => REGEX.BLANK.test(str)
/**
 * @LastEditors: lexy
 * @description: 手机号
 */
const REGEX_MOBILE = (str: string) => REGEX.MOBILE.test(str)
/**
 * @LastEditors: lexy
 * @description: 公民身份证
 */
const REGEX_CITIZEN_ID = (idCard: string) => {
    // 基础格式验证
    const reg = REGEX.CITIZEN_ID
    if (!reg.test(idCard)) return false

    // 18位身份证校验位验证
    if (idCard.length === 18) {
        const idCardWi = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
        const idCardY = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
        const sum = [...idCard.slice(0, 17)].reduce((acc, cur, idx) => acc + parseInt(cur) * idCardWi[idx], 0)

        return idCardY[sum % 11] === idCard[17].toUpperCase()
    }

    return true
}
/**
 * @LastEditors: lexy
 * @description: 邮箱
 */
const REGEX_EMAIL = (str: string) => REGEX.EMAIL.test(str)
/**
 * @LastEditors: lexy
 * @description: 密码校验，6-20位密码，至少使用字母、数字、符号中的2种组合
 */
const REGEX_PASSWORD = (str: string) => REGEX.PASSWORD.test(str)
/**
 * @LastEditors: lexy
 * @description: 营业执照统一社会信用代码
 */
const REGEX_GROUP_CREDIT_CODE = (code: string) => {
    // 基础格式校验：18位字符串
    if (!code || !REGEX.GROUP_CREDIT_CODE.test(code)) {
        return false
    }

    // 定义权重
    const weights = [1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28]

    // 定义代码字符集
    const codes = '0123456789ABCDEFGHJKLMNPQRTUWXY'

    // 计算校验码
    let sum = 0
    for (let i = 0; i < 17; i++) {
        const char = code.charAt(i)
        const codeIndex = codes.indexOf(char)
        if (codeIndex === -1) {
            return false
        }
        sum += codeIndex * weights[i]
    }

    let checkCodeIndex = 31 - (sum % 31)
    if (checkCodeIndex === 31) {
        checkCodeIndex = 0
    }
    // 计算校验位
    const checkCode = codes.charAt(checkCodeIndex)
    // 比对校验位
    return checkCode === code.charAt(17)
}
export {
    REGEX_DATE,
    REGEX_TIME,
    REGEX_TIME_DATE,
    REGEX_HTTP_URL,
    REGEX_HTTPS_URL,
    REXGEX_NUMBERS,
    REGEX_BLANK,
    REGEX_MOBILE,
    REGEX_CITIZEN_ID,
    REGEX_EMAIL,
    REGEX_PASSWORD,
    REGEX_GROUP_CREDIT_CODE,
}
