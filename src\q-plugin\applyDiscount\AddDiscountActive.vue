<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-07-19 16:27:52
 * @LastEditors: lexy 
 * @LastEditTime: 2024-04-26 14:09:26
-->
<template>
    <q-plugin
        dev-url="http://*************:5173/"
        :context="{
            VueRouter: { useRouter, useRoute },
            DateUtil,
            UseConvert,
            QChooseGoodsPopup,
            QChooseGoodsPopupFun: { formatGoodsPrice },
            Request: { get },
            GoodAPI: { doGetRetrieveProduct },
            Decimal,
            DecimalInput,
        }"
        name="PlatformAddDiscountActive"
        service="addon-full-reduction"
    />
</template>

<script lang="ts" setup>
import QPlugin from '@/q-plugin/index.vue'
import { useRouter, useRoute } from 'vue-router'
import DateUtil from '@/utils/date'
import UseConvert from '@/composables/useConvert'
import QChooseGoodsPopup from '@/components/q-choose-goods-popup/q-choose-goods-popup.vue'
import { formatGoodsPrice } from '@/components/q-choose-goods-popup'
import { get } from '@/apis/http'
import Decimal from 'decimal.js'
import DecimalInput from '@/components/decimal-input/decimal-input.vue'
import { doGetRetrieveProduct } from '@/apis/good'
</script>

<style scoped></style>
