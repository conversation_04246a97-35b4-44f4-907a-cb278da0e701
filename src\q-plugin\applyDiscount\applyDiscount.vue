<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-07-19 16:27:52
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2024-04-25 11:01:33
-->
<template>
    <q-plugin
        dev-url="http://*************:5173/"
        :context="{
            VueUse: { useVModel },
            ElementPlusIconsVue: { Search },
            VueRouter: { useRouter },
            UseConvert,
            PageManage,
            Request: { post, get, put, del },
            ElementPlus: { ElMessageBox, ElMessage },
        }"
        name="PlatformAppluDiscount"
        service="addon-full-reduction"
    />
</template>

<script lang="ts" setup>
import QPlugin from '@/q-plugin/index.vue'
import { useVModel } from '@vueuse/core'
import { Search } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { post, get, put, del } from '@/apis/http'

import UseConvert from '@/composables/useConvert'
import PageManage from '@/components/PageManage.vue'
import { ElMessageBox, ElMessage } from 'element-plus'
</script>

<style scoped></style>
