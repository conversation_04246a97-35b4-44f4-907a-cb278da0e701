<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-07-19 16:27:52
 * @LastEditors: lexy 
 * @LastEditTime: 2024-04-26 09:27:33
-->
<template>
    <q-plugin
        dev-url="http://localhost:5173"
        :context="{
            VueRouter: { useRouter, useRoute },
            DateUtil,
            Request: { get, post, put, del },
            UseConvert,
            QInputNumber,
            DecimalInput,
            ElementPlus: { ElMessage, ElMessageBox },
        }"
        name="PlatformAddBargain"
        service="addon-bargain"
    />
</template>

<script lang="ts" setup>
import QPlugin from '@/q-plugin/index.vue'
import { useRouter, useRoute } from 'vue-router'
import { get, post, put, del } from '@/apis/http'
import DateUtil from '@/utils/date'
import QInputNumber from '@/components/q-input-number/q-input-number.vue'
import UseConvert from '@/composables/useConvert'
import { ElMessage, ElMessageBox } from 'element-plus'
import DecimalInput from '@components/decimal-input/decimal-input.vue'
</script>

<style scoped></style>
