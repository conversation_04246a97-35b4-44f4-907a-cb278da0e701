<template>
    <q-plugin
        dev-url="http://localhost:5173"
        :context="{
            UseConvert,
            VueRouter: { useRoute, useRouter },
            VueUse: { useVModel },
            ElementPlusIconsVue: { Search },
            PageManageTwo,
            DecimalInput,
            Request: { get, post, put, del, patch },
            ElementPlus: { ElMessage, ElMessageBox },
        }"
        name="PlatformBargain"
        service="addon-bargain"
    />
</template>

<script lang="ts" setup>
import QPlugin from '@/q-plugin/index.vue'
import UseConvert from '@/composables/useConvert'
import { useRoute, useRouter } from 'vue-router'
import { useVModel } from '@vueuse/core'
import { ElementPlus, Search } from '@element-plus/icons-vue'
import PageManageTwo from '@/components/PageManage.vue'
import { get, post, put, del, patch } from '@/apis/http'
import { ElMessage, ElMessageBox } from 'element-plus'
import DecimalInput from '@/components/decimal-input/decimal-input.vue'
</script>

<style lang="scss" scoped></style>
