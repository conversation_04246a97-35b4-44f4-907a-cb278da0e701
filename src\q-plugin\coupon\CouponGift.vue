<script lang="ts" setup>
import { nextTick } from 'vue'
import QPlugin from '@/q-plugin/index.vue'
import { useVModel } from '@vueuse/core'
import useConvert from '@/composables/useConvert'
import * as Request from '@/apis/http'
import DateUtil from '@/utils/date'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import DecimalInput from '@/components/decimal-input/decimal-input.vue'

const userIdsStr = useRoute().query.ids
const router = useRouter()
if (!userIdsStr) {
    nextTick(() => router.back())
}
const userIds = JSON.parse(userIdsStr)
</script>
<template>
    <q-plugin dev-url="http://localhost:5173" :context="{
        VueUse: { useVModel },
        Request,
        DateUtil,
        UseConvert: useConvert,
        DecimalInput,
        ElementPlus: { ElMessageBox, ElMessage },
    }" :properties="{ userIds, productType: 'ALL', toBack: () => router.back() }" name="CouponGift"
        service="addon-coupon" />
</template>
