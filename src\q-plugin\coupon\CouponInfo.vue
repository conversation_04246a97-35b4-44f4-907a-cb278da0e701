<script lang="ts" setup>
import QPlugin from '@/q-plugin/index.vue'
import useConvert from '@/composables/useConvert'
import * as Request from '@/apis/http'
import DateUtil from '@/utils/date'
import { useRouter, useRoute } from 'vue-router'
import { useVModel } from '@vueuse/core'
import { doGetRetrieveProduct } from '@/apis/good'
import DecimalInput from '@/components/decimal-input/decimal-input.vue'
</script>
<template>
    <q-plugin
        dev-url="http://localhost:5173"
        :context="{
            Request,
            DateUtil,
            DecimalInput,
            UseConvert: useConvert,
            VueRouter: { useRouter, useRoute },
            VueUse: { useVModel },
            GoodAPI: { doGetRetrieveProduct },
        }"
        name="PlatformCouponInfo"
        service="addon-coupon"
    />
</template>
