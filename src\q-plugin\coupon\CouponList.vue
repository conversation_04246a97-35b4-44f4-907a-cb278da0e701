<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-09-18 10:04:22
 * @LastEditors: lexy 
 * @LastEditTime: 2024-04-28 11:15:59
-->
<script lang="ts" setup>
import QPlugin from '@/q-plugin/index.vue'
import { useVModel } from '@vueuse/core'
import UseConvert from '@/composables/useConvert'
import * as Request from '@/apis/http'
import DateUtil from '@/utils/date'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { Search } from '@element-plus/icons-vue'
import PageManage from '@/components/PageManage.vue'
</script>
<template>
    <q-plugin
        dev-url="http://localhost:5173"
        :context="{
            VueUse: { useVModel },
            VueRouter: { useRoute, useRouter },
            Request,
            DateUtil,
            UseConvert,
            ElementPlus: { ElMessageBox, ElMessage },
            ElementPlusIconsVue: { Search },
            PageManage,
        }"
        service="addon-coupon"
        name="PlatformCouponList"
    />
</template>
