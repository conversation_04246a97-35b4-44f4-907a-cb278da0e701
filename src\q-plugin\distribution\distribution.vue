<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-07-19 16:27:52
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-16 18:46:20
-->
<template>
    <q-plugin
        :context="{
            UseConvert,
            Request: { post, get, put },
            PageManageTwo: PageManage,
            MCard,
            DateUtil,
            ElementPlusIconsVue: { ArrowDownBold },
            VueClipboard3,
            Decimal,
            QDropDown,
            QTable,
            QTableColumn,
            QEditor,
            DecimalInput,
            ElementPlus: { ElMessageBox, ElMessage },
        }"
        dev-url="http://*************:5173/"
        name="PlatformDistribute"
        service="addon-distribute"
    />
</template>

<script lang="ts" setup>
import { post, get, put } from '@/apis/http'
import { ArrowDownBold } from '@element-plus/icons-vue'
import { ElMessageBox, ElMessage } from 'element-plus'

import DateUtil from '@/utils/date'
import Decimal from 'decimal.js'
import UseConvert from '@/composables/useConvert'

import VueClipboard3 from 'vue-clipboard3'
import QPlugin from '@/q-plugin/index.vue'
import PageManage from '@/components/PageManage.vue'
import MCard from '@/components/MCard.vue'
import QTable from '@/components/qszr-core/packages/q-table/QTable'
import QTableColumn from '@/components/qszr-core/packages/q-table/q-table-column.vue'
import QEditor from '@/components/q-editor/q-edit.vue'
import QDropDown from '@/components/qszr-core/packages/q-drop-down'
import DecimalInput from '@/components/decimal-input/decimal-input.vue'
</script>

<style scoped></style>
