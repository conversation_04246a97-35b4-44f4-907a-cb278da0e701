<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-07-19 16:27:52
 * @LastEditors: lexy 
 * @LastEditTime: 2024-04-26 14:38:20
-->
<template>
    <q-plugin
        dev-url="http://*************:5173"
        :context="{
            VueRouter: { useRoute, useRouter },
            UtilsHttp: { http },
            GoodAPI: {
                doGetRetrieveProduct,
            },
            UseConvert,
            DecimalInput,
            ElementPlus: {
                ElMessage,
            },
        }"
        name="PlatformGroupDetail"
        service="addon-team"
    />
</template>

<script lang="ts" setup>
import QPlugin from '@/q-plugin/index.vue'
import { useRoute, useRouter } from 'vue-router'
import { http } from '@/utils/http'
import { doGetRetrieveProduct } from '@/apis/good'
import UseConvert from '@/composables/useConvert'
import { ElMessage } from 'element-plus'
import DecimalInput from '@/components/decimal-input/decimal-input.vue'
</script>

<style scoped></style>
