<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-07-19 16:27:52
 * @LastEditors: lexy 
 * @LastEditTime: 2024-04-26 14:38:12
-->
<template>
    <q-plagin
        dev-url="http://*************:5173"
        :context="{
            UseConvert,
            VueRouter: { useRouter, useRoute },
            VueUse: { useVModel },
            ElementPlusIconsVue: { Search },
            PageManageTwo,
            UtilsHttp: { http },
            ElementPlus: { ElMessageBox, ElMessage },
        }"
        name="PlatformGroup"
        service="addon-team"
    />
</template>

<script lang="ts" setup>
import QPlagin from '@/q-plugin/index.vue'
import UseConvert from '@/composables/useConvert'
import { useRouter, useRoute } from 'vue-router'
import { useVModel } from '@vueuse/core'
import { Search } from '@element-plus/icons-vue'
import PageManageTwo from '@/components/PageManage.vue'
import { http } from '@/utils/http'
import { ElMessageBox, ElMessage } from 'element-plus'
</script>

<style scoped></style>
