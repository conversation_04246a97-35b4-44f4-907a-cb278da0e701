<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-07-19 16:27:52
 * @LastEditors: lexy
 * @LastEditTime: 2023-09-15 17:47:55
-->
<template>
    <q-plugin
        :context="{
            VueRouter: { useRoute, useRouter },
            UseConvert,
            Request: { get, del, post, put },
            ElementPlusIconsVue: { CircleClose },
            VueDraggableNext: { VueDraggableNext },
            QUpload,
            ElementPlus: { ElMessageBox, ElMessage },
            QEdit,
            selectMaterial,
            ExampleImg,
            DecimalInput,
        }"
        :dev-url="'http://localhost:5173'"
        name="AddIntegralGoods"
        service="addon-integral"
    />
</template>

<script lang="ts" setup>
import QPlugin from '@/q-plugin/index.vue'
import { useRoute, useRouter } from 'vue-router'
import UseConvert from '@/composables/useConvert'
import { get, del, post, put } from '@/apis/http'
import { CircleClose } from '@element-plus/icons-vue'
import { VueDraggableNext } from 'vue-draggable-next'
import QUpload from '@/components/q-upload/q-upload.vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import QEdit from '@/components/q-editor/q-edit.vue'
import selectMaterial from '@/views/material/selectMaterial.vue'
import ExampleImg from '@/assets/image/ex_img.png'
import DecimalInput from '@/components/decimal-input/decimal-input.vue'
</script>

<style scoped></style>
