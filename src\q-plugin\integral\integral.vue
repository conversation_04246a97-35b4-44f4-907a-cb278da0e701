<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-07-19 16:27:52
 * @LastEditors: lexy
 * @LastEditTime: 2023-09-18 15:09:10
-->
<template>
    <q-plugin
        :dev-url="'http://localhost:5173'"
        :context="{
            VueRouter: { useRoute, useRouter },
            UseConvert,
            Request: { get, post, del, put },
            ElementPlusIconsVue: { Search, ArrowDown, WarningFilled },
            PageManageTwo,
            QTable,
            QTableColumn,
            PlatformGoodStatus: { usePlatformGoodStatus },
            VueClipboard3,
            Lodash,
            VueUse: { useVModel },
            DateUtil,
            RemarkFlag,
            RemarkPopup,
            MCard,
            QAddress,
            EditorTwo,
            PlatformComp,
            ElementPlus: { ElMessageBox, ElMessage },
        }"
        name="Integral"
        service="addon-integral"
    />
</template>

<script lang="ts" setup>
import QPlugin from '@/q-plugin/index.vue'
import { useRoute, useRouter } from 'vue-router'
import UseConvert from '@/composables/useConvert'
import { get, post, del, put } from '@/apis/http'
import { Search, ArrowDown, WarningFilled } from '@element-plus/icons-vue'
import PageManageTwo from '@/components/PageManage.vue'
import QTable from '@/components/qszr-core/packages/q-table/QTable'
import QTableColumn from '@/components/qszr-core/packages/q-table/q-table-column.vue'
import { usePlatformGoodStatus } from '@/composables/usePlatformGoodStatus'
import VueClipboard3 from 'vue-clipboard3'
import Lodash from 'lodash'
import { useVModel } from '@vueuse/core'
import DateUtil from '@/utils/date'
import RemarkFlag from '@/components/remark/remark-flag.vue'
import RemarkPopup from '@/components/remark/remark-popup.vue'
import QAddress from '@/components/q-address/q-address.vue'
import MCard from '@/components/MCard.vue'
import EditorTwo from '@/components/q-editor/editor.vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import PlatformComp from '@/views/order/platform.vue'
</script>

<style scoped></style>
