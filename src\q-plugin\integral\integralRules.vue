<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-07-19 16:27:52
 * @LastEditors: lexy
 * @LastEditTime: 2023-09-20 11:27:40
-->
<template>
    <q-plugin
        dev-url="http://*************:5173"
        :context="{
            VueRouter: { useRouter },
            ElementPlus: { ElMessage },
            Editor,
            Request: { get, post, put, del },
        }"
        name="IntegralRules"
        service="addon-integral"
    />
</template>

<script lang="ts" setup>
import QPlugin from '@/q-plugin/index.vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { get, post, put, del } from '@/apis/http'
import Editor from '@/components/q-editor/editor.vue'
</script>

<style scoped></style>
