/*
 * @description:
 * @Author: lexy
 * @Date: 2022-11-21 15:07:49
 * @LastEditors: lexy
 * @LastEditTime: 2022-11-22 10:57:12
 */
import { get, post, put, del, patch } from '@/apis/http'
import { BASE_URL } from '@/q-plugin/liveStream/apis'
/**
 * @LastEditors: lexy
 * @description: 平台查询商品列表
 * @param {any} auditStatus 商品状态
 * @param {any} productName 商品名称
 * @returns {*}
 */
export const doGetLiveGoodsList = (data?: any) => {
    return get({
        url: BASE_URL + 'liveGoods',
        params: data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 删除直播商品
 * @param {string} goodsIds
 * @returns {*}
 */
export const doDelDeleteGoods = (goodsIds: string[]) => {
    return del({
        url: BASE_URL + `delete/goods/${goodsIds}`,
    })
}
