/*
 * @description:
 * @Author: lexy
 * @Date: 2022-08-18 16:05:08
 * @LastEditors: lexy
 * @LastEditTime: 2023-01-30 16:05:30
 */
import { get, post, put, del, patch } from '@/apis/http'
export const BASE_URL = 'gruul-mall-live/platform/live/'
/**
 * @LastEditors: lexy
 * @description: 平台查询直播列表
 * @param {any} data
 * @returns {*}
 */
export const doGetLiveList = (data?: any) => {
    return get({
        url: BASE_URL + 'liveRoom',
        params: data,
    })
}
/**
 * @LastEditors: lexy
 * @description: 分享直播间
 * @param {any} data
 * @returns {*}
 */
export const doGetShareLiveRoom = (roomId: string) => {
    return get({
        url: BASE_URL + '/share/live/room',
        params: { roomId },
    })
}
/**
 * @LastEditors: lexy
 * @description: 删除直播间
 * @param {string} roomIds
 * @returns {*}
 */
export const doDelDeleteRoom = (roomIds: string[]) => {
    return del({
        url: BASE_URL + `liveRoom/${roomIds}`,
    })
}

/**
 * @LastEditors: lexy
 * @description: 获取直播间角色列表
 * @param {string} username
 * @returns {*}
 */
export const doGetLiveMemberList = (params: any) => {
    return get({
        url: BASE_URL + `get/liveUser`,
        params,
    })
}
/**
 * @LastEditors: lexy
 * @description: 删除直播间角色
 * @param {string} ids
 * @returns {*}
 */
export const doDelDeleteLiveUser = (roomUserIds: any) => {
    return del({
        url: BASE_URL + `delete/liveUser/${roomUserIds}`,
    })
}
