<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-11-15 10:44:30
 * @LastEditors: lexy
 * @LastEditTime: 2022-11-16 15:01:27
-->
<script setup lang="ts">
import { ref, reactive, defineAsyncComponent } from 'vue'
type ActiveNameType = 'STUDIO' | 'LIVE_GOODS' | 'LIVE_MEMBER'
/*
 *variable
 */
const tabList = [
    {
        label: '直播间',
        name: 'STUDIO',
    },
    {
        label: '直播商品',
        name: 'LIVE_GOODS',
    },
    {
        label: '直播成员',
        name: 'LIVE_MEMBER',
    },
]
const activeName = ref<ActiveNameType>('STUDIO')
const defineAsyncComponentReactive = reactive({
    STUDIO: defineAsyncComponent(() => import('@/q-plugin/liveStream/views/components/studio-list/studio-list.vue')),
    LIVE_GOODS: defineAsyncComponent(() => import('@/q-plugin/liveStream/views/components/live-goods/live-goods.vue')),
    LIVE_MEMBER: defineAsyncComponent(() => import('@/q-plugin/liveStream/views/components/live-member/live-member.vue')),
})
/*
 *lifeCircle
 */
/*
 *function
 */

const handleClick = (tab: ActiveNameType) => {}
</script>

<template>
    <el-tabs v-model="activeName" @tab-change="handleClick">
        <el-tab-pane v-for="item in tabList" :key="item.label" :label="item.label" :name="item.name" />
    </el-tabs>
    <component :is="defineAsyncComponentReactive[activeName]" />
</template>

<style scoped lang="scss"></style>
