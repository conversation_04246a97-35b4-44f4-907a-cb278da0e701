<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-11-15 11:26:22
 * @LastEditors: lexy
 * @LastEditTime: 2023-01-12 14:06:58
-->
<script setup lang="ts">
import HeadSearch from '@/q-plugin/liveStream/views/components/studio-list/head-search.vue'
import PageManage from '@/components/PageManage.vue'
import { doGetLiveGoodsList, doDelDeleteGoods } from '@/q-plugin/liveStream/apis/goods'
import { ElMessage, ElMessageBox } from 'element-plus'
import { GOODS_TYPE_CN } from '@/q-plugin/liveStream/views/components/live-goods'
import type { ApiGoodsItem } from '@/q-plugin/liveStream/views/components/live-goods/types'
/*
 *variable
 */
const { divTenThousand } = useConvert()
const searchParams = ref({
    type: '',
    keywords: '',
    status: '',
})
const pageConfig = reactive({
    size: 10,
    current: 1,
    total: 0,
})
const goodsList = ref<ApiGoodsItem[]>([])
const chooseList = ref<ApiGoodsItem[]>([])
const addGoodsShow = ref(false)
/*
 *lifeCircle
 */
/*
 *function
 */
watch(
    () => searchParams.value.status,
    () => {
        // 搜索数据
        initGoodsList()
    },
)
const handleBatchDel = async () => {
    if (!chooseList.value.length) {
        ElMessage.info('请选择需要删除的直播商品')
        return
    }
    const isValidate = await ElMessageBox.confirm('确定进行删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
    if (!isValidate) return
    const ids = chooseList.value.map((item) => item.goodsId)
    const { code, data } = await doDelDeleteGoods(ids)
    if (code !== 200) {
        ElMessage.error('删除失败')
        return
    }
    ElMessage.success('删除成功')
    initGoodsList()
}
const handleSearch = () => {
    initGoodsList()
}
async function initGoodsList() {
    const { keywords, status: auditStatus } = searchParams.value
    const params = { ...pageConfig, keywords, auditStatus }
    const { code, data } = await doGetLiveGoodsList(params)
    if (code !== 200) return ElMessage.error('获取商品列表失败')
    goodsList.value = data.records
    pageConfig.current = data.current
    pageConfig.size = data.size
    pageConfig.total = data.total
}

/**
 * @LastEditors: lexy
 * @description: 删除
 * @returns {*}
 */
const handleDelClick = async (row: ApiGoodsItem) => {
    try {
        const isValidate = await ElMessageBox.confirm('确定删除该商品?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })
        if (!isValidate) return
        console.log('row.goodsId', row)
        const { code, data } = await doDelDeleteGoods([row.goodsId])
        if (code !== 200) {
            ElMessage.error('删除失败')
            return
        }
        ElMessage.success('删除成功')
        goodsList.value = goodsList.value.filter((item) => item.id !== row.id)
        pageConfig.total--
    } catch (error) {
        error
    }
}
/**
 * @LastEditors: lexy
 * @description: 分页器
 * @param {*} value
 * @returns {*}
 */
const handleSizeChange = (value: number) => {
    pageConfig.current = 1
    pageConfig.size = value
    initGoodsList()
}
const handleCurrentChange = (value: number) => {
    pageConfig.current = value
    initGoodsList()
}
</script>

<template>
    <div class="handle_container" style="padding-top: 16px">
        <head-search v-model="searchParams" types="GOODS" @batch-del="handleBatchDel" @search="handleSearch" @add="addGoodsShow = true" />
    </div>
    <el-table
        ref="multipleTableRef"
        :data="goodsList"
        stripe
        height="calc(100vh - 280px)"
        :header-row-style="{ fontSize: '12px', color: '#909399' }"
        :header-cell-style="{ background: '#f6f8fa' }"
        :cell-style="{ fontSize: '12px', color: '#333333' }"
        @selection-change="chooseList = $event"
    >
        <el-table-column type="selection" width="55" />
        <el-table-column label="店铺名称" width="150">
            <template #default="{ row }: { row: ApiGoodsItem }">
                <span class="shop-name" :title="row.shopName">{{ row.shopName }}</span>
            </template>
        </el-table-column>
        <el-table-column label="商品信息" width="350">
            <template #default="{ row }: { row: ApiGoodsItem }">
                <div class="goods">
                    <el-image style="width: 40px; height: 40px" :src="row.ossImgUrl" />
                    <div class="goods__info">
                        <div class="goods__info--name" :title="row.productName">
                            {{ row.productName }}
                        </div>
                        <div v-if="row.priceType === 1" class="goods__info--price">{{ row.price && divTenThousand(row.price).toFixed(2) }}</div>
                        <div v-else-if="row.priceType === 2" class="goods__info--price">
                            {{ row.price && divTenThousand(row.price).toFixed(2) }}
                            ~ ￥{{ row.price2 && divTenThousand(row.price2).toFixed(2) }}
                        </div>
                        <div v-else class="goods__info--price">
                            市场价：{{ row.price && divTenThousand(row.price).toFixed(2) }} ￥现价：{{
                                row.price2 && divTenThousand(row.price2).toFixed(2)
                            }}
                        </div>
                    </div>
                </div>
            </template>
        </el-table-column>
        <el-table-column label="状态">
            <template #default="{ row }: { row: ApiGoodsItem }">
                <span :style="{ color: row.auditStatus === 'FAILED_APPROVED' ? '#F12F22' : '' }">{{ GOODS_TYPE_CN[row.auditStatus] }}</span>
            </template>
        </el-table-column>
        <el-table-column label="添加时间" align="center" width="200px">
            <template #default="{ row }: { row: ApiGoodsItem }">
                <div>{{ row.createTime }}</div>
            </template>
        </el-table-column>
        <el-table-column label="提交审核时间" align="center" width="200px">
            <template #default="{ row }: { row: ApiGoodsItem }">
                <span>{{ row.verifyTime }}</span>
            </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" align="center">
            <template #default="{ row }: { row: ApiGoodsItem }">
                <el-link
                    style="padding: 0 5px"
                    :underline="false"
                    type="primary"
                    :disabled="row.auditStatus === 'UNDER_REVIEW'"
                    size="small"
                    @click="handleDelClick(row)"
                >
                    删除
                </el-link>
            </template>
        </el-table-column>
    </el-table>
    <el-row justify="end" align="middle">
        <!-- 好用的分页器 -->
        <page-manage
            v-model="pageConfig"
            :load-init="true"
            :page-size="pageConfig.size"
            :total="pageConfig.total"
            @reload="initGoodsList"
            @handle-size-change="handleSizeChange"
            @handle-current-change="handleCurrentChange"
        />
    </el-row>
    <!-- <AddGoods v-model="addGoodsShow" /> -->
</template>

<style scoped lang="scss">
@include b(shop-name) {
    display: inline-block;
    width: 100px;
    @include utils-ellipsis(1);
}
@include b(goods) {
    width: 310px;
    display: flex;
    justify-content: flex-start;
    @include e(info) {
        padding: 0 0 0 10px;
        width: 250px;
        line-height: 20px;
        color: #515151;
        font-size: 12px;
        @include m(name) {
            width: 65%;
            @include utils-ellipsis(1);
        }
        @include m(price) {
            &::before {
                content: '￥';
            }
        }
    }
}
</style>
