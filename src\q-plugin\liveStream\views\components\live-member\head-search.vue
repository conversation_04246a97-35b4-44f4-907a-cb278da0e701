<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-11-15 11:43:17
 * @LastEditors: lexy
 * @LastEditTime: 2022-11-16 15:38:37
-->
<script setup lang="ts">
import { PropType } from 'vue'
import SelectLiveType from '@/q-plugin/liveStream/components/select-live-type.vue'
import { Search } from '@element-plus/icons-vue'
import { useVModel } from '@vueuse/core'
export interface Operation {
    keywords: string
}
/*
 *variable
 */
const props = defineProps({
    modelValue: {
        type: Object as PropType<Operation>,
        default() {
            return {}
        },
    },
    batchDisabled: {
        type: Boolean,
        default: true,
    },
    leftBtnText: {
        type: String,
        default: '新增商品',
    },
})

const emit = defineEmits(['update:modelValue', 'batchDel', 'add', 'search'])
const _modelValue = useVModel(props, 'modelValue', emit)
</script>

<template>
    <el-row :gutter="24" justify="space-between" style="margin-bottom: 15px; width: 100%">
        <el-col :span="14">
            <el-button round type="primary" plain @click="emit('batchDel')">批量删除</el-button>
        </el-col>
        <el-col :span="6">
            <el-input v-model="_modelValue.keywords" placeholder="输入关键词" style="width: 100%" @keypress.enter="emit('search')">
                <template #append>
                    <el-button :icon="Search" @click="emit('search')" />
                </template>
            </el-input>
        </el-col>
    </el-row>
</template>

<style scoped lang="scss"></style>
