<script setup lang="ts">
import { ref, reactive } from 'vue'
import HeadSearch from '@/q-plugin/liveStream/views/components/live-member/head-search.vue'
import PageManage from '@/components/PageManage.vue'
import { doGetLiveMemberList, doDelDeleteLiveUser } from '@/q-plugin/liveStream/apis'
import { role } from '@/q-plugin/liveStream/views'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { ApiRole } from '@/q-plugin/liveStream/views/components/live-member/types'
/*
 *variable
 */
const searchParams = ref({
    keywords: '',
})
const addMemberShow = ref(false)
const roleList = ref<ApiRole[]>([])
const multipleTableRef = ref()
const pageConfig = reactive({
    size: 10,
    current: 1,
    total: 0,
})
const chooseList = ref<ApiRole[]>([])

/*
 *lifeCircle
 */
/*
 *function
 */
/**
 * @LastEditors: lexy
 * @description: 批量删除
 * @returns {*}
 */
const handleBatchDel = async () => {
    if (!chooseList.value.length) {
        ElMessage.info('请选择需要删除的角色')
        return
    }
    const isValidate = await ElMessageBox.confirm('确定进行删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
    if (!isValidate) return
    const ids = chooseList.value.map((item) => item.id)
    const { code, data } = await doDelDeleteLiveUser(ids)
    if (code !== 200) {
        ElMessage.error('删除失败')
        return
    }
    ElMessage.success('删除成功')
    initMemberList()
}
/**
 * @LastEditors: lexy
 * @description: 搜索
 * @returns {*}
 */
const handleSearch = () => {
    initMemberList()
}
async function initMemberList() {
    const { keywords } = searchParams.value
    const params = { ...pageConfig, keywords }
    const { code, data } = await doGetLiveMemberList(params)
    if (code !== 200) return ElMessage.error('获取角色列表失败')
    roleList.value = data.records
    pageConfig.current = data.current
    pageConfig.size = data.size
    pageConfig.total = data.total
}
/**
 * @LastEditors: lexy
 * @description: 单个删除
 * @param {*} id
 * @returns {*}
 */
const handleDelClick = async (row: ApiRole) => {
    const isValidate = await ElMessageBox.confirm('确定删除该角色?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
    if (!isValidate) return
    const { code, data } = await doDelDeleteLiveUser([row.id])
    if (code !== 200) {
        ElMessage.error('删除失败')
        return
    }
    ElMessage.success('删除成功')
    roleList.value = roleList.value.filter((item) => item.id !== row.id)
    pageConfig.total--
}
/**
 * @LastEditors: lexy
 * @description: 分页器
 * @param {*} value
 * @returns {*}
 */
const handleSizeChange = (value: number) => {
    pageConfig.current = 1
    pageConfig.size = value
    initMemberList()
}
const handleCurrentChange = (value: number) => {
    pageConfig.current = value
    initMemberList()
}
</script>

<template>
    <div class="handle_container" style="padding-top: 16px">
        <head-search v-model="searchParams" left-btn-text="新增成员" @batch-del="handleBatchDel" @search="handleSearch" @add="addMemberShow = true" />
    </div>
    <el-table
        ref="multipleTableRef"
        :data="roleList"
        stripe
        height="calc(100vh - 280px)"
        :header-row-style="{ fontSize: '12px', color: '#909399' }"
        :header-cell-style="{ background: '#f6f8fa' }"
        :cell-style="{ fontSize: '12px', color: '#333333' }"
        @selection-change="chooseList = $event"
    >
        <el-table-column type="selection" width="55" />
        <el-table-column label="店铺名称">
            <template #default="{ row }: { row: ApiRole }">
                <div class="name">{{ row.shopName }}</div>
            </template>
        </el-table-column>
        <el-table-column label="用户昵称">
            <template #default="{ row }: { row: ApiRole }">
                <span>{{ row.userName }}</span>
            </template>
        </el-table-column>
        <el-table-column label="微信号" align="center">
            <template #default="{ row }: { row: ApiRole }">
                <div>{{ row.wechatNumber }}</div>
            </template>
        </el-table-column>
        <el-table-column label="角色" align="center">
            <template #default="{ row }: { row: ApiRole }">
                <span>{{ role[row.role] }}</span>
            </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center">
            <template #default="{ row }: { row: ApiRole }">
                <span>{{ row.createTime }}</span>
            </template>
        </el-table-column>
        <el-table-column prop="address" label="操作" fixed="right" width="150" align="center">
            <template #default="{ row }: { row: ApiRole }">
                <el-link style="padding: 0 5px" :underline="false" type="primary" size="small" @click="handleDelClick(row)">删除</el-link>
            </template>
        </el-table-column>
    </el-table>
    <el-row justify="end" align="middle">
        <!-- 好用的分页器 -->
        <page-manage
            v-model="pageConfig"
            :load-init="true"
            :page-size="pageConfig.size"
            :total="pageConfig.total"
            @reload="initMemberList"
            @handle-size-change="handleSizeChange"
            @handle-current-change="handleCurrentChange"
        />
    </el-row>
</template>

<style scoped lang="scss">
@include b(name) {
    width: 120px;
    @include utils-ellipsis;
}
</style>
