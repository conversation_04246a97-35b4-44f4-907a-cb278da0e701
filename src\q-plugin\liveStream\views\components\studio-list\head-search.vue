<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-11-15 11:43:17
 * @LastEditors: lexy
 * @LastEditTime: 2022-12-13 14:52:11
-->
<script setup lang="ts">
import { PropType } from 'vue'
import SelectLiveType from '@/q-plugin/liveStream/components/select-live-type.vue'
import { Search } from '@element-plus/icons-vue'
import { GOODS_TYPE_CN } from '@/q-plugin/liveStream/views/components/live-goods'
import { liveIndexStatus } from '@/q-plugin/liveStream/views'
import { useVModel } from '@vueuse/core'
export interface Operation {
    status: string
    type: string
    keywords: string
}
/*
 *variable
 */
const props = defineProps({
    modelValue: {
        type: Object as PropType<Operation>,
        default() {
            return {}
        },
    },
    batchDisabled: {
        type: Boolean,
        default: true,
    },
    title: {
        type: String,
        default: '新增直播间',
    },
    types: {
        type: String as PropType<'LIST' | 'GOODS'>,
        default: 'LIST',
    },
})

const emit = defineEmits(['update:modelValue', 'batchDel', 'add', 'search'])
const _modelValue = useVModel(props, 'modelValue', emit)
</script>

<template>
    <el-row :gutter="24" justify="space-between" style="margin-bottom: 15px">
        <el-col :span="14">
            <el-button round type="primary" plain @click="emit('batchDel')">批量删除</el-button>
        </el-col>
        <el-col :span="9">
            <el-space>
                <select-live-type v-model="_modelValue.status" :list="types === 'LIST' ? liveIndexStatus : GOODS_TYPE_CN">
                    <el-option label="全部状态" :value="''" />
                </select-live-type>
            </el-space>
            <el-input v-model="_modelValue.keywords" placeholder="输入关键词" style="width: 55%" @keypress.enter="emit('search')">
                <template #append>
                    <el-button :icon="Search" @click="emit('search')" />
                </template>
            </el-input>
        </el-col>
    </el-row>
</template>

<style scoped lang="scss"></style>
