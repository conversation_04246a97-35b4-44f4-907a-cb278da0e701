<script setup lang="ts">
import { ref, reactive, computed, watch, PropType, defineExpose } from 'vue'
import HeadSearch from '@/q-plugin/liveStream/views/components/studio-list/head-search.vue'
import PageManage from '@/components/PageManage.vue'
import { doGetLiveList, doDelDeleteRoom, doGetShareLiveRoom } from '@/q-plugin/liveStream/apis'
import { ElMessage, ElMessageBox } from 'element-plus'
import { liveIndexStatus, formatTime_S } from '@/q-plugin/liveStream/views'
import Decimal from 'decimal.js'
import type { ApiRoomItem } from '@/q-plugin/liveStream/views/types'
/*
 *variable
 */
const searchParams = ref({
    type: '',
    keywords: '',
    status: '',
})
const dataSrc = ref('')
const studioList = ref<ApiRoomItem[]>([])
const multipleTableRef = ref()
// 分享弹窗
const sharePopupShow = ref(false)
const pageConfig = reactive({
    size: 10,
    current: 1,
    total: 0,
})
const chooseList = ref<ApiRoomItem[]>([])
/*
 *lifeCircle
 */
watch(
    () => searchParams.value.status,
    () => {
        // 搜索数据
        initStodioList()
    },
    {
        deep: true,
    },
)
/*
 *function
 */
/**
 * @LastEditors: lexy
 * @description: 批量删除
 * @returns {*}
 */
const handleBatchDel = async () => {
    if (!chooseList.value.length) {
        ElMessage.info('请选择需要删除的直播间')
        return
    }
    const isValidate = await ElMessageBox.confirm('确定进行删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
    if (!isValidate) return
    const ids = chooseList.value.map((item) => item.wechatRoomId)
    const { code, data } = await doDelDeleteRoom(ids)
    if (code !== 200) {
        ElMessage.error('删除失败')
        return
    }
    initStodioList()
    ElMessage.success('删除成功')
}
/**
 * @LastEditors: lexy
 * @description: 搜索
 * @returns {*}
 */
const handleSearch = () => {
    initStodioList()
}
async function initStodioList() {
    const { keywords, status: roomStatus } = searchParams.value
    const params = { ...pageConfig, keywords, roomStatus }
    const { code, data } = await doGetLiveList(params)
    if (code !== 200) return ElMessage.error('获取直播间列表失败')
    studioList.value = data.records
    pageConfig.current = data.current
    pageConfig.size = data.size
    pageConfig.total = data.total
}
/**
 * @LastEditors: lexy
 * @description: 单个删除
 * @param {*} id
 * @returns {*}
 */
const handleDelClick = async (row: any) => {
    try {
        const isValidate = await ElMessageBox.confirm('确定删除该直播间?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })
        if (!isValidate) return
        const { code, data } = await doDelDeleteRoom([row.wechatRoomId])
        if (code !== 200) {
            ElMessage.error('删除失败')
            return
        }
        ElMessage.success('删除成功')
        studioList.value = studioList.value.filter((item) => item.id !== row.id)
        pageConfig.total--
    } catch (error) {
        error
    }
}
/**
 * @LastEditors: lexy
 * @description: 分享直播间
 * @param {*} row
 * @returns {*}
 */
const handleShare = async (row: ApiRoomItem) => {
    const { code, data, msg } = await doGetShareLiveRoom(row.wechatRoomId)
    if (code !== 200) {
        ElMessage.error(msg)
        return
    }
    dataSrc.value = data
    sharePopupShow.value = true
}
const handleClose = () => {
    dataSrc.value = ''
    sharePopupShow.value = false
}
const fromatTimeM = (time: string) => {
    if (!time) {
        return ''
    }
    return formatTime_S(new Decimal(time).mul(1000).toNumber())
    //将时间戳格式转换成年月日时分秒
}
/**
 * @LastEditors: lexy
 * @description: 分页器
 * @param {*} value
 * @returns {*}
 */
const handleSizeChange = (value: number) => {
    console.log('size', value)
    pageConfig.current = 1
    pageConfig.size = value
    initStodioList()
}
const handleCurrentChange = (value: number) => {
    pageConfig.current = value
    initStodioList()
}
</script>

<template>
    <div class="handle_container" style="padding-top: 16px">
        <head-search v-model="searchParams" @batch-del="handleBatchDel" @search="handleSearch" />
    </div>
    <el-table
        ref="multipleTableRef"
        :data="studioList"
        stripe
        height="calc(100vh - 280px)"
        :header-row-style="{ fontSize: '12px', color: '#909399' }"
        :header-cell-style="{ background: '#f6f8fa' }"
        :cell-style="{ fontSize: '12px', color: '#333333' }"
        @selection-change="chooseList = $event"
    >
        <el-table-column type="selection" width="55" />
        <el-table-column label="店铺名称" width="140">
            <template #default="{ row }: { row: ApiRoomItem }">
                <div class="name">
                    {{ row.shopName }}
                </div>
            </template>
        </el-table-column>
        <el-table-column label="直播名称" width="140">
            <template #default="{ row }: { row: ApiRoomItem }">
                <div class="name">
                    {{ row.roomName }}
                </div>
            </template>
        </el-table-column>
        <el-table-column label="主播名称" align="center" width="140">
            <template #default="{ row }: { row: ApiRoomItem }">
                <div class="name">
                    {{ row.anchorName }}
                </div>
            </template>
        </el-table-column>
        <el-table-column label="主播微信号" align="center" width="140">
            <template #default="{ row }: { row: ApiRoomItem }">
                <span>{{ row.wechatNumber }}</span>
            </template>
        </el-table-column>
        <el-table-column label="开播时间" width="300" align="center">
            <template #default="{ row }: { row: ApiRoomItem }">
                <span>{{ fromatTimeM(row.startTime) }}</span>
                --
                <span>{{ fromatTimeM(row.endTime) }}</span>
            </template>
        </el-table-column>
        <el-table-column label="状态">
            <template #default="{ row }: { row: ApiRoomItem }">
                <span>{{ liveIndexStatus[row.status] }}</span>
            </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="250" align="center">
            <template #default="{ row }: { row: ApiRoomItem }">
                <el-link
                    v-if="row.status !== 'CLOSED'"
                    style="padding: 0 5px"
                    :underline="false"
                    type="primary"
                    size="small"
                    @click="handleShare(row)"
                    >分享
                </el-link>
                <el-link style="padding: 0 5px" :underline="false" type="primary" size="small" @click="handleDelClick(row)">删除</el-link>
            </template>
        </el-table-column>
    </el-table>
    <el-row justify="end" align="middle">
        <!-- 好用的分页器 -->
        <page-manage
            v-model="pageConfig"
            :load-init="true"
            :page-size="pageConfig.size"
            :total="pageConfig.total"
            @reload="initStodioList"
            @handle-size-change="handleSizeChange"
            @handle-current-change="handleCurrentChange"
        />
    </el-row>
    <el-dialog v-model="sharePopupShow" width="30%" title="分享" @close="handleClose">
        <div class="sharePopupShow-box">
            <div class="sharePopupShow-box__title">直播间小程序码</div>
            <el-image style="width: 187px; height: 187px" :src="dataSrc" referrerpolicy="no-referrer" />
        </div>
    </el-dialog>
</template>

<style scoped lang="scss">
@include b(name) {
    width: 120px;
    @include utils-ellipsis;
}
@include b(sharePopupShow-box) {
    padding: 30px 80px;
    background: #f7f7f7;
    border-radius: 2px;
    text-align: center;
    @include e(title) {
        font-size: 14px;
        color: #333333;
        margin-bottom: 24px;
    }
}
</style>
