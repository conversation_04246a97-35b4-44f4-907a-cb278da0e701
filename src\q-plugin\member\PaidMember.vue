<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-09-18 10:04:22
 * @LastEditors: lexy
 * @LastEditTime: 2023-09-18 15:33:31
-->
<script lang="ts" setup>
import QPlugin from '@/q-plugin/index.vue'
import useConvert from '@/composables/useConvert'
import * as Request from '@/apis/http'
import { ElMessageBox, ElMessage, ElSwitch } from 'element-plus'
import { cloneDeep } from 'lodash'
import DecimalInput from '@/components/decimal-input/decimal-input.vue'
</script>
<template>
    <q-plugin
        dev-url="http://localhost:5173"
        :context="{
            Request,
            DecimalInput,
            UseConvert: useConvert,
            ElementPlus: { ElMessageBox, ElMessage },
            Lodash: { cloneDeep },
        }"
        hide-on-miss
        name="PaidMember"
        service="addon-member"
    />
</template>
