<!--
 * @Author: lexy 
 * @Date: 2024-04-25 10:05:46
 * @LastEditors: lexy 
 * @LastEditTime: 2024-04-25 16:50:24
 * @FilePath: \webB2B2C\src\q-plugin\secondsKill\SeckillInfo.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script lang="ts" setup>
import QPlugin from '@/q-plugin/index.vue'
import { useEventListener } from '@vueuse/core'
import UseConvert from '@/composables/useConvert'
import * as Request from '@/apis/http'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import DecimalInput from '@/components/decimal-input/decimal-input.vue'
</script>
<template>
    <q-plugin
        dev-url="http://localhost:5173"
        :context="{
            VueRouter: { useRoute, useRouter },
            VueUse: { useEventListener },
            UseConvert,
            Request,
            ElementPlus: { ElMessageBox, ElMessage },
            DecimalInput,
        }"
        name="PlatformOnlyMemberInfo"
        service="addon-member-only"
    />
</template>
