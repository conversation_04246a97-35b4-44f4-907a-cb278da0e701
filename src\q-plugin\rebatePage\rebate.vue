<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-07-31 15:10:04
 * @LastEditors: lexy
 * @LastEditTime: 2024-04-11 10:17:27
-->
<template>
    <q-plugin
        :context="{
            UseConvert,
            ElementPlusIconsVue: { QuestionFilled, ArrowDown },
            VueClipboard3,
            DateUtil,
            Lodash,
            Request: { get, post, put },
            PageManageTwo,
            Decimal,
            RemarkPopup,
            MCard,
            DecimalInput,
            VueUse: { useVModel },
            ElementPlus: { ElMessage },
        }"
        name="Rebate"
        service="addon-rebate"
    />
</template>

<script lang="ts" setup>
import QPlugin from '@/q-plugin/index.vue'
import UseConvert from '@/composables/useConvert'
import { QuestionFilled, ArrowDown } from '@element-plus/icons-vue'
import VueClipboard3 from 'vue-clipboard3'
import DateUtil from '@/utils/date'
import Lodash from 'lodash'
import { get, post, put } from '@/apis/http'
import PageManageTwo from '@/components/PageManage.vue'
import RemarkPopup from '@/components/remark/remark-popup.vue'
import MCard from '@/components/MCard.vue'
import Decimal from 'decimal.js'
import { useVModel } from '@vueuse/core'
import DecimalInput from '@/components/decimal-input/decimal-input.vue'
import { ElMessage } from 'element-plus'
</script>

<style scoped></style>
