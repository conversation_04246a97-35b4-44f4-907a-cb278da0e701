<script lang="ts" setup>
import QPlugin from '@/q-plugin/index.vue'
import PageManage from '@/components/PageManage.vue'
import { ElMessage } from 'element-plus'
import UseConvert from '@/composables/useConvert'
import * as Request from '@/apis/http'

const $props = defineProps({
    pointGoodsList: {
        type: Array,
        default() {
            return []
        },
    },
    goodsVisible: {
        type: Boolean,
        default: false,
    },
})
const qPlugins = ref()
defineExpose({ qPlugins })
</script>
<template>
    <q-plugin
        ref="qPlugins"
        dev-url="http://*************:5173"
        :context="{
            UseConvert,
            Request,
            ElementPlus: { ElMessage },
            PageManage,
        }"
        :properties="$props"
        name="PlatformDecorationSeckillSelectGood"
        service="addon-seckill"
    />
</template>
