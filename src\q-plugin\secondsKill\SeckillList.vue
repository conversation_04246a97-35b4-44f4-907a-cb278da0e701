<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-09-05 16:54:44
 * @LastEditors: 刘飞 
 * @LastEditTime: 2024-04-25 13:24:06
-->
<script lang="ts" setup>
import QPlugin from '@/q-plugin/index.vue'
import { useVModel } from '@vueuse/core'
import UseConvert from '@/composables/useConvert'
import * as Request from '@/apis/http'
// import DateUtil from '@/utils/date'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { Search } from '@element-plus/icons-vue'
import PageManage from '@/components/PageManage.vue'
</script>
<template>
    <q-plugin
        dev-url="http://*************:5173"
        :context="{
            VueRouter: { useRoute, useRouter },
            VueUse: { useVModel },
            UseConvert,
            Request,
            PageManage,
            ElementPlus: { ElMessageBox, ElMessage },
            ElementPlusIconsVue: { Search },
        }"
        name="PlatformSeckillList"
        service="addon-seckill"
    />
</template>
