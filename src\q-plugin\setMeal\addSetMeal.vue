<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-07-19 16:27:52
 * @LastEditors: lexy 
 * @LastEditTime: 2024-04-26 16:26:58
-->
<template>
    <q-plugin
        dev-url="http://*************:5173"
        :context="{ VueRouter: { useRoute, useRouter }, DateUtil, UseConvert, Request: { get, post, del }, QUpload }"
        name="PlatformAddSetMeal"
        service="addon-matching-treasure"
    />
</template>

<script lang="ts" setup>
import QPlugin from '@/q-plugin/index.vue'
import { useRoute, useRouter } from 'vue-router'
import DateUtil from '@/utils/date'
import UseConvert from '@/composables/useConvert'
import { get, post, del } from '@/apis/http'
import QUpload from '@/components/q-upload/q-upload.vue'
</script>

<style scoped></style>
