<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-07-19 16:27:52
 * @LastEditors: lexy 
 * @LastEditTime: 2024-05-08 15:14:22
-->
<template>
    <q-plugin
        dev-url="http://*************:5173"
        :context="{
            VueUse: { useVModel },
            ElementPlusIconsVue: { Search },
            VueRouter: { useRoute, useRouter },
            Request: { get, post, del },
            PageManageTwo,
            ElementPlus: { ElMessage, ElMessageBox },
        }"
        name="PlatformSetMeal"
        service="addon-matching-treasure"
    />
</template>

<script lang="ts" setup>
import QPlugin from '@/q-plugin/index.vue'
import { useVModel } from '@vueuse/core'
import { Search } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import { get, post, del } from '@/apis/http'
import PageManageTwo from '@/components/PageManage.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
</script>

<style scoped></style>
