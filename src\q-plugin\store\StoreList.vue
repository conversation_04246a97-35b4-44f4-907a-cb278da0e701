<script lang="ts" setup>
import QPlugin from '@/q-plugin/index.vue'
import { useRouter } from 'vue-router'
import PageManage from '@/components/PageManage.vue'
import * as Request from '@/apis/http'
import { REGEX_MOBILE } from '@/libs/validate'
import { ElMessageBox, ElMessage } from 'element-plus'
import MCard from '@/components/MCard.vue'
</script>
<template>
    <q-plugin
        :context="{
            VueRouter: { useRouter },
            PageManage,
            Request,
            LibsValidate: { REGEX_MOBILE },
            ElementPlus: { ElMessageBox, ElMessage },
            MCard,
        }"
        name="PlatformStoreList"
        service="addon-shop-store"
    />
</template>
