<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-09-05 16:54:44
 * @LastEditors: lexy
 * @LastEditTime: 2023-09-12 09:56:33
-->
<script lang="ts" setup>
import QPlugin from '@/q-plugin/index.vue'
import { ElMessage, ElUpload, ElMessageBox } from 'element-plus'
import MCard from '@/components/MCard.vue'
import { doGetCategory, doGetShopList } from '@/apis/shops'
import QTable from '@/components/qszr-core/packages/q-table/QTable'
import QTableColumn from '@/components/qszr-core/packages/q-table/q-table-column.vue'
import PageManageTwo from '@/components/PageManage.vue'
import { useRoute } from 'vue-router'
import QUpload from '@/components/q-upload/q-upload.vue'
import { usePlatformGoodStatus } from '@/composables/usePlatformGoodStatus'
import UseConvert from '@/composables/useConvert'
import VueClipboard3 from 'vue-clipboard3'
import {
    doGetSupplierCommodityDetails,
    doGetSeachSupplierSearchList,
    doGetSupplierList,
    doUpdateSupplierSellStatus,
    recoverSupplierGoods,
} from '@/apis/good'
import { cloneDeep } from 'lodash'
import { CircleClose, CopyDocument } from '@element-plus/icons-vue'
</script>
<template>
    <q-plugin
        :context="{
            GoodAPI: {
                doGetSupplierCommodityDetails,
                doGetSeachSupplierSearchList,
                doGetSupplierList,
                doUpdateSupplierSellStatus,
                recoverSupplierGoods,
            },
            ElementPlus: { ElMessage, ElUpload, ElMessageBox },
            ShopAPI: { doGetCategory, doGetShopList },
            MCard,
            VueRouter: { useRoute },
            QTable,
            QTableColumn,
            UseConvert,
            QUpload,
            PageManageTwo,
            PlatformGoodStatus: { usePlatformGoodStatus },
            Lodash: { cloneDeep },
            ElementPlusIconsVue: { CircleClose, CopyDocument },
            VueClipboard3,
        }"
        name="PlatformCommodityList"
        service="addon-supplier"
        dev-url="http://localhost:5173"
    />
</template>
