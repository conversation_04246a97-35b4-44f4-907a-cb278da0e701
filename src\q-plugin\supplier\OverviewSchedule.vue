<script lang="ts" setup>
import QPlugin from '@/q-plugin/index.vue'
import { useRouter } from 'vue-router'
import * as Request from '@/apis/http'
import { ElMessage } from 'element-plus'

const prop = defineProps({
    loadSupplierData: {
        type: Object,
        default: () => {},
    },
})
</script>
<template>
    <q-plugin
        :context="{
            VueRouter: { useRouter },
            Request,
            ElementPlus: { ElMessage },
        }"
        :properties="prop"
        hide-on-miss
        name="PlatformOverviewSchedule"
        service="addon-supplier"
    />
</template>
