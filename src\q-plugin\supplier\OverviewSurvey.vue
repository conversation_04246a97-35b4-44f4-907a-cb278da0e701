<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-09-06 13:57:17
 * @LastEditors: lexy
 * @LastEditTime: 2023-09-06 14:23:53
-->
<script lang="ts" setup>
import QPlugin from '@/q-plugin/index.vue'

const prop = defineProps({
    supplierData: {
        type: Object,
        default: () => {},
    },
})
</script>
<template>
    <q-plugin
        :dev-url="'http://localhost:5173'"
        :properties="prop.supplierData"
        hide-on-miss
        name="PlatformOverviewSurvey"
        service="addon-supplier"
    />
</template>
