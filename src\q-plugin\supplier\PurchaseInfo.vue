<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-09-18 10:04:22
 * @LastEditors: lexy
 * @LastEditTime: 2023-09-18 15:38:01
-->
<script lang="ts" setup>
import QPlugin from '@/q-plugin/index.vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import UseConvert from '@/composables/useConvert'
import * as Request from '@/apis/http'
import Decimal from 'decimal.js'
import VueClipboard3 from 'vue-clipboard3'
import { regionData } from 'element-china-area-data'
import QAddress from '@/components/q-address/q-address.vue'
import { AddressFn } from '@/components/q-address'
import DateUtil from '@/utils/date'
</script>
<template>
    <q-plugin
        dev-url="http://*************:5173"
        :context="{
            VueRouter: { useRoute, useRouter },
            Decimal,
            ElementPlus: { ElMessageBox, ElMessage },
            VueClipboard3,
            ElementChinaAreaData: { regionData },
            QAddressIndex: { AddressFn },
            Request,
            DateUtil,
            QAddress,
            UseConvert,
        }"
        name="PlatformPurchaseInfo"
        service="addon-supplier"
    />
</template>
