<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-09-05 16:54:44
 * @LastEditors: lexy
 * @LastEditTime: 2023-09-14 19:13:12
-->
<script lang="ts" setup>
import QPlugin from '@/q-plugin/index.vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import MCard from '@/components/MCard.vue'
import PageManage from '@/components/PageManage.vue'
import { useRoute, useRouter } from 'vue-router'
import UseConvert from '@/composables/useConvert'
import { cloneDeep } from 'lodash'
import VueClipboard3 from 'vue-clipboard3'
import Decimal from 'decimal.js'
import { regionData } from 'element-china-area-data'
import { AddressFn } from '@/components/q-address'
import * as Request from '@/apis/http'
import DateUtil from '@/utils/date'
import { ArrowDown } from '@element-plus/icons-vue'
</script>
<template>
    <q-plugin
        dev-url="http://*************:5173"
        :context="{
            PageManage,
            UseConvert,
            VueRouter: { useRoute, useRouter },
            MCard,
            Lodash: { cloneDeep },
            Decimal,
            ElementPlus: { ElMessageBox, ElMessage },
            VueClipboard3,
            ElementChinaAreaData: { regionData },
            QAddressIndex: { AddressFn },
            Request,
            DateUtil,
            ElementPlusIconsVue: { ArrowDown },
        }"
        name="PlatformPurchaseList"
        service="addon-supplier"
    />
</template>
