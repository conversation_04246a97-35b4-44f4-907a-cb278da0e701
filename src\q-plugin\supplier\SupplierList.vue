<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-09-05 16:54:44
 * @LastEditors: lexy 
 * @LastEditTime: 2023-11-24 13:30:44
-->
<script lang="ts" setup>
import QPlugin from '@/q-plugin/index.vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import MCard from '@/components/MCard.vue'
import QTable from '@/components/qszr-core/packages/q-table/QTable'
import QTableColumn from '@/components/qszr-core/packages/q-table/q-table-column.vue'
import PageManage from '@/components/PageManage.vue'
import { useRoute, useRouter } from 'vue-router'
import UseConvert from '@/composables/useConvert'
import { Shop, Warning } from '@element-plus/icons-vue'
import { doChangeStatus, doDelShop, doShopAudit, doGetShopList, doCheckOCR, doShopReAudit } from '@/apis/shops'
import QDropdownBtn from '@/components/q-btn/q-dropdown-btn.vue'
import QTooltip from '@/components/q-tooltip/q-tooltip.vue'
import Storage from '@/utils/Storage'
import DateUtil from '@/utils/date'
</script>
<template>
    <q-plugin
        :context="{
            MCard,
            PageManage,
            QTable,
            QTableColumn,
            VueRouter: { useRoute, useRouter },
            UseConvert,
            ShopAPI: { doChangeStatus, doCheckOCR, doDelShop, doShopAudit, doShopReAudit, doGetShopList },
            ElementPlus: { ElMessageBox, ElMessage },
            ElementPlusIconsVue: { Shop, Warning },
            QDropdownBtn,
            QTooltip,
            Storage,
            DateUtil,
        }"
        dev-url="http://localhost:5173"
        name="PlatformSupplierList"
        service="addon-supplier"
    />
</template>
