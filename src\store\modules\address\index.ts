import { defineStore } from 'pinia'
import storage from '@/utils/Storage'
import { ADDRESS_TYPE, doGetTypeAddressList } from '@/apis/address/index'

export interface AreaItem {
    code: string
    name: string
    children?: AreaItem[]
}

function getStorageKey(type: 'provinces' | 'cities' | 'districts') {
    return `address_${type}`
}

type ADDRESS_DATA = {
    provinces: AreaItem[]
    cities: Record<string, AreaItem[]>
    districts: Record<string, AreaItem[]>
    loading: boolean
}

const $storage = new storage()
const storeageTempTime = 60 * 60 * 24 * 31

export const useAddressStore = defineStore('useAddressStore', {
    state: () =>
        ({
            provinces: [],
            cities: {},
            districts: {},
            loading: false,
        } as ADDRESS_DATA),
    actions: {
        initCache() {
            const provinces = JSON.parse($storage.getItem(getStorageKey('provinces')))
            if (provinces) this[`provinces`] = JSON.parse(provinces)
            const cities = JSON.parse($storage.getItem(getStorageKey('cities')))
            if (cities) this[`cities`] = JSON.parse(cities)
            const districts = JSON.parse($storage.getItem(getStorageKey('districts')))
            if (districts) this[`districts`] = JSON.parse(districts)
        },
        async getProvinces(): Promise<AreaItem[]> {
            if (this.provinces.length > 0) return this.provinces
            this.loading = true
            try {
                const { data = [] } = await doGetTypeAddressList()
                if (data?.length) {
                    this.provinces = data || []
                    $storage.setItem(getStorageKey('provinces'), JSON.stringify(data), storeageTempTime)
                    return data
                }
                return []
            } finally {
                this.loading = false
            }
        },
        async getCities(provinceCode: string): Promise<AreaItem[]> {
            if (this.cities[provinceCode]) return this.cities[provinceCode]
            this.loading = true
            try {
                const { data = [] } = await doGetTypeAddressList(ADDRESS_TYPE.PROVINCE, provinceCode)
                if (data?.length) {
                    this.cities[provinceCode] = data
                    $storage.setItem(getStorageKey('cities'), JSON.stringify(this.cities), storeageTempTime)
                    return data
                }
                return []
            } finally {
                this.loading = false
            }
        },
        async getDistricts(cityCode: string): Promise<AreaItem[]> {
            if (this.districts[cityCode]) return this.districts[cityCode]
            this.loading = true
            try {
                const { data = [] } = await doGetTypeAddressList(ADDRESS_TYPE.CITY, cityCode)
                if (data?.length) {
                    this.districts[cityCode] = data?.map(({ name, code }) => ({ name, code, leaf: true }))
                    $storage.setItem(getStorageKey('districts'), JSON.stringify(this.districts), storeageTempTime)
                    return data
                }
                return []
            } finally {
                this.loading = false
            }
        },
        clearCache() {
            $storage.removeItem(getStorageKey('provinces'))
            $storage.removeItem(getStorageKey('cities'))
            $storage.removeItem(getStorageKey('districts'))
        },
    },
    getters: {
        isLoading: (state) => state.loading,
        provinceData: (state) => state.provinces,
        citiesData: (state) => (provinceCode: string) => state.cities[provinceCode],
        districtsData: (state) => (cityCode: string) => state.districts[cityCode],
    },
})
