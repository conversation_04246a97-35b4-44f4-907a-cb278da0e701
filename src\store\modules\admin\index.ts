/*
 * @description: 登录保存用户信息
 * @Author: lexy
 * @Date: 2022-10-19 18:43:40
 * @LastEditors: lexy
 * @LastEditTime: 2023-07-18 14:07:27
 */
import { defineStore } from 'pinia'
import storage from '@/utils/Storage'

const storageExample = new storage()
const adminInfo_ = {
    value: '',
    additionalInformation: { jti: '', shopId: '', userId: '' },
    expiration: '',
    expired: false,
    expiresIn: 0,
    scope: [],
    refreshToken: {
        expireAt: '',
        expiresIn: 0,
        value: '',
    },
    tokenType: 'bearer' as const,
    shopId: '',
    userId: '',
    nickname: '',
    mobile: '',
    email: '',
}
interface ApiAdminInfo extends UserData {
    additionalInformation: {
        jti: string
        shopId: string
        userId: string
        mobile: string
    }
    expiration: string
    expired: boolean
    expiresIn: number
    refreshToken: {
        expireAt: string
        expiration: string
        value: string
    }
    scope: string[]
    tokenType: 'bearer'
    value: string
}
interface UserData {
    /**
     * 店铺id
     */
    shopId: string
    /**
     * 用户id
     */
    userId: string
    /**
     * 昵称
     */
    nickname: string
    /**
     * 手机号
     */
    mobile: string
    /**
     * 邮箱
     */
    email: string
}
export const useAdminInfo = defineStore('useAdminInfo', {
    actions: {
        SET_ADMIN_INFO(payload: ApiAdminInfo) {
            this.adminInfo = { ...this.adminInfo, ...payload }
            storageExample.setItem('adminInfo', payload, Number(payload.expiresIn))
            return this.adminInfo.value
        },
        REMOVE_ADMIN_INFO() {
            this.adminInfo = adminInfo_
            storageExample.removeItem('adminInfo')
        },
    },
    state: () => ({
        adminInfo: (storageExample.getItem('adminInfo') || adminInfo_) as ApiAdminInfo,
    }),
    getters: {
        getterToken: (state) => state.adminInfo.value,
        getterAdminInfo: (state) => state.adminInfo,
    },
})
