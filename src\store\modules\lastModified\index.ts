/*
 * @description:装修存储
 * @Author: lexy
 * @Date: 2022-08-02 10:33:19
 * @LastEditors: lexy
 * @LastEditTime: 2023-02-17 17:18:17
 */
import { defineStore } from 'pinia'
import lastModified from './store'
import storage from '@/utils/Storage'
const $storage = new storage()
export const useLastModified = defineStore('lastModified', {
    state: () => lastModified || $storage.getItem('lastModified'),
    actions: {
        SET_LAST_MODIFIED(playload: string) {
            $storage.setItem('lastModified', playload, 60 * 60 * 24)
            this.lastModified = playload
        },
    },
    getters: {},
})
