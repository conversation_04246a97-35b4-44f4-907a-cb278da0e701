/*
 * @description:
 * @Author: lexy
 * @Date: 2022-10-17 13:05:01
 * @LastEditors: lexy
 * @LastEditTime: 2022-10-17 16:02:44
 */
import { defineStore } from 'pinia'
import { ApiTagItem } from '@/views/baseVip/types'
export const useVipTagsStore = defineStore('vipTagsStore', {
    state() {
        return {
            tags: [] as ApiTagItem[],
        }
    },
    actions: {
        SET_TAGS(payload: ApiTagItem[]) {
            this.tags = payload
        },
    },
})
