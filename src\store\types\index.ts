/*
 * @description:
 * @Author: lexy
 * @Date: 2022-03-15 09:24:01
 * @LastEditors: lexy
 * @LastEditTime: 2022-11-03 14:03:35
 */
export interface Menu {
    id: number
    name: string
    icon: string
    type: number
    path: string
    children?: Array<Menu>
}

export interface Button {
    name: string
    type: string
    icon: string
}

export interface User {
    username: string
    name: string
    token: string
    avatar: string | undefined
    menus: Array<Menu>
    buttons: Map<string, Button>
}

export interface Aside {
    show: boolean
}
export interface System {
    leftAside: Aside
    rightAside: Aside
}

export type appType = {
    sidebar: {
        opened: boolean
        withoutAnimation: boolean
        // 判断是否手动点击Collapse
        isClickCollapse: boolean
    }
    layout: string
    device: string
    viewportSize: { width: number; height: number }
    sortSwap: boolean
}
