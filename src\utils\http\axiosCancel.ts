import axios from 'axios'
import { isFunction } from '@/utils/is'
import type { AxiosRequestConfig, Canceler } from 'axios'

// Store pending requests: Key is the unique request identifier, Value is the cancel function
let pendingMap = new Map<string, Canceler>()

// Generates a unique identifier string for a request based on method, URL, params, and data
export const getPendingUrl = (config: AxiosRequestConfig) => {
    const { method, url, params, data } = config
    // Ensure consistent serialization for comparison
    const paramsString = params ? JSON.stringify(params) : ''
    const dataString = data ? JSON.stringify(data) : ''
    return [method, url, paramsString, dataString].join('&')
}

export class AxiosCanceler {
    /**
     * Adds a request to the pending map.
     * If an identical request is already pending, it does not add the new one to the map,
     * preventing the cancellation of the original request.
     * @param config The Axios request configuration.
     */
    addPending(config: AxiosRequestConfig) {
        const url = getPendingUrl(config)
        // Always create a cancel token and associate it with the current request's config.
        // This allows the caller to cancel this specific request if needed,
        // even if it's a duplicate of one already pending.
        config.cancelToken =
            config.cancelToken ||
            new axios.CancelToken((cancel) => {
                // Only add the cancel function to the map if no identical request is already pending.
                // This prevents a new identical request from implicitly cancelling the previous one
                // via the removePending mechanism.
                if (!pendingMap.has(url)) {
                    pendingMap.set(url, cancel)
                }
            })
    }

    /**
     * Removes a pending request from the map and cancels it.
     * @param config The Axios request configuration.
     */
    removePending(config: AxiosRequestConfig) {
        const url = getPendingUrl(config)
        if (pendingMap.has(url)) {
            const cancel = pendingMap.get(url)
            // Execute the cancel function associated with the request in the map.
            // Passing the URL (the key) as the message for the Cancel object.
            cancel?.(url)
            // Remove the request from the map.
            pendingMap.delete(url)
        }
    }

    /**
     * Removes all pending requests from the map and cancels them.
     * Typically used during route navigation.
     */
    removeAllPending() {
        pendingMap.forEach((cancel) => {
            // Ensure cancel is a function before calling
            if (cancel && isFunction(cancel)) {
                // Cancel without a specific message
                cancel()
            }
        })
        pendingMap.clear()
    }

    /**
     * Resets the pending map, clearing all entries without cancelling ongoing requests.
     * Use with caution, typically only if absolutely necessary to clear state.
     */
    reset(): void {
        pendingMap = new Map<string, Canceler>()
    }
}
