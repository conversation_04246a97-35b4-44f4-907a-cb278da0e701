import { SourceType, useAddressStore } from '@/store/modules/address'
import type { FormInstance } from 'element-plus'

const addressData = useAddressStore()
/**
 * @description 对验证表单的Promise封装
 * @param { FormInstance | null } formRefs 验证表单实例对象
 * @returns { Promise<any> }
 */
export const validateForm = (formRefs?: FormInstance | null) => {
    if (!formRefs) {
        return Promise.reject(new Error('no form instance input'))
    }
    return new Promise((resolve, reject) => {
        formRefs?.validate((isValid, invalidFields) => {
            if (isValid) {
                resolve('success valid')
            } else {
                reject(invalidFields)
            }
        })
    })
}

type CITY_DATA_TYPE = {
    name: string
    code: string
    children?: Array<CITY_DATA_TYPE>
}

export type ADR_OBJ_TYPE = {
    province: string
    city?: string
    district?: string
}

/**
 * 根据地址信息反查省市信息ID
 * @param address    地址信息字符对象
 * @param level      当前信息层级
 * @param keyValues  城市信息数组
 * @param key        城市信息键值名
 * @returns
 */
export async function findAddressCodesByString(address: ADR_OBJ_TYPE, level = 0, keyValues: string[] = [], key: 'code' | 'name' = 'code') {
    // 定义要匹配的地址层级
    const levels = ['province', 'city', 'district']
    const currentLevel = levels[level]

    let data = null
    if (currentLevel === 'province') {
        data = await addressData.getProvinces()
    } else if (currentLevel === 'city') {
        data = await addressData.getCities(keyValues[level - 1])
    } else if (currentLevel === 'district') {
        data = await addressData.getDistricts(keyValues[level - 1])
    }

    // 基础情况：已经处理完所有层级或没有更多数据
    if (level >= levels.length || !data) {
        return keyValues
    }

    // 查找当前层级的匹配项
    const match = data.find((item: CITY_DATA_TYPE) => item.name.includes(address[currentLevel]))

    if (match) {
        // 添加匹配到的代码
        keyValues.push(match[key])
        // 递归处理下一层级
        if (data?.length) {
            return await findAddressCodesByString(address, level + 1, keyValues)
        }
    }

    return keyValues
}

/**
 * base64 转file
 * @param dataurl   base64数据
 * @param filename  文件名称
 * @returns
 */
export function dataURLtoFile(dataurl: string, filename: string) {
    let arr = dataurl.split(',')
    let mime = arr[0].match(/:(.*?);/)![1]
    let suffix = mime.split('/')[1]
    let bstr = window.atob(arr[1])
    let n = bstr.length
    let u8arr = new Uint8Array(n)
    while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
    }
    return new File([u8arr], `${filename}.${suffix}`, {
        type: mime,
    })
    //将base64转换为文件
}
