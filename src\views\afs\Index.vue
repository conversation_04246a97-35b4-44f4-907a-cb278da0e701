<script setup lang="ts">
import { ElMessage } from 'element-plus'
import QIcon from '@/components/q-icon/q-icon.vue'
import AccordionFrom from '@/components/order/accordion-from.vue'
import QTable from '@/components/qszr-core/packages/q-table/QTable'
import QTableColumn from '@/components/qszr-core/packages/q-table/q-table-column.vue'
import PageManage from '@/components/PageManage.vue'
import { TabItem, getAfsListStatusCn, getAfsNumStatusCn, getAfsListPagesStatusCn } from '@/composables/useAfsStatus'
import { doGetAfsList } from '@/apis/afs'
import RemarkFlag from '@/components/remark/remark-flag.vue'
import RemarkPopup from '@/components/remark/remark-popup.vue'
import type { ApiOrderAfsItem } from '@views/afs/types'
import { doPostAfsExport } from '@/apis/exportData'
/*
 *variable
 */
const { divTenThousand } = useConvert()
const activeTab = ref('')
const remarkDialog = ref(false) // 批量备注弹窗
const remarkMessageText = ref('') // 备注消息文本
const tabData = ref<ApiOrderAfsItem[]>([])
const multiSelect = ref<ApiOrderAfsItem[]>([])
const ids = ref<string[]>([])
const $router = useRouter()
const pageConfig = reactive({
    size: 10,
    current: 1,
    total: 0,
    params: { afsNo: '', receiverName: '', endTime: '', buyerNickname: '', orderNo: '', productName: '', startTime: '' },
})
const tableStyleUp = ref(false)
enum SellTypeEnum {
    CONSIGNMENT = '代销商品',
    PURCHASE = '采购商品',
    OWN = '自有商品',
}
/*
 *lifeCircle
 */
initAfsList()
/*
 *function
 */
async function initAfsList() {
    const { params, current, size } = pageConfig
    const { code, data } = await doGetAfsList({ ...params, status: activeTab.value, current, size, afsNo: params.orderNo })
    if (code !== 200) return ElMessage.error('订单列表获取失败')
    tabData.value = data.records
    pageConfig.current = data.current
    pageConfig.size = data.size
    pageConfig.total = data.total
}
//tab点击切换
const handleTabChange = () => {
    initAfsList()
}
/**
 * @LastEditors: lexy
 * @description: 获取搜索表单数据
 * @returns {*}
 */
const GetSearchData = (data: any) => {
    pageConfig.params = data
    initAfsList()
}
/**
 * @LastEditors: lexy
 * @description: 查看详情(平台端传shopid，商家端传itemid)
 * @params salesShow 显示售后页面
 * @returns {*}
 */
const handleSelected = (row: ApiOrderAfsItem, e: string, afsNo: string) => {
    const { no, shopOrderItemId, packageId, orderNo } = row
    $router.push({ name: 'afsDetailsIndex', query: { orderNo, no, shopOrderItemId, packageId: packageId || '', afsNo } })
}
/**
 * @LastEditors: lexy
 * @description: 分页器
 * @param {*} value
 * @returns {*}
 */
const handleSizeChange = (value: number) => {
    pageConfig.current = 1
    pageConfig.size = value
    initAfsList()
}
const handleCurrentChange = (value: number) => {
    pageConfig.current = value
    initAfsList()
}
const totalPrice = (price: string, num: number) => {
    return divTenThousand(price).mul(num)
}
const handleChangeShow = (val: boolean) => {
    tableStyleUp.value = val
}
const handleNoteItem = (row: ApiOrderAfsItem) => {
    if (row.no) {
        ids.value = [row.no]
        if (row.remark) {
            remarkMessageText.value = row.remark
        }
        remarkDialog.value = true
    }
}
const handleNote = () => {
    if (multiSelect.value.length) {
        ids.value = multiSelect.value.map((item) => item.no)
        remarkDialog.value = true
        return
    }
    ElMessage.error('请先选择订单')
}
const handleRemarkSuccess = () => {
    multiSelect.value = []
    initAfsList()
}
// 导出数据
// "afsNo":null//"工单号"
// "buyerNickname":null,//买家昵称
// "productName":null,//商品名称
// "receiverName":null,//收货人姓名
// "startTime":null,//下单开始时间
// "endTime":null,//下单结束时间
// "status":null,//工单状态  待审核 处理中 已完成 已关闭
// "exportAfsOrderNos":[]//需要导出的工单号列表
const exportData = async (SearchFromData: any) => {
    if (multiSelect.value.length) {
        let exportAfsOrderNos = ['']
        exportAfsOrderNos = multiSelect.value.map((item) => item.no)
        const { code, data, msg } = await doPostAfsExport({ exportAfsOrderNos })
        if (code !== 200) return ElMessage.error(msg || '导出失败')
        else return ElMessage.success('导出成功')
    } else {
        let param = {
            afsNo: SearchFromData.orderNo,
            buyerNickname: SearchFromData.buyerNickname,
            productName: SearchFromData.productName,
            receiverName: SearchFromData.receiverName,
            startTime: SearchFromData.clinchTime?.[0],
            endTime: SearchFromData.clinchTime?.[1],
            status: activeTab.value,
            exportAfsOrderNos: '',
        }
        param.status = param.status.trim()
        const { code, data, msg } = await doPostAfsExport(param)
        if (code !== 200) return ElMessage.error(msg || '导出失败')
        else return ElMessage.success('导出成功')
    }
}
</script>

<template>
    <!-- 搜索部分s -->
    <accordion-from @search-data="GetSearchData" @change-show="handleChangeShow" @export-data="exportData" />
    <!-- 搜索部分e -->
    <div class="grey_bar"></div>
    <!-- tab部分s -->
    <div class="tab_container">
        <el-tabs v-model="activeTab" style="margin-top: 13px" @tab-change="handleTabChange">
            <el-tab-pane v-for="item in TabItem" :key="item.id" :label="item.title" :name="item.name" />
        </el-tabs>
    </div>
    <!-- tab部分e -->
    <!-- <div class="handle_container">
         <el-button link class="caozuo_btn" plain round bg style="width: 82px; height: 36px; background: #ecf5fd" @click="handleNote">
        批量备注
    </el-button> </div>-->
    <!-- table表格s -->
    <q-table
        v-model:checkedItem="multiSelect"
        :data="tabData"
        :selection="true"
        style="margin-top: 13px"
        class="tab"
        :class="{ tableup: !tableStyleUp }"
    >
        <template #header="{ row }">
            <div class="tab__header">
                <div>
                    <el-tag style="margin-right: 10px">{{ getAfsNumStatusCn(row.type) }}单</el-tag>
                    <span>订单号:{{ row.no }}</span>
                </div>
                <div>创建时间:{{ row.createTime }}</div>
                <div>店铺名称：{{ row.shopName || '未知店铺' }}</div>
                <remark-flag :content="row.remark" @see-remark="handleNoteItem(row)" />
            </div>
        </template>

        <q-table-column label="退款商品" align="left">
            <template #default="{ row }">
                <el-avatar style="width: 68px; height: 68px; flex-shrink: 0" shape="square" size="large" :src="row.afsOrderItem.image" />
                <div style="flex: 1; padding-left: 10px; overflow: hidden">
                    <div class="avatar_text_box">
                        <div class="money_text">
                            <div class="avatar_text_box__show">{{ row.afsOrderItem.productName }}</div>
                        </div>
                        <div class="money_text shop_specs" :title="row.afsOrderItem.specs" style="width: 200px">
                            {{ row.afsOrderItem.specs?.join('， ') }}
                        </div>
                        <p class="order-info__selltype">{{ SellTypeEnum[row?.afsOrderItem?.sellType] }}</p>
                    </div>
                </div>
                <div style="width: 100px; height: 68px; text-align: center" class="money_text">
                    <div>￥{{ divTenThousand(row.afsOrderItem.dealPrice) }}</div>
                    <div style="color: #838383; font-size: 10px">x {{ row.afsOrderItem.num }}</div>
                </div>
            </template>
        </q-table-column>
        <q-table-column label="退款金额" class="rate_size" width="100">
            <template #default="{ row }">
                <div class="avatar_text money_text" style="width: 68px; text-align: center; color: #f00; font-weight: 600">
                    ￥{{ divTenThousand(row.refundAmount) }}
                </div>
            </template>
        </q-table-column>
        <q-table-column label="退款用户" width="130">
            <template #default="{ row }">
                <div class="tab__client_box">
                    <div class="avatar_text avatar_text__bottom money_text">{{ row.buyerNickname }}</div>
                    <!-- <div style="padding: 0 10px 0" class="money_text">({{ row.afsOrderReceiver?.name }},{{ row.afsOrderReceiver?.mobile }})</div> -->
                </div>
            </template>
        </q-table-column>
        <q-table-column label="状态" width="95">
            <template #default="{ row }">
                <div class="sale__status">
                    <div class="order-status_text">{{ getAfsListPagesStatusCn(row.packageStatus) }}</div>
                    <div class="money_text">{{ getAfsListStatusCn(row) }}</div>
                </div>
            </template>
        </q-table-column>
        <q-table-column prop="sex" label="操作" width="170">
            <template #default="{ row }">
                <el-button type="primary" size="large" round plain @click="handleSelected(row, '查看详情', row.no)"> 查看详情 </el-button>
            </template>
        </q-table-column>
    </q-table>
    <!-- tab表格e -->
    <el-row justify="space-between" align="bottom" style="padding-top: 20px">
        <el-button link class="caozuo_btn" plain round bg style="width: 82px; height: 36px; background: #ecf5fd" @click="remarkDialog = true"
            >批量备注
        </el-button>
        <page-manage
            :load-init="true"
            :page-size="pageConfig.size"
            :page-num="pageConfig.current"
            :total="pageConfig.total"
            @reload="initAfsList"
            @handle-size-change="handleSizeChange"
            @handle-current-change="handleCurrentChange"
        />
    </el-row>
    <!-- 备注弹窗s -->
    <remark-popup
        v-model:isShow="remarkDialog"
        v-model:ids="ids"
        v-model:remark="remarkMessageText"
        remark-type="AFS"
        @success="handleRemarkSuccess"
    />
    <!-- 备注弹窗e -->
</template>

<style lang="scss" scoped>
@include b(note) {
    &:hover {
        color: #fff;
        background: #309af3 !important;
    }
}
@include b(tab) {
    height: calc(100vh - 492px);
    overflow-y: scroll;
    transition: height 0.5s;
    @include e(header) {
        @include flex(space-between);
        width: 100%;
        @include m(icon) {
            width: 20%;
            text-align: right;
        }
    }
}
.caozuo_btn:hover {
    color: #fff;
    background: #309af3 !important;
}
@include b(sale) {
    @include e(status) {
        @include flex;
        flex-direction: column;
    }
}
@include b(money_text) {
    font-size: 12px;
    color: #000000;
    overflow: hidden;
    width: 100%;
}
@include b(shop_specs) {
    @include utils-ellipsis(1);
}
@include b(order-status_text) {
    margin-bottom: 5px;
    font-size: 24rpx;
    color: #ff7417;
}
@include b(avatar_text) {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2; /* 可以显示的行数，超出部分用...表示*/
    -webkit-box-orient: vertical;
    @include e(bottom) {
        margin-bottom: 5px;
    }
}
@include b(avatar_text_box) {
    height: 68px;
    @include flex(flex-start, flex-start);
    flex-direction: column;
    @include e(show) {
        width: 260px;
        white-space: nowrap; /*nowrap 文本不会换行，会在此行继续显示*/
        overflow: hidden; /*溢出隐藏*/
        text-overflow: ellipsis; /*文本溢出隐藏，显示省略号*/
        font-weight: 600;
    }
}
@include b(tableup) {
    // height: calc(100vh - 340px);
    height: calc(100vh - 285px);
}
</style>
