<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-07-23 16:33:17
 * @LastEditors: lexy
 * @LastEditTime: 2022-12-19 11:39:19
-->
<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { doGetOrderDetails } from '@/apis/afs'
import type { ApiAfsOrder } from '@/views/afs/types/api'
type ActiveName = 'afsOrderInfo' | 'afsLogisticsInfo' | 'afsHistory'
/*
 *variable
 */
const tabPaneOrderDetails = [
    { label: '售后信息', name: 'afsHistory' },
    { label: '订单信息', name: 'afsOrderInfo' },
    { label: '物流信息', name: 'afsLogisticsInfo' },
]
const $route = useRoute()
const reactiveAsyncComponent = reactive({
    afsOrderInfo: defineAsyncComponent(() => import('@/views/afs/components/afs-order-info.vue')),
    afsLogisticsInfo: defineAsyncComponent(() => import('@/views/afs/components/afs-logistics-info.vue')),
    afsHistory: defineAsyncComponent(() => import('@/views/afs/components/afs-history.vue')),
})
const activeName = ref<ActiveName>('afsHistory')
// 订单详情数据
const OrderDetailsData = ref<ApiAfsOrder>()
//
/*
 *lifeCircle
 */
initOrderDetails()
/*
 *function
 */
/**
 * @LastEditors: lexy
 * @description: 获取售后订单详情信息
 * @returns {*}
 */
async function initOrderDetails() {
    const { packageId, orderNo } = $route.query
    if (orderNo) {
        const { code, data } = await doGetOrderDetails(orderNo as string, { packageId: packageId || '', usePackage: true })
        if (code !== 200) return ElMessage.error('获取售后订单详情失败')
        OrderDetailsData.value = data
    }
}
</script>

<template>
    <div style="padding: 16px; overflow-y: scroll">
        <el-tabs v-model="activeName">
            <el-tab-pane v-for="tabPaneItem in tabPaneOrderDetails" :key="tabPaneItem.label" :label="tabPaneItem.label" :name="tabPaneItem.name" />
        </el-tabs>
        <component :is="reactiveAsyncComponent[activeName]" :order="OrderDetailsData" />
    </div>
</template>
