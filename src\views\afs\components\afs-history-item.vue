<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-08-19 10:33:01
 * @LastEditors: lexy
 * @LastEditTime: 2022-11-04 13:47:59
-->
<script setup lang="ts">
/*
 *variable
 */
const $props = defineProps({
    src: {
        type: String,
        default:
            'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fpic1.zhimg.com%2Fv2-129d70e0863f9ce24ba112bd5df63660_1440w.jpg%3Fsource%3D172ae18b&refer=http%3A%2F%2Fpic1.zhimg.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1662608234&t=d22143c56de9c0c32b2c0628ce95cccb',
    },
    name: {
        type: String,
        default: '美子',
    },
    status: {
        type: String,
        default: '买家撤销退款申请',
    },
    time: { type: String, default: '2022-07-15 17:54:40' },
})

/*
 *variable
 */
/*
 *lifeCircle
 */
/*
 *function
 */
</script>

<template>
    <div class="afterSalesInfo">
        <div class="afterSalesInfo__box">
            <div class="afterSalesInfo__box-left">
                <el-image style="width: 61px; height: 61px" :src="$props.src" fit="cover" />
                <div class="afterSalesInfo__box-left-text">
                    <span>{{ $props.name }}</span>
                    <slot>
                        <span>{{ $props.status }}</span>
                    </slot>
                </div>
            </div>
            <time class="afterSalesInfo__box--time">{{ $props.time }}</time>
        </div>
        <slot name="picture"></slot>
    </div>
</template>

<style scoped lang="scss">
@include b(afterSalesInfo) {
    padding: 24px 0;
    border-bottom: 1px solid #ededed;
    @include e(box) {
        @include flex(space-between);
        height: 61px;
        @include m(time) {
            font-size: 14px;
            color: #333333;
        }
    }
    @include e(box-left) {
        @include flex(space-between);
    }
    @include e(box-left-text) {
        height: 61px;
        margin-left: 10px;
        @include flex(space-between, flex-start);
        flex-direction: column;
    }
}
</style>
