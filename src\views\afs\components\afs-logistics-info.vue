<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-08-19 11:26:19
 * @LastEditors: lexy
 * @LastEditTime: 2024-05-17 18:05:03
-->
<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-07-25 14:48:15
 * @LastEditors: lexy
 * @LastEditTime: 2022-08-19 11:58:54
-->
<script setup lang="ts">
import type { PropType } from 'vue'
// import QAddress from '@/components/q-address/q-address.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { doGetFirstDeliveryPage, doGetLogisticsTrajectoryByWaybillNo } from '@/apis/afs'
import type { ApiAfsOrder, ApiLogistics01 } from '@/views/afs/types/api'

/*
 *variable
 */
const $props = defineProps({
    order: {
        type: Object as PropType<ApiAfsOrder>,
        default() {
            return {}
        },
    },
})
const $route = useRoute()
const parcel = ref('')
// 第一个包裹的订单快递信息（快递公司，快递单号等）
const logistics = ref<ApiLogistics01>({
    createTime: '',
    deleted: false,
    expressCompanyCode: '',
    expressCompanyName: '',
    expressNo: '',
    id: '',
    orderNo: '',
    receiverAddress: '',
    receiverMobile: '',
    receiverName: '',
    remark: '',
    shopId: '',
    status: 'BUYER_COMMENTED_COMPLETED',
    type: 'EXPRESS',
    updateTime: '',
    version: 0,
    success: true,
})
const currentDeliveryLocation = ref([
    {
        areaCode: '',
        areaName: '',
        context: '',
        ftime: '',
        status: '',
        time: '',
    },
])
//未发货
const notDeliverGoods = ref(false)
const activities = [
    {
        content: '待取件',
        timestamp: '2018-04-12 20:46',
        text: '您的包裹状态修改为到站自提，您可凭取件码到菜鸟驿站自提或到站扫码取件',
    },
    {
        content: '待派送',
        timestamp: '2018-04-03 20:46',
        text: '您的包裹由宁波市海曙区万兴路651号店菜鸟驿站派送，如有问题可致电菜鸟驿站人员17606837611',
    },
    {
        content: '派送中',
        timestamp: '2018-04-03 20:46',
        text: '【浙江宁波石碶公司】的派件员【万顺点部】正在为您派件，如有疑问请联系派件员，联系电话【13789897791】，快件已消毒，申通小哥已测量体温正',
    },
    {
        content: '运输中',
        timestamp: '2018-04-03 20:46',
        text: '快件已到达【浙江宁波石碶公司】扫描员是【祥龙交叉带-进港】',
    },
    {
        content: 'Default node',
        timestamp: '2018-04-03 20:46',
        text: '快件由【浙江宁波转运中心】发往【浙江宁波石碶公司】，包裹已消杀',
    },
]
/*
 *lifeCircle
 */
watch(
    () => $props.order,
    (val) => {
        if (!val.shopOrderPackages) {
            notDeliverGoods.value = true
            return
        }
        initFirstDeliveryPage()
    },
    {
        immediate: true,
    },
)
/*
 *function
 */
async function initFirstDeliveryPage() {
    const { no, shopOrderItemId, packageId, orderNo } = $route.query
    console.log('packageId', packageId)
    const { code, data } = await doGetFirstDeliveryPage($props.order.no, $props.order.shopOrders[0].no, packageId ? (packageId as string) : '')
    if (code !== 200) return ElMessage.error('物流信息获取失败')
    console.log('物流信息data', data)
    logistics.value = data
    if (!logistics.value) return
    const { expressCompanyCode, expressNo, status: logisticsStatus } = logistics.value
    const { code: status, data: res } = await doGetLogisticsTrajectoryByWaybillNo(expressCompanyCode, expressNo, logisticsStatus)
    if (status !== 200) return ElMessage.error('物流轨迹获取失败')
    if (!res.status || res.status !== '200') {
        currentDeliveryLocation.value[0].context = res.message
        return
    }
    currentDeliveryLocation.value = res.data
}
</script>

<template>
    <div class="logisticsInfo">
        <!-- <el-row>
            <el-button ref="button" plain @click="parcel = ''">包裹1</el-button>
            <el-button plain @click="parcel = 'Shipment'">未发货</el-button>
        </el-row> -->
        <div v-if="!notDeliverGoods">
            <div class="logisticsInfo__title">物流信息</div>
            <div class="logisticsInfo__text">收货地址：{{ logistics.receiverAddress }}</div>
            <div class="logisticsInfo__text">物流公司：{{ logistics.expressCompanyName }}</div>
            <div class="logisticsInfo__text" style="margin-bottom: 20px">物流单号：{{ logistics.expressNo }}</div>
            <div class="logisticsInfo__divider" />
            <div class="logisticsInfo__title">物流详情</div>
            <el-timeline class="logisticsInfo__timeline">
                <el-timeline-item
                    v-for="(activity, index) in currentDeliveryLocation"
                    :key="index"
                    :timestamp="`${activity.time}`"
                    style="padding-bottom: 42px"
                    :color="index === 0 ? '#409eff' : ' '"
                    class="logisticsInfo__timeline--item"
                >
                    <el-row>
                        <div :style="{ color: index === 0 ? '#409eff' : ' ' }" class="logisticsInfo__timeline--status">
                            {{ activity.status }}
                        </div>
                        <div :style="{ color: index === 0 ? '#409eff' : ' ' }" class="logisticsInfo__timeline--time">
                            {{ activity.context }}
                        </div>
                    </el-row>
                </el-timeline-item>
            </el-timeline>
        </div>
        <div v-else>
            <div class="logisticsInfo__title">物流信息</div>
            <div class="logisticsInfo__text">物流详情:该商品，暂未发货。</div>
        </div>
    </div>
    <!-- <el-empty v-else description="暂无数据~" /> -->
</template>

<style scoped lang="scss">
@include b(logisticsInfo) {
    @include e(title) {
        font-size: 14px;
        color: #333333;
        font-weight: Bold;
        margin: 17px 0;
    }
    @include e(text) {
        margin-bottom: 11px;
    }
    @include e(timeline) {
        margin-top: 42px;
        & li:nth-child(1) {
            & :deep(.el-timeline-item__timestamp) {
                color: #000;
            }
        }
        @include m(status) {
            font-size: 13px;
            font-weight: bold;
            margin-right: 10px;
            color: #838383;
        }
        @include m(time) {
            color: #838383;
        }
    }
    @include e(divider) {
        height: 4px;
        margin: 0 -15px;
        background: #f2f2f2;
    }
}
</style>
