<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-07-25 14:42:12
 * @LastEditors: lexy
 * @LastEditTime: 2022-12-22 18:29:09
-->
<script setup lang="ts">
import { useMergeCells, initSpanTag } from '@/composables/useMergeCells'
import { watch } from 'vue'
import { payTypeFn } from '@/composables/usePaymentCn'
import { getAfsOrderInfoStatusCn } from '@/composables/useAfsStatus'
import { doGetUserDataByBuyerId } from '@/apis/afs'
import type { ApiOrder, OrderDiscount } from '@/views/order/types/order'
import type { PropType } from 'vue'
import type { ApiBuyersData } from '@/views/afs/types/api'
import Decimal from 'decimal.js'

type TabType = { row: any; column: any; rowIndex: any; columnIndex: number }
/*
 *variable
 */
const $props = defineProps({
    order: {
        type: Object as PropType<ApiOrder>,
        default() {
            return {}
        },
    },
})
const buyersData = ref<ApiBuyersData>({
    avatar: '',
    createTime: '',
    deleted: false,
    gender: '',
    id: '',
    nickname: '',
    updateTime: '',
    userId: '',
    version: 0,
})
const discountDataByType = {
    PLATFORM_COUPON: {
        name: '平台优惠',
        price: new Decimal(0),
    },
    SHOP_COUPON: {
        name: '店铺优惠',
        price: new Decimal(0),
    },
    freightPrice: {
        name: '运费',
        price: new Decimal(0),
    },
    MEMBER_DEDUCTION: {
        name: '会员折扣',
        price: new Decimal(0),
    },
    FULL_REDUCTION: { name: '满减', price: new Decimal(0) },
}
const { divTenThousand } = useConvert()
/*
 *lifeCircle
 */
// initOrderInfo()
watch(
    () => $props.order,
    async (val) => {
        if (val.buyerId) {
            const { data: res, code } = await doGetUserDataByBuyerId(val.buyerId)
            buyersData.value = code !== 200 ? { avatar: '', nickname: '', userId: '' } : res
        }
        $props.order.orderDiscounts.forEach((item: OrderDiscount) => {
            discountDataByType[item.sourceType].price = discountDataByType[item.sourceType].price.add(
                item.discountItems.map((item) => new Decimal(item.discountAmount)).reduce((pre, current) => current.add(pre)),
            )
        })
        discountDataByType.freightPrice.price = $props.order.shopOrders
            .flatMap((shopOrder) => shopOrder.shopOrderItems)
            .map((shopOrderItem) => new Decimal(shopOrderItem.freightPrice))
            .reduce((pre, current) => current.add(pre))
    },
    {
        immediate: true,
    },
)
/*
 *function
 */
const totalPrice = () => {
    if ($props.order.shopOrders && $props.order.shopOrders[0].shopOrderItems) {
        return $props.order.shopOrders[0].shopOrderItems.reduce((p, item) => {
            return divTenThousand(item.dealPrice).mul(item.num).add(p)
        }, new Decimal(0))
    }
}
</script>

<template>
    <div v-if="$props.order.orderReceiver" class="orderInfo">
        <div class="orderInfo__title">订单状态：{{ getAfsOrderInfoStatusCn($props.order.status) }}</div>
        <el-steps :active="2" align-center class="orderInfo__steps">
            <el-step title="提交订单" :description="$props.order.createTime" />
            <el-step title="买家已付款" :description="$props.order.orderPayment.payTime" />
            <el-step title="商家发货" :description="$props.order?.shopOrderPackages ? $props.order?.shopOrderPackages[0].deliveryTime : ''" />
            <el-step title="买家收货" :description="$props.order?.shopOrderPackages ? $props.order?.shopOrderPackages[0].confirmTime : ''" />
        </el-steps>
        <div class="orderInfo__userInfo">
            <div class="orderInfo__userInfo--left">
                <div class="orderInfo__userInfo--title">物流信息</div>
                <div>用户昵称：{{ buyersData.nickname }}</div>
                <div>买家姓名：{{ $props.order.orderReceiver.name }}</div>
                <div>买家手机号：{{ $props.order.orderReceiver.mobile }}</div>
                <div>收货地址：{{ $props.order.orderReceiver.address }}</div>
                <div style="margin-bottom: 20px">买家留言：-</div>
            </div>
            <div class="orderInfo__userInfo--right">
                <div class="orderInfo__userInfo--title">订单信息</div>
                <div>订单编号：{{ $props.order.no }}</div>
                <div>创建时间：{{ $props.order.createTime }}</div>
                <div>支付方式：{{ payTypeFn($props.order.orderPayment.type, $props.order.orderPayment.payMode) }}支付</div>
                <div style="margin-bottom: 20px">付款时间：{{ $props.order.orderPayment.createTime }}</div>
            </div>
        </div>
        <el-table
            :cell-style="
                ({ row, column, rowIndex, columnIndex }:TabType) => {
                    if (columnIndex === 0)
                        return {
                            borderBottom: 'none',
                        }
                }
            "
            :span-method="useMergeCells"
            :data="$props.order.shopOrders[0].shopOrderItems"
            :get-span-arr="initSpanTag($props.order.shopOrders[0].shopOrderItems, 'no')"
            style="width: 100%"
            calss="orderInfo__tab"
            border
        >
            <el-table-column label="包裹订单" width="160" align="center">
                <template #default>
                    <div>{{ $props.order.no }}</div>
                </template>
            </el-table-column>
            <el-table-column label="商品" width="230" align="center">
                <template #default="{ row }">
                    <div class="orderInfo__tab--goods">
                        <el-image
                            fits="cover"
                            style="width: 70px; height: 70px"
                            shape="square"
                            size="large"
                            :src="row.image"
                            :title="row.productName"
                        />
                        <div class="orderInfo__tab--goods-right">
                            <div class="orderInfo__tab--goods-right-show">{{ row.productName }}</div>
                            <div>{{ row.specs }}</div>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="单价&amp;数量" align="center">
                <template #default="{ row }">
                    <div class="orderInfo__tab--price">
                        <div>单价： ￥{{ divTenThousand(row.dealPrice) }}</div>
                        <div>数量：&nbsp;*{{ row.num }}</div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="优惠" align="center">
                <template #default>
                    <div>积分抵扣:--</div>
                    <div>满减优惠:{{ divTenThousand(discountDataByType.FULL_REDUCTION.price) }}</div>
                    <div>优惠券优惠:{{ divTenThousand(discountDataByType.SHOP_COUPON.price) }}</div>
                    <div>会员优惠:{{ divTenThousand(discountDataByType.MEMBER_DEDUCTION.price) }}</div>
                </template>
            </el-table-column>
            <el-table-column label="总价" align="center">
                <template #default="{ row }">
                    <div>总价： ￥{{ divTenThousand(row.dealPrice).mul(row.num) }}</div>
                </template>
            </el-table-column>
        </el-table>
        <div class="orderInfo__footer">
            实收款：<span>{{ totalPrice() }}</span>
            元
        </div>
    </div>
    <el-empty v-else description="暂无数据~" />
</template>

<style scoped lang="scss">
@include b(orderInfo) {
    @include e(title) {
        font-size: 18px;
        font-weight: Bold;
        color: #515151;
    }
    @include e(steps) {
        height: 122px;
        margin: 22px 0;
        display: flex;

        // border: 1px solid #d5d5d5;
    }
    @include e(footer) {
        text-align: right;
        & span:only-child {
            font-size: 22px;
            font-weight: bold;
            color: #2e99f3;
        }
    }
    @include e(userInfo) {
        display: flex;
        background: #f7f8fa;
        margin-bottom: 22px;
        padding: 0 30px;
        @include m(left) {
            flex: 0.5;
            & div:nth-of-type(n + 2) {
                margin-bottom: 11px;
            }
        }
        @include m(right) {
            flex: 0.5;
            & div:nth-of-type(n + 2) {
                margin-bottom: 11px;
            }
        }
        @include m(title) {
            font-size: 14px;
            color: #333333;
            font-weight: Bold;
            margin: 17px 0;
        }
    }
    @include e(tab) {
        @include m(goods) {
            display: flex;
            justify-content: space-between;
        }
        @include m(goods-right) {
            font-size: 12px;
            color: #586884;
            width: 132px;
            margin-left: 10px;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            flex-direction: column;
        }
        @include m(goods-right-show) {
            @include utils-ellipsis(2);
        }
        @include m(price) {
            display: flex;
            font-size: 12px;
            color: #50596d;
            justify-content: center;
            align-items: flex-start;
            flex-direction: column;
        }
    }
}

//去除table的每一条数据的下边框
.el-table td {
    border-bottom: none;
}
//去除表格的最下面一行的边框
.tableStyle::before {
    width: 0;
}
//设置表的外边框
.el-table {
    border-radius: 9px;
    border: 1px solid #d5d5d5;
}
.el-table th.is-leaf {
    border-bottom: none;
}
// :deep.el-step__description {
//     height: 20px;
//     line-height: 20px;
// }
</style>
