/*
 * @description:
 * @Author: lexy
 * @Date: 2022-08-22 17:14:20
 * @LastEditors: lexy
 * @LastEditTime: 2022-10-20 17:15:28
 */
import { ShopOrder, OrderReceiver, ORDERPAYMENT, ORDERPAYMENTSTATUS, ORDERTYPE } from '@/views/order/types/order'
import { A_REfUND_WHY, ARefundType, AFSSTATUS, PACKAGESTATUS } from '@/views/afs/types'
/**
 * @LastEditors: lexy
 * @param: WITHOUT  无需物流发货
 * @param: EXPRESS 普通发货 自己填 物流公司与 单号
 * @param: PRINT_EXPRESS 打印发货
 * @returns {*}
 */
export enum DeliverType {
    WITHOUT,
    EXPRESS,
    PRINT_EXPRESS,
}
/**
 * @LastEditors: lexy
 * @description: 后端返回售后列表查看详情订单数据
 * @returns {*}
 */
export interface ApiAfsOrder {
    buyerId: string
    createTime: string
    id: string
    no: string
    orderDiscounts: []
    orderPayment: OrderPayment
    orderReceiver: OrderReceiver
    payTimeOut: string
    shopOrders: ShopOrder[]
    status: keyof typeof ORDERPAYMENTSTATUS
    type: keyof typeof ORDERTYPE
    shopOrderPackages: ApiLogistics01[]
    updateTime: string
    histories?: []
}
interface OrderPayment {
    createTime: string
    discountAmount: string
    freightAmount: string
    id: string
    payAmount: string
    payTime: string
    payerId: string
    sn: string
    totalAmount: string
    type: keyof typeof ORDERPAYMENT
}
/**
 * @LastEditors: lexy
 * @description: 后端返回的User接口信息
 * @returns {*}
 */
export interface ApiBuyersData {
    avatar?: string
    createTime?: string
    deleted?: false
    gender?: string
    id: string
    nickname?: string
    updateTime?: string
    userId?: string
    version?: 0
}
/**
 * @LastEditors: lexy
 * @description: 后端返回协商历史信息
 * @returns {*}
 */
export interface ApiHistory {
    afsOrderItem: AfsOrderItem
    buyerId: string
    createTime: string
    expiredTime: string
    explain: string
    histories: History[]
    id: string
    no: string
    orderNo: string
    packageStatus: keyof typeof PACKAGESTATUS
    reason: keyof typeof A_REfUND_WHY
    refundAmount: string
    keyNodeTimeout: KeyNodeTimeout
    shopId: string
    shopLogo: string
    shopName: string
    shopOrderItemId: string
    status: keyof typeof AFSSTATUS
    type: keyof typeof ARefundType
    updateTime: string
    version: number
    supplierId: string
    supplierName?: string
    supplierLogo?: string
}
interface KeyNodeTimeout {
    confirmReturnedTimeout: string
    requestAgreeTimeout: string
    returnedTimeout: string
}
interface AfsOrderItem {
    createTime: string
    dealPrice: string
    image: string
    num: number
    productId: string
    productName: string
    salePrice: string
    services: string[]
    skuId: string
    specs: string[]
    updateTime: string
    version: number
}
interface History {
    afsStatus: keyof typeof AFSSTATUS
    createTime: string
    evidences: string[]
    id: string
    packageStatus: keyof typeof PACKAGESTATUS
    remark: string
    updateTime: string
    operator: HISTORY_OPERATOR_TYPE
}
export enum History_Operator {
    CONSUMER = 'CONSUMER',
    SUPPLIER = 'SUPPLIER_CONSOLE',
    PLATFORM = 'PLATFORM_CONSOLE',
    SHOP = 'SHOP_CONSOLE',
}
type ToUnion<T extends Record<string, string>> = keyof {
    [Prop in keyof T as `${T[Prop]}`]: Prop
}
export type HISTORY_OPERATOR_TYPE = ToUnion<typeof History_Operator>
export interface ApiShopInfo {
    id: string
    logo?: string
    name?: string
}
export interface ApiLogistics01 {
    createTime: string
    deleted: false
    deliveryTime?: string
    expressCompanyCode: string
    expressCompanyName: string
    expressNo: string
    id: string
    orderNo: string
    receiverAddress: string
    receiverMobile: string
    receiverName: string
    remark: string
    shopId: string
    status: keyof typeof PACKAGESTATUS
    type: keyof typeof DeliverType
    updateTime: string
    version: 0
    // 确认收货时间
    confirmTime?: string
    //  评论时间
    commentTime?: string
    success: true
}
