<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-10-12 09:26:37
 * @LastEditors: lexy
 * @LastEditTime: 2022-12-22 14:23:14
-->
<script setup lang="ts">
import SearchForm from '@/views/baseVip/components/search-form.vue'
import QDropdownBtn from '@/components/q-btn/q-dropdown-btn.vue'
import BaseVipTable from '@/views/baseVip/components/base-vip-table.vue'
import { doGetUserTag } from '@/apis/vip'
import { ElMessage } from 'element-plus'
import VipImport from './components/import.vue'
import type { ApiTagItem, ParamsSearchVipBase } from '@/views/baseVip/types'
import { Ref } from 'vue'
import request from '@/apis/request'
import { cloneDeep } from 'lodash'
/*
 *variable
 */
let searchParams: ParamsSearchVipBase = {} as ParamsSearchVipBase
const sortType = ref('3')
const filterSkipValOptions = ref([
    { label: '按交易总额降序', value: '1' },
    { label: '按交易总额升序', value: '2' },
    { label: '按成为会员时间升序', value: '3' },
    { label: '按成为会员时间降序', value: '4' },
])
const searchFromChangeVal = ref(false)
const tableRef: Ref<InstanceType<typeof BaseVipTable> | null> = ref(null)
const importDialogShow = ref(false)
const vipImportRef: Ref<InstanceType<typeof VipImport> | null> = ref(null)

/*
 *lifeCircle
 */
/*
 *function
 */
// openLabelView
const handleDropdownLeft = () => {
    tableRef.value?.batchGiftsCoupons()
}
const handleConfirmImport = async () => {
    vipImportRef.value?.uploadExcelRef?.submit()
}
const handleDropdownRight = (e: string) => {
    switch (e) {
        case 'Tags':
            openTagePopUp()
            break
        case 'import':
            // TODO 导入逻辑
            importDialogShow.value = true
            break
        case 'export':
            // eslint-disable-next-line no-case-declarations
            const serviceSearchParams: any = cloneDeep(searchParams)
            Object.keys(serviceSearchParams).forEach((key) => {
                if (!serviceSearchParams[key]) delete serviceSearchParams[key]
            })
            request
                .post(
                    'gruul-mall-user/user/export',
                    {
                        ...serviceSearchParams,
                        total: tableRef.value?.pageConfig.total,
                        sortType: 3,
                        ids: tableRef.value?.tableSelectedArr.map((item) => item.id),
                    },
                    {
                        responseType: 'arraybuffer',
                    },
                )
                .then((res) => {
                    const blob = new Blob([res.data], { type: 'application/vnd.ms-excel;charset=utf-8' })
                    const elink = document.createElement('a')
                    elink.href = URL.createObjectURL(blob)
                    elink.download = '批量导出客户.xls'
                    elink.click()
                })
            // TODO 导出逻辑
            break
        case 'Blacklist':
            tableRef.value?.batchBlack()
            break
        default:
            break
    }
}
const handleSearchData = (e: ParamsSearchVipBase) => {
    searchParams = e
    tableRef.value!.tableSelectedArr = []
    tableRef.value?.initBaseVipList(e)
}
const openTagePopUp = () => {
    tableRef.value?.openLabelView()
}
const handleSelect = () => {
    // 等待 sortType 的值改变后传递给子组件使用
    const time = setTimeout(() => {
        tableRef.value?.initBaseVipList()
        clearTimeout(time)
    }, 100)
}
</script>

<template>
    <search-form @search-data="handleSearchData" @change-show="searchFromChangeVal = $event" />
    <div class="grey_bar"></div>
    <div class="handle_container" style="padding-top: 16px">
        <div class="filterSkipVal-box">
            <q-dropdown-btn
                title="赠送优惠券"
                :option="[
                    { label: '导入', name: 'import' },
                    { label: '导出', name: 'export' },
                    { label: '设置标签', name: 'Tags' },
                    { label: '加入黑名单', name: 'Blacklist' },
                ]"
                @left-click="handleDropdownLeft"
                @right-click="handleDropdownRight"
            />
            <el-select v-model="sortType" class="m-2" placeholder="请选择筛选条件" @change="handleSelect">
                <el-option v-for="item in filterSkipValOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
        </div>
    </div>
    <base-vip-table ref="tableRef" :sort-type="sortType" :search-from-change-val="searchFromChangeVal" />
    <el-dialog v-model="importDialogShow" title="导入" destroy-on-close>
        <VipImport ref="vipImportRef" @import-success="importDialogShow = false" />
        <template #footer>
            <el-button @click="importDialogShow = false">取 消</el-button>
            <el-button type="primary" @click="handleConfirmImport">确 定</el-button>
        </template>
    </el-dialog>
</template>

<style scoped lang="scss">
@include b(filterSkipVal-box) {
    @include flex(space-between);
    margin: 15px 0;
}

:deep(.base-vip-table-Up) {
    height: calc(100vh - 330px);
}
</style>
