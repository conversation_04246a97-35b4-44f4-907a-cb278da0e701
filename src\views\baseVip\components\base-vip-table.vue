<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-10-13 09:31:24
 * @LastEditors: lexy
 * @LastEditTime: 2023-09-20 19:04:16
-->
<script setup lang="ts">
import { onBeforeMount } from 'vue'
import { useRouter } from 'vue-router'
import QTable from '@/components/qszr-core/packages/q-table/QTable'
import QTableColumn from '@/components/qszr-core/packages/q-table/q-table-column.vue'
import LabelViewDialog from '@/views/baseVip/components/label-view-dialog.vue'
import PageManage from '@/components/PageManage.vue'
import QDropdownBtn from '@/components/q-btn/q-dropdown-btn.vue'
import VipIntegralDropdown from '@/q-plugin/integral/VipIntegralDropdown.vue'
import BalanceTopUpDialog from '@/views/baseVip/components/balance-top-up-dialog.vue'
import { ElMessage } from 'element-plus'
import { doGetBaseVipList } from '@/apis/vip'
import type { ApiBaseVipListItem, ApiTagItem, ParamsSearchVipBase } from '@/views/baseVip/types'
import useDisableUserHooks from '../hooks/useDisableUserHooks'
/*
 *variable
 */

const $props = defineProps({
    searchFromChangeVal: {
        type: Boolean,
        default: true,
    },
    sortType: { type: String, default: '3' },
})
const $router = useRouter()
const { divTenThousand } = useConvert()
const pageConfig = reactive({
    size: 20,
    current: 1,
    total: 0,
})
const tableSelectedArr = ref<ApiBaseVipListItem[]>([])
const vipBaseList = ref<ApiBaseVipListItem[]>([])
// 标签
const labelViewData = reactive({
    labelView: false,
    currentUserIds: [] as string[],
    currentUserTagIds: [] as string[],
})
const changeType = ref<'BALANCE' | 'INTEGRAL' | 'GROWTHVALUE'>('BALANCE')
const { labelView, currentUserIds, currentUserTagIds } = toRefs(labelViewData)
// 余额充值
const topUpBalance = reactive({
    isShowTopUpBalance: false,
    currentBalance: {
        balance: '',
        consumeCount: 0,
        createTime: '',
        dealTotalMoney: '',
        distributionCount: 0,
        id: '',
        integralTotal: '',
        remark: '',
        userHeadPortrait: '',
        userPhone: '',
        userNickname: '',
        userTagVOList: [{ tagId: '', tagName: '' }],
    },
})
const { isShowTopUpBalance, currentBalance } = toRefs(topUpBalance)
const blackDialog = ref(false)
const currentUserId = ref<string[]>([])

const { blackDialogFormModel, handleBlackConfirm } = useDisableUserHooks()
/*
 *lifeCircle
 */
onBeforeMount(() => {
    initBaseVipList()
})

/*
 *function
 */
async function initBaseVipList(params?: ParamsSearchVipBase) {
    const { data, code } = await doGetBaseVipList({ ...params, ...pageConfig, sortType: $props.sortType })
    if (code !== 200) return ElMessage.error('获取会员信息失败')
    vipBaseList.value = data.records
    tableSelectedArr.value = []
    pageConfig.total = data.total
}

const handleOperation = (val: string, row: ApiBaseVipListItem) => {
    switch (val) {
        case 'Balance':
            changeType.value = 'BALANCE'
            currentBalance.value = row
            isShowTopUpBalance.value = true
            break
        case 'growthValue':
            changeType.value = 'GROWTHVALUE'
            currentBalance.value = row
            isShowTopUpBalance.value = true
            break
        case 'black':
            blackDialogFormModel.roleList = []
            currentUserId.value = [row.userId]
            blackDialog.value = true
            break
        default:
            break
    }
}
// 分页
const handleSizeChange = (value: number) => {
    pageConfig.current = 1
    pageConfig.size = value
    initBaseVipList()
}
const handleCurrentChange = (value: number) => {
    pageConfig.current = value
    initBaseVipList()
}
const handleTagEdit = (row: any) => {
    currentUserIds.value = [row.userId]
    if (row.userTagVOList.length) {
        currentUserTagIds.value = row.userTagVOList.map((item: { tagId: string; tagName: string }) => item.tagId)
        console.log('currentUserTagIds.value', currentUserTagIds.value)
    }
    labelView.value = true
}
const openLabelView = () => {
    if (!tableSelectedArr.value.length) {
        return ElMessage.info('请选择操作用户')
    }
    currentUserIds.value = tableSelectedArr.value.map((item) => item.userId)
    labelView.value = true
}
/**
 * @LastEditors: lexy
 * @description: 批量赠送
 * @returns {*}
 */
const batchGiftsCoupons = () => {
    if (!tableSelectedArr.value.length) {
        return ElMessage.info('请选择操作用户')
    }
    const userIds = tableSelectedArr.value.map((item) => item.userId)
    $router.push({
        name: 'memberCouponBaseInfo',
        query: { ids: JSON.stringify(userIds) },
    })
}

/**
 * @LastEditors: lexy
 * @description:  批量加入黑名单
 * @returns {*}
 */
function batchBlack() {
    if (!tableSelectedArr.value.length) {
        return ElMessage.info('请选择操作用户')
    }
    blackDialogFormModel.roleList = []
    currentUserId.value = tableSelectedArr.value.map((item) => item.userId)
    blackDialog.value = true
}
const handleCloseBlackDialog = () => {
    blackDialogFormModel.explain = ''
    blackDialogFormModel.roleList = []
}

defineExpose({
    tableSelectedArr,
    openLabelView,
    initBaseVipList,
    batchGiftsCoupons,
    batchBlack,
    pageConfig,
})
</script>
<template>
    <q-table
        v-model:checked-item="tableSelectedArr"
        :data="vipBaseList"
        class="base-vip-table"
        :selection="true"
        :class="{ 'base-vip-table-Up': !$props.searchFromChangeVal }"
    >
        <template #header="{ row }">
            <div class="base-vip-table-top">
                <div class="base-vip-table-top--time">注册时间:{{ row.createTime }}</div>
            </div>
        </template>
        <q-table-column label="客户信息" width="400">
            <template #default="{ row }">
                <div class="customer-Infor">
                    <el-image
                        class="customer-Infor__img"
                        fit="cover"
                        style="width: 40px; height: 40px; margin-right: 10px"
                        :src="row.userHeadPortrait"
                    />
                    <div class="customer-nick-Infor">
                        <div class="ellipsis">
                            <span class="customer-nick-Infor__nickname"> {{ row.userNickname }}</span>
                            <span v-if="row.userPhone" class="customer-nick-Infor__phone">{{ row.userPhone }}</span>
                        </div>
                        <div class="ellipsis">
                            <div>
                                <span>{{ row.memberType === 'PAID_MEMBER' ? '付费会员' : '免费会员' }}</span>
                                <span>({{ row.memberType === 'PAID_MEMBER' ? 'SVIP' : 'VIP' }}{{ row.rankCode }})</span>
                            </div>
                            <br />
                            <span class="customer-nick-Infor__member">成长值：{{ row.growthValue }}</span>
                        </div>
                    </div>
                </div>
            </template>
        </q-table-column>
        <q-table-column label="资产(消费)信息">
            <template #default="{ row }">
                <div class="money-text">
                    <el-row justify="space-between" align="middle">
                        <span class="money-text__label">储值余额：</span>
                        <div class="money-text__value money-text__red">{{ divTenThousand(row.balance) }}</div>
                    </el-row>
                    <el-row justify="space-between" align="middle">
                        <span class="money-text__label">消费总金额：</span>
                        <span class="money-text__value">{{ divTenThousand(row.dealTotalMoney) }}</span>
                    </el-row>
                </div>
            </template>
        </q-table-column>
        <q-table-column label="标签" width="200">
            <template #default="{ row }">
                <p
                    v-if="row.userTagVOList.length"
                    class="customer-nick-Infor__box"
                    c
                    :title="row.userTagVOList.map((item:ApiTagItem) => item.tagName).join(',')"
                    @click="handleTagEdit(row)"
                >
                    <span style="color: #2e99f3; font-size: 10px; margin-right: 5px" class="customer-nick-Infor__tags">
                        {{ row.userTagVOList.map((item: ApiTagItem) => item.tagName).join('、') }}
                    </span>
                    <!-- <span style="color: #838383; font-size: 10px" class="customer-nik-Infor--label"> 等共{{ row.userTagVOList.length }}个标签 </span> -->
                </p>
            </template>
        </q-table-column>
        <q-table-column prop="sex" label="操作" width="200">
            <template #default="{ row }">
                <q-dropdown-btn
                    title="查看"
                    :option="[
                        { label: '储值调整', name: 'Balance' },
                        { label: '成长值调整', name: 'growthValue' },
                        { label: '加入黑名单', name: 'black' },
                    ]"
                    :slot-index="1"
                    @right-click="handleOperation($event, row)"
                    @left-click="
                        $router.push({
                            name: 'vipDetailsIndex',
                            query: {
                                userId: row.userId,
                            },
                        })
                    "
                >
                    <vip-integral-dropdown :user-id="row.userId" />
                </q-dropdown-btn>
            </template>
        </q-table-column>
    </q-table>
    <el-row justify="space-between" align="middle">
        <slot name="batch">
            <span></span>
        </slot>
        <page-manage
            :page-size="pageConfig.size"
            :page-num="pageConfig.current"
            :total="pageConfig.total"
            @reload="initBaseVipList"
            @handle-size-change="handleSizeChange"
            @handle-current-change="handleCurrentChange"
        />
    </el-row>
    <!-- 黑名单弹窗 -->
    <el-dialog v-model="blackDialog" title="权限设置" @close="handleCloseBlackDialog">
        <el-form>
            <el-form-item label="拉黑原因">
                <el-input v-model="blackDialogFormModel.explain" placeholder="请输入拉黑原因" />
            </el-form-item>
            <el-form-item label="权限">
                <el-checkbox-group v-model="blackDialogFormModel.roleList">
                    <el-checkbox label="FORBIDDEN_COMMENT">禁止评论</el-checkbox>
                    <el-checkbox label="FORBIDDEN_ORDER">禁止下单</el-checkbox>
                </el-checkbox-group>
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button @click="blackDialog = false">取消</el-button>
            <el-button
                type="primary"
                @click="
                    handleBlackConfirm(currentUserId, () => {
                        blackDialog = false
                        initBaseVipList()
                    })
                "
                >确定</el-button
            >
        </template>
    </el-dialog>
    <label-view-dialog v-model:label-view="labelView" :user-ids="currentUserIds" :current-user-tag-ids="currentUserTagIds" @reset="initBaseVipList" />
    <balance-top-up-dialog
        v-model:top-up-balance="isShowTopUpBalance"
        :base-vip-list-item="currentBalance"
        :change-type="changeType"
        @reset="initBaseVipList"
    />
</template>

<style scoped lang="scss">
@include b(base-vip-table) {
    height: calc(100vh - 450px);
    transition: height 0.5s;
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
        display: none;
    }
}

@include b(base-vip-table-top) {
    @include flex(flex-start);
    width: 100%;
    @include m(no) {
    }
    @include m(time) {
        padding: 0 20px;
    }
}

@include b(customer-Infor) {
    width: 300px;
    height: 80px;
    @include flex(flex-start);
    @include e(img) {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        flex-shrink: 0;
    }
}

@include b(customer-nick-Infor) {
    // height: 80px;
    @include flex(space-around, center);
    // flex-direction: column;
    @include e(nickname) {
        font-size: 1.2em;
        font-weight: bold;
    }
    @include e(phone) {
        font-size: 1.2em;
        font-weight: bold;
        margin-top: 10px;
        display: block;
    }
    @include e(member) {
        margin-top: 10px;
    }
    @include e(box) {
        padding: 0 15px;
    }
    @include e(tags) {
        display: inline-block;
        max-width: 100px;
        @include utils-ellipsis(3);
    }
    @include m(label) {
        cursor: pointer;
        &::after {
            content: '';
            display: inline-block;
            margin: 0 0 2px 2px;
            width: 0;
            height: 0;
            vertical-align: middle;
            border-top: 5px solid #000;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
        }
    }
}

@include b(base-vip-table-Up) {
    // height: calc(100vh - 300px);
    height: calc(100vh - 270px);
}

@include b(money-text) {
    @include e(value) {
        font-size: 1.3em;
        font-weight: 600;
    }
    @include e(red) {
        color: #fd0505;
    }
}

.ellipsis {
    width: 180px;
    @include utils-ellipsis;
}
</style>
