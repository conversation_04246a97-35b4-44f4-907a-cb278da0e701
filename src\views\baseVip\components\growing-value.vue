<!--
 * @description: 成长值记录
 * @Author: lexy
 * @Date: 2022-10-13 19:56:02
 * @LastEditors: lexy
 * @LastEditTime: 2022-10-14 09:15:57
-->
<script setup lang="ts">
/*
 *variable
 */
const tableData = []
/*
 *lifeCircle
 */
/*
 *function
 */
</script>

<template>
    <el-table :data="tableData" style="width: 100%" :header-row-style="{ background: '#f6f8fa' }" :row-style="{ height: '60px' }">
        <el-table-column prop="createTime" label="变动时间" />
        <el-table-column prop="title" label="订单编号" />
        <el-table-column prop="createTime" label="成长值" />
        <el-table-column label="变更说明"> </el-table-column>
    </el-table>
    <!-- <page-manage
        :page-size="pageConfig.pageSize"
        :page-num="pageConfig.pageNum"
        :total="pageConfig.total"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
        @reload="GetNewsList"
    /> -->
</template>

<style scoped></style>
