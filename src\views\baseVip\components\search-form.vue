<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-06-09 18:42:45
 * @LastEditors: lexy
 * @LastEditTime: 2023-02-10 13:35:35
-->
<script setup lang="ts">
import { doGetUserTag } from '@/apis/vip'
import MCard from '@/components/MCard.vue'
import { useVipTagsStore } from '@/store/modules/vipSetting'
import { ElMessage } from 'element-plus'
// /*
//  *variable
//  */

const $emit = defineEmits(['search-data', 'changeShow'])
const $useVipTagsStore = useVipTagsStore()
const ShowMCard = ref(false)
// 下拉选择状态初始数据
const searchFromData = reactive({
    userCardNum: '', // 会员卡号
    userNickname: '', // 用户名称
    tagId: '', //标签id
    userPhone: '', // 用户手机
    memberType: '', // 用户类型
    clinchTime: ['', ''],
    rankCode: '',
    consigneeName: '', // 收货人姓名
})
/*
 *lifeCircle
 */
watch(
    () => ShowMCard.value,
    (val) => {
        $emit('changeShow', val)
    },
)
/*
 *function
 */
const HandleSearch = () => {
    const { userCardNum, userNickname, consigneeName, tagId, userPhone, memberType, rankCode } = searchFromData
    const params = {
        userCardNum,
        userNickname,
        consigneeName,
        registrationStartTime: '',
        registrationEndTime: '',
        tagId,
        userPhone,
        memberType,
        rankCode,
    }
    if (Array.isArray(searchFromData.clinchTime)) {
        params.registrationStartTime = searchFromData.clinchTime[0]
        params.registrationEndTime = searchFromData.clinchTime[1]
    }
    $emit('search-data', params)
}
const handleReset = () => {
    searchFromData.clinchTime = ['', '']
    searchFromData.consigneeName = ''
    searchFromData.userCardNum = ''
    searchFromData.userNickname = ''
    searchFromData.tagId = ''
    searchFromData.userPhone = ''
    searchFromData.memberType = ''
    searchFromData.rankCode = ''
    HandleSearch()
}

const searchTagList = ref<any[]>([])
async function initGetUserTag() {
    const { code, data } = await doGetUserTag({ bound: true })
    if (code !== 200) return ElMessage.error('会员标签获取失败')
    searchTagList.value = data
}
initGetUserTag()
</script>

<template>
    <!-- 搜索部分s -->
    <div class="form">
        <el-form class="form-flex">
            <MCard v-model="ShowMCard">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="用户名称" label-width="90px">
                            <el-input v-model="searchFromData.userNickname" placeholder="请输入用户名称" maxlength="20" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="手机号" label-width="90px">
                            <el-input v-model="searchFromData.userPhone" placeholder="请输入手机号" maxlength="11" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="会员类型" label-width="90px">
                            <el-select v-model="searchFromData.memberType" placeholder="请选择会员类型">
                                <el-option label="全部" value="" />
                                <el-option label="付费会员" value="PAID_MEMBER" />
                                <el-option label="免费会员" value="FREE_MEMBER" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="会员等级" label-width="90px">
                            <el-input v-model="searchFromData.rankCode" placeholder="请输入会员等级" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="注册时间" label-width="90px">
                            <el-date-picker
                                v-model="searchFromData.clinchTime"
                                format="YYYY/MM/DD"
                                value-format="YYYY-MM-DD"
                                type="daterange"
                                start-placeholder="开始时间"
                                end-placeholder="结束时间"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="标签" label-width="90px">
                            <el-select v-model="searchFromData.tagId" placeholder="全部" maxlength="20">
                                <el-option label="全部" value=" " />
                                <el-option v-for="item in searchTagList" :key="item.id" :label="item.tagName" :value="item.id" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-button class="form__btn" type="primary" round @click="HandleSearch">搜索</el-button>
                        <el-button class="form__btn" type="primary" round @click="handleReset">重置</el-button>
                    </el-col>
                    <!-- <el-col :span="8">
                        <el-form-item label="会员类型" label-width="90px">
                            <el-select v-model="searchFromData.userNickname" placeholder="全部" maxlength="20">
                                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col> -->

                    <!-- <el-col :span="8">
                        <el-form-item label="会员等级" label-width="90px">
                            <el-select v-model="searchFromData.userNickname" placeholder="全部" maxlength="20">
                                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col> -->
                </el-row>
            </MCard>
        </el-form>
    </div>
    <!-- 搜索部分e -->
</template>

<style lang="scss" scoped>
@include b(form) {
    background: #f9f9f9;
    @include e(btn) {
        margin-left: 20px;
    }
}
</style>
