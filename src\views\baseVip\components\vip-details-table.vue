<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-10-13 19:42:28
 * @LastEditors: lexy
 * @LastEditTime: 2023-02-18 09:15:40
-->
<script setup lang="ts">
import { shallowRef } from 'vue'
import VipIntegralSubsidiary from '@/q-plugin/integral/VipIntegralSubsidiary.vue'
import VipDistributionInformation from '@/q-plugin/distribution/VipDistributionInformation.vue'

type TabActive = 'transactionDetails' | 'theBalanceOfSubsidiary' | 'theIntegralSubsidiary' | 'distributionOfInformation' | 'growingValueTo'
/*
 *variable
 */
const tabActive = ref<TabActive>('transactionDetails')
const tabsArr = ref([
    { label: '交易明细', name: 'transactionDetails' },
    { label: '储值明细', name: 'theBalanceOfSubsidiary' },
])
const $props = defineProps({
    userId: {
        type: String,
        default: '',
    },
})
const asyncComponent = shallowRef({
    transactionDetails: defineAsyncComponent(() => import('./transaction-details.vue')),
    theBalanceOfSubsidiary: defineAsyncComponent(() => import('./balance-subsidiary.vue')),
    // distributionOfInformation: defineAsyncComponent(() => import('./distribution-information.vue')),
    // growingValueTo: defineAsyncComponent(() => import('./growing-value.vue')),
})
/*
 *lifeCircle
 */
/*
 *function
 */
</script>

<template>
    <el-tabs v-model="tabActive" class="demo-tabs">
        <el-tab-pane v-for="item in tabsArr" :key="item.label" :name="item.name" :label="item.label" />
        <vip-integral-subsidiary :user-id="$props.userId" />
        <vip-distribution-information :user-id="$props.userId" />
    </el-tabs>
    <template v-if="$props.userId && asyncComponent[tabActive]">
        <component :is="asyncComponent[tabActive]" :user-id="$props.userId"></component>
    </template>
</template>

<style scoped lang="scss"></style>
