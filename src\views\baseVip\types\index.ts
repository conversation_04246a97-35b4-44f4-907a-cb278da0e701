/*
 * @description:
 * @Author: lexy
 * @Date: 2022-10-15 09:29:30
 * @LastEditors: lexy
 * @LastEditTime: 2022-12-21 13:35:40
 */
/**
 * @LastEditors: lexy
 * @description: 普通会员列表
 * @returns {*}
 */
export interface ApiBaseVipListItem {
    [key: string]: any
    balance: string
    consumeCount: number
    createTime: string
    dealTotalMoney: string
    distributionCount: number
    id: string
    userId: string
    integralTotal: string
    remark: string
    userHeadPortrait: string
    userPhone: string
    userNickname: string
    userTagVOList: [{ tagId: string; tagName: string }]
}
/**
 * @LastEditors: lexy
 * @description: 会员标签
 * @returns {*}
 */
export interface ApiTagItem {
    id: string
    tagName: string
    option: false
}
/**
 * @LastEditors: lexy
 * @description: 会员搜索参数
 * @returns {*}
 */
export interface ParamsSearchVipBase {
    userCardNum: string
    userNickname: string
    consigneeName: string
    registrationStartTime: string
    registrationEndTime: string
    tagId: string
    sortType: string
    userId: string
}
export interface ApiMemberInfoType {
    [key: string]: any
    balance: string
    createTime: string
    dealTotalMoney: string
    gender: string
    growthValue: string
    id: string
    userHeadPortrait: string
    userId: string
    userNickname: string
    userPhone: string
    userTagVOList: { tagId: string; tagName: string }[]
}
export enum PAYTYPE {
    SYSTEM_GIVE,
    PERSONAL_CHARGING,
    SYSTEM_CHARGING,
    SHOPPING_PURCHASE,
    PURCHASE_MEMBER,
    REFUND_SUCCEED,
    WITHDRAW,
    GIVE,
}
