<template>
    <el-dialog v-model="visible" title="调用详情" width="700px" @close="onClose" :close-on-click-modal="false">
        <div class="detail-section">
            <h4>基本信息</h4>
            <el-descriptions :column="2" border>
                <el-descriptions-item label="业务线">{{ row?.businessType }}</el-descriptions-item>
                <el-descriptions-item label="服务商">{{ row?.serviceType }}</el-descriptions-item>
                <el-descriptions-item label="服务名">{{ row?.serviceName }}</el-descriptions-item>
                <el-descriptions-item label="调用服务">{{ row?.callServiceName }}</el-descriptions-item>
                <el-descriptions-item label="调用接口">{{ row?.callApi }}</el-descriptions-item>
                <el-descriptions-item label="状态">{{ row?.callStatus }}</el-descriptions-item>
                <el-descriptions-item label="是否计费">{{ row?.isCost ? '计费' : '不计费' }}</el-descriptions-item>
                <el-descriptions-item label="费用类型">{{ row?.costType }}</el-descriptions-item>
                <el-descriptions-item label="费用(元)">
                    {{ divTenThousand(row.amount) }}
                </el-descriptions-item>
                <el-descriptions-item label="请求时间">{{ row?.requestTime }}</el-descriptions-item>
                <el-descriptions-item label="耗时">{{ row?.costTime }}ms</el-descriptions-item>
            </el-descriptions>
        </div>
        <div class="detail-section">
            <h4>接口入参</h4>
            <el-table :data="requestParamTable" size="small" border style="width: 100%">
                <el-table-column prop="key" label="参数名" />
                <el-table-column prop="value" label="值" />
            </el-table>
        </div>
        <div class="detail-section">
            <h4>接口返回结果</h4>
            <el-input type="textarea" :rows="6" :model-value="responseResultStr" readonly />
        </div>
        <template #footer>
            <el-button @click="visible = false">关闭</el-button>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, computed, watch } from 'vue'
const props = defineProps({
    visible: Boolean,
    row: {
        type: Object,
        default: () => ({}),
    },
})
const emit = defineEmits(['close'])

const { divTenThousand } = useConvert()
const visible = ref(props.visible)
watch(
    () => props.visible,
    (val) => {
        visible.value = val
    },
)

const onClose = () => {
    visible.value = false
    emit('close')
}
// 解析接口入参 requestParam
const requestParamTable = computed(() => {
    let paramStr = props.row?.requestParam || ''
    try {
        const obj = JSON.parse(paramStr)
        if (typeof obj === 'object' && obj !== null) {
            return Object.entries(obj).map(([key, value]) => ({ key, value }))
        }
    } catch (e) {}
    return []
})
// 格式化接口返回结果 responseResult
const responseResultStr = computed(() => {
    let resStr = props.row?.responseResult || ''
    try {
        return JSON.stringify(JSON.parse(resStr), null, 2)
    } catch (e) {
        return resStr
    }
})
</script>

<style scoped>
.detail-section {
    margin-bottom: 18px;
}
</style>
