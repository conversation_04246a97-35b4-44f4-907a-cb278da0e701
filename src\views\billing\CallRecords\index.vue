<template>
    <search :business-type-list="businessTypeList" :service-type-list="serviceTypeList" @change-show="changeShow" @search="handleSearch" />
    <div class="grey_bar"></div>
    <div class="handle_container">
        <el-button type="primary" :disabled="!callRecordsData.length" @click="handleExport">导出</el-button>
    </div>
    <div class="table_container">
        <el-table
            :data="callRecordsData"
            size="large"
            stripe
            show-overflow-tooltip
            :height="tableHeight"
            :header-row-style="{ fontSize: '12px', color: '#909399' }"
            :header-cell-style="{ background: '#f6f8fa' }"
            :cell-style="{ fontSize: '12px', color: '#333333' }"
        >
            <el-table-column prop="businessType" label="业务线" width="100" :formatter="formatBusinessType" />
            <el-table-column prop="serviceType" label="服务商" width="100" :formatter="formatServiceType" />
            <el-table-column prop="serviceName" label="服务名" width="160" />
            <el-table-column prop="callServiceName" label="调用服务" width="180" />
            <el-table-column prop="callApi" label="调用接口" width="200" :tooltip-formatter="({ row }) => row.callApi">
                <template #default="{ row }">
                    <el-icon @click="handleCopy(row.callApi)"><i-ep-copy-document /></el-icon> {{ row.callApi }}
                </template>
            </el-table-column>
            <el-table-column prop="callStatus" label="状态">
                <template #default="{ row }">
                    <el-tag :type="row.callStatus === '成功' ? 'success' : 'danger'" effect="light">
                        {{ row.callStatus }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="isCost" label="是否计费" :formatter="formatIsCost" />
            <el-table-column prop="costType" label="费用类型" width="100" :formatter="formatCostType" />
            <el-table-column prop="amount" label="费用(元)">
                <template #default="{ row }">
                    {{ divTenThousand(row.amount) }}
                </template>
            </el-table-column>
            <el-table-column prop="requestTime" label="请求时间" width="160" />
            <el-table-column prop="costTime" label="耗时" :formatter="formatCostTime" />
            <el-table-column label="操作" width="140">
                <template #default="{ row }">
                    <el-button link type="primary" @click="handleDetail(row)">调用详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <BetterPageManage
            :page-num="pageConfig.current"
            :page-size="pageConfig.size"
            :total="pageConfig.total"
            @reload="initialData"
            @handle-size-change="handleSizeChange"
            @handle-current-change="handleCurrentChange"
        />
    </div>
    <CallDetailDialog :visible="detailDialogVisible" :row="detailRow" @close="handleDetailDialogClose" />
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import BetterPageManage from '@/components/BetterPageManage/BetterPageManage.vue'
import Search from './search.vue'
import { DictType, CostType, costTypeMap } from '@/apis/billing/model/type'
import { doGetBillingCostCode, doGetBillingRecordsList, doPostExportBillingRecords } from '@/apis/billing'
import CallDetailDialog from './detailDialog.vue'

interface TableRow {
    businessType: string
    serviceType: string
    serviceName: string
    callServiceName: string
    callApi: string
    callStatus: string // 直接用 string，实际为"成功"或"失败"
    isCost: boolean
    costType: CostType
    amount: string
    requestTime: string
    costTime: number
}

const { divTenThousand } = useConvert()
const callRecordsData = ref<TableRow[]>([])
const pageConfig = reactive({
    size: 10,
    current: 1,
    total: 0,
})
const loading = ref(false)
const tableHeight = ref('calc(100vh - 256px)')
const searchOptions = reactive<Record<string, any>>({
    businessType: '',
    serviceType: '',
    callServiceName: '',
    callApi: '',
    callStatus: '',
    isCost: '' as boolean | '',
    serviceName: '',
    startDate: '',
    endDate: '',
})

const detailDialogVisible = ref(false)
const detailRow = ref<TableRow>({} as TableRow)
const businessLineMap = ref<Record<string, string>>({} as Record<string, string>)
const serviceTypeMap = ref<Record<string, string>>({} as Record<string, string>)
const businessTypeList = ref<any[]>([])
const serviceTypeList = ref<any[]>([])

const initDictData = () => {
    Promise.all([doGetBillingCostCode('BUSINESS'), doGetBillingCostCode('SERVICE')]).then((results) => {
        if (results[0].data) {
            businessTypeList.value = ((results[0].data as DictType[]) || []).map((item) => {
                if (item.code && item.desc) {
                    businessLineMap.value[item.code] = item.desc
                }
                return { value: item.code, label: item.desc }
            })
        }
        if (results[1].data) {
            serviceTypeList.value = ((results[1].data as DictType[]) || []).map((item) => {
                if (item.code && item.desc) {
                    serviceTypeMap.value[item.code] = item.desc
                }
                return { value: item.code, label: item.desc }
            })
        }
    })
}

// 在组件挂载时检查URL参数
onMounted(() => {
    // init
    // 在组件挂载时初始化字典数据
    initDictData()
    // 初始化数据
    initialData()
})

const formatBusinessType = (row: TableRow) => {
    return businessLineMap.value[row.businessType] || row.businessType
}
const formatServiceType = (row: TableRow) => {
    return serviceTypeMap.value[row.serviceType] || row.serviceType
}
const formatIsCost = (row: TableRow) => {
    return row.isCost ? '计费' : '不计费'
}
const formatCostType = (row: TableRow) => {
    return costTypeMap[row.costType] || row.costType
}
const formatCostTime = (row: TableRow) => {
    return `${row.costTime}ms`
}
/**
 * 复制文本
 * @param text 文本
 */
const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text).then((res) => {
        ElMessage.success('复制成功！')
    })
}

const initialData = async () => {
    loading.value = true
    try {
        const { data } = await doGetBillingRecordsList({
            ...searchOptions,
            ...pageConfig,
        })
        if (data) {
            pageConfig.total = Number((data as any).total) || 0
            callRecordsData.value = (data as any).records || []
        }
    } catch (error) {
        console.error('Failed to fetch billing records:', error)
    } finally {
        loading.value = false
    }
}
const handleExport = async () => {
    loading.value = true
    try {
        const { data, code } = await doPostExportBillingRecords({
            ...searchOptions,
            ...pageConfig,
        })
        if (code === 200 && data) {
            ElMessage.success('导出数据发起成功！请去 下载中心 查找对应文件～')
        }
    } catch (error) {
        console.error('Failed to fetch billing records:', error)
    } finally {
        loading.value = false
    }
}

const handleSearch = (searchCondition: Record<string, any>) => {
    Object.keys(searchOptions).forEach((key) => {
        if (searchCondition?.[key] !== undefined) {
            searchOptions[key] = searchCondition?.[key]
        }
    })
    pageConfig.current = 1
    initialData()
}
const changeShow = (show: boolean) => {
    tableHeight.value = `calc(100vh - ${show ? 456 : 256}px)`
}
const handleDetail = (row: TableRow) => {
    detailRow.value = row as TableRow
    detailDialogVisible.value = true
}
const handleDetailDialogClose = () => {
    detailDialogVisible.value = false
}
const handleSizeChange = (value: number) => {
    pageConfig.current = 1
    pageConfig.size = value
    initialData()
}
const handleCurrentChange = (value: number) => {
    pageConfig.current = value
    initialData()
}
</script>

<style scoped>
.handle_container {
    margin-top: 16px;
}
.el-link + .el-link {
    margin-left: 8px;
}
</style>
