<template>
    <div class="form">
        <el-form class="form-flex">
            <MCard v-model="showCard">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="业务线" label-width="90px">
                            <el-select v-model="searchFormData.businessType" placeholder="请选择业务线，默认全部">
                                <el-option :value="''" label="全部" />
                                <el-option v-for="(item, index) in businessTypeList" :key="index" :value="item.value" :label="item.label" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="服务商" label-width="90px">
                            <el-select v-model="searchFormData.serviceType" placeholder="请选择服务商，默认全部">
                                <el-option :value="''" label="全部" />
                                <el-option v-for="(item, index) in serviceTypeList" :key="index" :value="item.value" :label="item.label" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="调用方" label-width="90px">
                            <el-input v-model="searchFormData.callServiceName" placeholder="请输入调用方，默认全部" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="调用接口" label-width="90px">
                            <el-input v-model="searchFormData.callApi" placeholder="请输入调用接口" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="状态" label-width="90px">
                            <el-select v-model="searchFormData.callStatus" placeholder="请选择状态，默认全部">
                                <el-option :value="''" label="全部" />
                                <el-option v-for="(item, index) in callStatusList" :key="index" :value="item.value" :label="item.label" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="是否计费" label-width="90px">
                            <el-select v-model="searchFormData.isCost" placeholder="请选择是否计费">
                                <el-option :value="''" label="全部" />
                                <el-option :value="true" label="计费" />
                                <el-option :value="false" label="不计费" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="服务名" label-width="90px">
                            <el-input v-model="searchFormData.serviceName" placeholder="请输入服务名" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="请求日期" label-width="90px">
                            <el-date-picker
                                v-model="searchFormData.requestDate"
                                type="daterange"
                                format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD"
                                start-placeholder="开始时间"
                                end-placeholder="结束时间"
                                clearable
                                range-separator="至"
                                :disabled-date="disabledDate"
                                :shortcuts="[
                                    {
                                        text: '最近一周',
                                        value: () => {
                                            const end = new Date()
                                            const start = new Date()
                                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
                                            return [start, end]
                                        },
                                    },
                                    {
                                        text: '最近一个月',
                                        value: () => {
                                            const end = new Date()
                                            const start = new Date()
                                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                                            return [start, end]
                                        },
                                    },
                                    {
                                        text: '最近三个月',
                                        value: () => {
                                            const end = new Date()
                                            const start = new Date()
                                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30 * 3)
                                            return [start, end]
                                        },
                                    },
                                ]"
                                style="width: 100%"
                                @calendar-change="handleCalendarChange"
                                @visible-change="handleVisibleChange"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-button class="form__btn" type="primary" round @click="handleSearch">搜索</el-button>
                        <el-button class="form__btn" round @click="handleReset">重置</el-button>
                    </el-col>
                </el-row>
            </MCard>
        </el-form>
    </div>
</template>

<script lang="ts" setup>
import { reactive, ref, watch } from 'vue'
import { cloneDeep } from 'lodash'
import { CallStatus, callStatusMap } from '@/apis/billing/model/type'
import { useRangeLimitedDate } from '@/hooks'
const { disabledDate, handleCalendarChange, handleVisibleChange } = useRangeLimitedDate(30 * 3, true, true)

const route = useRoute()
const router = useRouter()
const showCard = ref(false)
const $emit = defineEmits(['search', 'changeShow'])

const searchFormData = reactive({
    businessType: '',
    serviceType: '',
    callServiceName: '',
    callApi: '',
    callStatus: '' as CallStatus | '',
    isCost: '' as boolean | '',
    serviceName: '',
    requestDate: [] as string[],
    startDate: '',
    endDate: '',
})
type OptionType = { label: string; value: string }

// 接收从父组件传递的字典数据
const props = defineProps({
    businessTypeList: {
        type: Array<OptionType>,
        default: () => [],
    },
    serviceTypeList: {
        type: Array<OptionType>,
        default: () => [],
    },
})

const callStatusList = computed(() => Object.keys(callStatusMap).map((key: string) => ({ value: key, label: callStatusMap[key] })))

const handleSearch = () => {
    const cloneSearchForm: any = cloneDeep(searchFormData)
    if (Array.isArray(searchFormData.requestDate)) {
        cloneSearchForm.startDate = searchFormData.requestDate?.[0]
        cloneSearchForm.endDate = searchFormData.requestDate?.[1]
    }
    delete cloneSearchForm.requestDate
    $emit('search', cloneSearchForm)
}

const handleReset = () => {
    // 如果路径中带参数，也需要重置
    if (Object.keys(route?.query).length > 0) {
        router.replace({ path: route.path })
    }
    Object.keys(searchFormData).forEach((key) => {
        const k = key as keyof typeof searchFormData
        searchFormData[k] = '' as any
    })
    handleSearch()
}

watch(
    () => showCard.value,
    (val) => {
        $emit('changeShow', val)
    },
)

// 对账单会带着账期、服务类型、服务商等字段跳转到调用记录页面
// 在页面跳转过来时需要实现对参数的获取并自动查询的功能
// 从路由参数中获取查询条件并自动查询
// 检查URL中是否包含从对账单页面传递过来的参数
watch(
    () => route.query,
    ({ billTime, serviceType, serviceName }) => {
        // 如果存在这些参数，则更新搜索条件并执行查询
        if (billTime || serviceType || serviceName) {
            showCard.value = true
            // 更新搜索选项
            if (billTime) {
                // 获取billTime月份的第一天和最后一天
                const [year, month] = (billTime as string).split('-')
                // 计算月份的最后一天
                const lastDay = new Date(parseInt(year), parseInt(month), 0).getDate()
                // 设置日期范围
                searchFormData.startDate = `${year}-${month}-01`
                searchFormData.endDate = `${year}-${month}-${lastDay}`
                searchFormData.requestDate = [searchFormData.startDate, searchFormData.endDate]
            }
            if (serviceType) {
                searchFormData.serviceType = serviceType as string
            }
            if (serviceName) {
                searchFormData.serviceName = serviceName as string
            }
            handleSearch()
        }
    },
    { immediate: true, deep: true },
)
</script>

<style scoped>
.form-flex {
    display: flex;
    flex-direction: column;
}
.form__btn {
    margin-right: 12px;
}
</style>
