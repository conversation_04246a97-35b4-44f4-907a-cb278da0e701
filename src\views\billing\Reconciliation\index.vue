<template>
    <ReconciliationSearch :service-type-list="serviceTypeList" @search="handleSearch" @change-show="changeShow" />
    <div class="grey_bar"></div>
    <div class="handle_container">
        <el-button type="primary" :disabled="!costBillingData.length" @click="handleExport">导出</el-button>
    </div>
    <div class="table_container">
        <el-table
            :data="costBillingData"
            size="large"
            stripe
            :height="tableHeight"
            :header-row-style="{ fontSize: '12px', color: '#909399' }"
            :header-cell-style="{ background: '#f6f8fa' }"
            :cell-style="{ fontSize: '12px', color: '#333333' }"
        >
            <el-table-column prop="serviceType" label="服务商类型" width="100" />
            <el-table-column prop="serviceName" label="服务名称" />
            <el-table-column prop="amount" label="账单金额(元)" width="100">
                <template #default="{ row }">
                    {{ divTenThousand(row.amount) }}
                </template>
            </el-table-column>
            <el-table-column prop="createTime" label="对账时间" width="160" />
            <el-table-column prop="billTime" label="账期" width="100" />
            <el-table-column label="状态" width="100">
                <template #default="{ row }">
                    <el-tag :type="row.status === 'PRE' ? 'info' : row.status === 'YES' ? 'success' : 'danger'" effect="plain">
                        {{ confirmStatusMap[row.status] || row.status }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="userName" label="确认人" width="120" />
            <el-table-column prop="confirmTime" label="确认时间" width="160" />
            <el-table-column label="操作" width="220">
                <template #default="{ row }">
                    <el-space>
                        <el-link :underline="false" @click="handleCallRecord(row)">调用记录</el-link>
                        <template v-if="row.status === 'PRE'">
                            <el-dropdown>
                                <el-text class="icon-text" style="line-height: 24px"><i-ep-guide />确认</el-text>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item @click="handleConfirm(row)">
                                            <el-text class="icon-text" type="success"> <i-ep-check /> 无误 </el-text>
                                        </el-dropdown-item>
                                        <el-dropdown-item @click="handleException(row)">
                                            <el-text class="icon-text" type="warning"> <i-ep-close /> 异常 </el-text>
                                        </el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </template>
                        <el-button link type="primary" @click="handleExportRecord(row)">导出记录</el-button>
                    </el-space>
                </template>
            </el-table-column>
        </el-table>
        <BetterPageManage
            :page-num="pageConfig.current"
            :page-size="pageConfig.size"
            :total="pageConfig.total"
            @reload="initialData"
            @handle-size-change="handleSizeChange"
            @handle-current-change="handleCurrentChange"
        />
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ReconciliationSearch from './search.vue'
import BetterPageManage from '@/components/BetterPageManage/BetterPageManage.vue'
import { doGetCostBillingList, doPostConfirmCostBilling, doPostCostBillingList, doPostExportBillingRecords } from '@/apis/billing'
import { confirmStatusMap } from '@/apis/billing/model/type'
import { ElMessageBox } from 'element-plus'
import { doGetBillingCostCode } from '@/apis/billing'

const router = useRouter()
interface TableRow {
    id: string
    userId: string
    userName: string
    serviceType: string
    serviceName: string
    billTime: string
    status: string
    createTime: string
}
const { divTenThousand } = useConvert()
const costBillingData = ref<TableRow[]>([])
const pageConfig = reactive({
    size: 10,
    current: 1,
    total: 0,
})
const tableHeight = ref('calc(100vh - 256px)')
const loading = ref(false)
const searchOptions = reactive<Record<string, any>>({
    serviceType: '',
    serviceName: '',
    billTime: '',
    status: '',
    confirmDate: '',
})
const serviceTypeList = ref<Array<{ label: string; value: string }>>([])
const initDictData = () => {
    doGetBillingCostCode('SERVICE').then((res) => {
        serviceTypeList.value = (res.data || [])?.map((item) => ({ value: item.code, label: item.desc }))
    })
}

const handleSearch = (searchCondition: Record<string, any>) => {
    Object.keys(searchOptions).forEach((key) => {
        if (searchCondition?.[key] !== undefined) {
            searchOptions[key] = searchCondition?.[key]
        }
    })
    pageConfig.current = 1
    initialData()
}
const handleCallRecord = (row: any) => {
    // 拿取账期月份
    // 作为跳转调用记录页的入参
    // 从账单行中提取账期月份
    const billMonth = row.billTime
    const serviceType = serviceTypeList.value.find(({ label }) => row.serviceType === label)?.value || ''

    // 跳转到调用记录页面
    router.push({
        path: '/billing/call-records',
        query: {
            // 传递账期月份作为查询参数
            billTime: billMonth,
            // 可以传递其他需要的参数
            serviceType,
            serviceName: row.serviceName,
        },
    })
}
const handleExportRecord = async (row: any) => {
    loading.value = true
    // 获取billTime月份的第一天和最后一天
    const [year, month] = (row.billTime as string).split('-')
    // 计算月份的最后一天
    const lastDay = new Date(parseInt(year), parseInt(month), 0).getDate()
    try {
        const { data, code } = await doPostExportBillingRecords({
            startDate: `${year}-${month}-01`,
            endDate: `${year}-${month}-${lastDay}`,
            serviceName: row.serviceName,
            current: pageConfig.current,
            size: 100,
        })
        if (code === 200 && data) {
            ElMessage.success('导出数据发起成功！请去 下载中心 查找对应文件～')
        }
    } catch (error) {
        console.error('Failed to fetch billing records:', error)
    } finally {
        loading.value = false
    }
}
const handleConfirm = (row: TableRow) => {
    ElMessageBox.confirm('请核对当前账单无误！', '确认无误', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'success',
    })
        .then(() => {
            doPostConfirmCostBilling(row.id, 'YES').then((res) => {
                if (res.code === 200) {
                    ElMessage({
                        type: 'success',
                        message: '当前账单已确认无误',
                    })
                    initialData()
                } else {
                    throw Error(res.msg)
                }
            })
        })
        .catch(() => {
            ElMessage({
                type: 'info',
                message: '确认无误失败',
            })
        })
}
const handleException = (row: TableRow) => {
    ElMessageBox.confirm('当前账单存在异常？', '确认异常', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            doPostConfirmCostBilling(row.id, 'ERROR').then((res) => {
                if (res.code === 200) {
                    ElMessage({
                        type: 'success',
                        message: '当前账单已确认异常',
                    })
                    initialData()
                } else {
                    throw Error(res.msg)
                }
            })
        })
        .catch(() => {
            ElMessage({
                type: 'info',
                message: '确认异常失败',
            })
        })
}
const handleSizeChange = (val: number) => {
    pageConfig.current = 1
    pageConfig.size = val
    initialData()
}
const handleCurrentChange = (val: number) => {
    pageConfig.current = val
    initialData()
}
const changeShow = (show: boolean) => {
    tableHeight.value = `calc(100vh - ${show ? 408 : 256}px)`
}

const initialData = async () => {
    loading.value = true
    try {
        const { data } = await doGetCostBillingList({
            ...searchOptions,
            ...pageConfig,
        })
        if (data) {
            pageConfig.total = Number((data as any).total) || 0
            costBillingData.value = (data as any).records || []
        }
    } catch (error) {
        console.error('Failed to fetch billing records:', error)
    } finally {
        loading.value = false
    }
}
// 初始化数据
initDictData()
initialData()

/**
 * 导出账单
 */
const handleExport = async () => {
    loading.value = true
    try {
        const { data, code } = await doPostCostBillingList({
            ...searchOptions,
            ...pageConfig,
        })
        if (code === 200 && data) {
            ElMessage.success('导出数据发起成功！请去 下载中心 查找对应文件～')
        }
    } catch (error) {
        console.error('Failed to fetch billing records:', error)
    } finally {
        loading.value = false
    }
}
</script>

<style scoped>
.icon-text {
    display: flex;
    align-items: center;
}
.handle_container {
    margin-top: 16px;
}
.el-link + .el-link {
    margin-left: 8px;
}
</style>
