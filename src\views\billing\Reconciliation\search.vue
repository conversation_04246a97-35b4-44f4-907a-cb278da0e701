<template>
    <div class="form">
        <el-form class="form-flex">
            <MCard v-model="showCard">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="服务商类型" label-width="90px">
                            <el-select v-model="form.serviceType" placeholder="请选择服务商类型">
                                <el-option :value="''" label="全部" />
                                <el-option v-for="item in serviceTypeList" :key="item.value" :value="item.value" :label="item.label" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="服务名称" label-width="90px">
                            <el-input v-model="form.serviceName" placeholder="请输入服务名称" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="账期" label-width="90px">
                            <el-date-picker v-model="form.billTime" type="month" format="YYYY-MM" value-format="YYYY-MM" placeholder="请选择账期" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="状态" label-width="90px">
                            <el-select v-model="form.status" placeholder="请选择状态">
                                <el-option :value="''" label="全部" />
                                <el-option label="待确认" value="PRE" />
                                <el-option label="确认无误" value="YES" />
                                <el-option label="确认异常" value="ERROR" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="确认日期" label-width="90px">
                            <el-date-picker
                                v-model="form.confirmDate"
                                type="date"
                                format="YYYY/MM/DD"
                                value-format="YYYY-MM-DD"
                                placeholder="确认日期"
                                :disabled-date="disabledDate"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-button class="form__btn" type="primary" round @click="onSearch">搜索</el-button>
                        <el-button class="form__btn" round @click="onReset">重置</el-button>
                    </el-col>
                </el-row>
            </MCard>
        </el-form>
    </div>
</template>
<script setup lang="ts">
import { ref, reactive } from 'vue'
import { cloneDeep } from 'lodash'
const showCard = ref(false)
const getLastMonth = () => {
    const now = new Date()
    now.setMonth(now.getMonth() - 1)
    const y = now.getFullYear()
    const m = now.getMonth() + 1
    return `${y}-${m < 10 ? '0' + m : m}`
}
const form = reactive({
    serviceType: '',
    serviceName: '',
    billTime: getLastMonth(),
    status: '',
    confirmDate: getLastMonth() + '-01',
})
type OptionType = { label: string; value: string }

// 接收从父组件传递的字典数据
const props = defineProps({
    serviceTypeList: {
        type: Array<OptionType>,
        default: () => [],
    },
})

const $emit = defineEmits(['search', 'changeShow'])

const disabledDate = (date: Date) => {
    const now = new Date()
    const threeMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate())
    return date < threeMonthsAgo || date > now
}

const onSearch = () => {
    const params: Record<string, any> = cloneDeep(form)
    $emit('search', params)
}
const onReset = () => {
    form.serviceType = ''
    form.serviceName = ''
    form.billTime = getLastMonth()
    form.status = ''
    form.confirmDate = ''
    onSearch()
}
watch(
    () => showCard.value,
    (val) => {
        $emit('changeShow', val)
    },
)
</script>
<style scoped>
.form-flex {
    display: flex;
    flex-direction: column;
}
.form__btn {
    margin-right: 12px;
}
</style>
