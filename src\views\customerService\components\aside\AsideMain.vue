<template>
    <div class="scrollable-container">
        <div :collapse="collapse" class="customer-service-users">
            <!-- 分类展示用户列表 -->
            <div v-for="(users, type) in groupedUsers" :key="type" class="user-category">
                <div class="category-header" :title="getCategoryFullTitle(type)" @click="toggleCategory(type)">
                    <el-icon class="category-icon" :class="{ 'is-expanded': expandedCategories[type] }">
                        <CaretRight />
                    </el-icon>
                    <span class="category-title">{{ getCategoryTitle(type) }}</span>
                    <span class="category-count">({{ users.length }})</span>
                </div>

                <div v-show="expandedCategories[type]" class="category-content">
                    <div
                        v-for="messageUser in users"
                        :key="messageUser.chatWithUserInfo?.userId"
                        :index="messageUser.chatWithUserInfo?.userId"
                        class="user_msg_menu_item"
                        :class="{
                            'is-active': messageUser.chatWithUserInfo?.userId === currentSelectUser?.chatWithUserInfo?.userId,
                        }"
                        @click="onChange(messageUser)"
                    >
                        <div v-if="messageUser.chatWithUserInfo.includeRights" class="special_tag">
                            <QIcon svg name="icon-a-zuhe12851" style="width: 100%; height: 100%"></QIcon>
                        </div>
                        <el-badge :hidden="messageUser.lastMessage?.read" is-dot class="badge">
                            <el-avatar class="avatar" :size="50" shape="square" :src="getUserAvatar(messageUser.chatWithUserInfo.avatar)" />
                        </el-badge>
                        <div class="user-desc">
                            <div class="user-msg">
                                <div
                                    class="nickname"
                                    :title="getUserNickname(messageUser.chatWithUserInfo?.userId, messageUser.chatWithUserInfo.nickname)"
                                >
                                    {{ getUserNickname(messageUser.chatWithUserInfo?.userId, messageUser.chatWithUserInfo.nickname) }}
                                </div>
                                <div class="last-time">
                                    {{ renderTime(messageUser.lastMessage?.sendTime) }}
                                </div>
                            </div>
                            <div class="last-message" :title="renderMessage(messageUser.lastMessage)">
                                {{ renderMessage(messageUser.lastMessage) }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { ref, watch, nextTick, computed, reactive } from 'vue'
import type { PropType } from 'vue'
import { CaretRight } from '@element-plus/icons-vue'
import DateUtil from '@/utils/date'
import { MessageType, MessageUser, ILastMessage } from '../../types'
import { getUserNickname, getUserAvatar } from '@/libs/userHelper'
import { currentSelectUser } from '@/views/customerService'
import { useRoute } from 'vue-router'

const props = defineProps({
    messageUsers: {
        type: Array as PropType<Array<MessageUser>>,
        default: () => [],
    },
})

const collapse = ref(false)
const route = useRoute()
const emits = defineEmits(['change'])

const onChange = (selectUser: MessageUser) => {
    if (selectUser.lastMessage) {
        selectUser.lastMessage.read = true
    }
    emits('change', { ...selectUser })
}

watch(
    () => props.messageUsers,
    async (val: Array<MessageUser>) => {
        if (!route.query.id) {
            return
        }
        if (val[0]?.chatWithUserInfo) {
            const selectUser = val.find((item) => item.chatWithUserInfo?.userId === route.query.id)
            if (selectUser) {
                await nextTick()
                onChange(selectUser)
            }
        }
    },
)

const renderTime = (time: any) => {
    if (!time) return ''
    const lastTime = new Date(+time)
    const dateUtil = new DateUtil(lastTime)
    const isToday = new Date().getDay() === lastTime.getDay()
    return isToday ? dateUtil.getH() + ':' + dateUtil.getMin() : dateUtil.getYMD()
}

const renderMessage = (message: ILastMessage) => {
    if (!message) return ''
    switch (message.messageType) {
        case MessageType.PRODUCT:
            return '[商品]'
        case MessageType.IMAGE:
            return '[图片]'
        case MessageType.ORDER:
            return '[订单]'
        case MessageType.ORDER_WARNING:
            return '[订单预警]'
        default:
            return message.message
    }
}

// 分类展开状态
const expandedCategories = reactive<Record<string, boolean>>({
    CONSUMER: true,
    SHOP_ADMIN: true,
    PLATFORM_ADMIN: true,
    SUPPLIER: true,
    OTHER: true,
})

// 根据receiverType分组用户
const groupedUsers = computed(() => {
    const groups: Record<string, MessageUser[]> = {
        CONSUMER: [],
        SHOP_ADMIN: [],
        PLATFORM_ADMIN: [],
        SUPPLIER: [],
        OTHER: [],
    }

    props.messageUsers.forEach((user: MessageUser) => {
        const receiverType = user.lastMessage?.receiver?.receiverType || 'OTHER'

        // 根据receiverType分类
        if (receiverType === 'CONSUMER') {
            groups.CONSUMER.push(user)
        } else if (receiverType === 'SHOP_ADMIN') {
            groups.SHOP_ADMIN.push(user)
        } else if (receiverType === 'PLATFORM_ADMIN') {
            groups.PLATFORM_ADMIN.push(user)
        } else if (receiverType === 'SUPPLIER' || receiverType === 'SUPPLIER_CONSOLE') {
            groups.SUPPLIER.push(user)
        } else {
            groups.OTHER.push(user)
        }
    })

    // 过滤掉空的分组
    const filteredGroups: Record<string, MessageUser[]> = {}
    Object.keys(groups).forEach((key) => {
        if (groups[key].length > 0) {
            filteredGroups[key] = groups[key]
        }
    })

    return filteredGroups
})

// 获取分类标题（简写版本）
const getCategoryTitle = (type: string) => {
    const titles: Record<string, string> = {
        CONSUMER: '普通用户',
        SHOP_ADMIN: '店铺管理',
        PLATFORM_ADMIN: '平台管理',
        SUPPLIER: '供应商',
        OTHER: '其他',
    }
    return titles[type] || '未知'
}

// 获取分类完整标题（悬停时显示）
const getCategoryFullTitle = (type: string) => {
    const titles: Record<string, string> = {
        CONSUMER: '普通用户',
        SHOP_ADMIN: '店铺管理员',
        PLATFORM_ADMIN: '平台管理员',
        SUPPLIER: '供应商',
        OTHER: '其他类型',
    }
    return titles[type] || '未知类型'
}

// 切换分类展开状态
const toggleCategory = (type: string) => {
    expandedCategories[type] = !expandedCategories[type]
}
</script>
<style scoped lang="scss">
.customer-service-users {
    padding: 16px 12px;
    border-right: none;

    .user-category {
        margin-bottom: 12px;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

        .category-header {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            cursor: pointer;
            user-select: none;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                transition: left 0.5s;
            }

            &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);

                &::before {
                    left: 100%;
                }
            }

            .category-icon {
                margin-right: 8px;
                transition: all 0.3s ease;
                color: white;
                font-size: 14px;

                &.is-expanded {
                    transform: rotate(90deg);
                    color: #ffd700;
                }
            }

            .category-title {
                font-weight: 600;
                color: white;
                font-size: 14px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
                flex: 1;
            }

            .category-count {
                background: rgba(255, 255, 255, 0.2);
                color: white;
                font-size: 11px;
                padding: 2px 8px;
                border-radius: 12px;
                font-weight: 500;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.1);
            }
        }

        .category-content {
            background: #fafbfc;
            border-left: 3px solid #667eea;
            position: relative;

            &::before {
                content: '';
                position: absolute;
                left: 0;
                top: 0;
                bottom: 0;
                width: 3px;
                background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
                opacity: 0.6;
            }

            .user_msg_menu_item {
                margin-left: 0;
                border-left: none;
                border-bottom: 1px solid #f0f2f5;
                background: white;
                transition: all 0.2s ease;
                position: relative;

                &:hover {
                    background: #f8f9ff;
                    transform: translateX(4px);
                    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
                }

                &:last-child {
                    border-bottom: none;
                }

                &::before {
                    content: '';
                    position: absolute;
                    left: 0;
                    top: 0;
                    bottom: 0;
                    width: 0;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    transition: width 0.3s ease;
                }

                &.is-active::before {
                    width: 4px;
                }
            }
        }

        // 不同分类的主题色
        &:nth-child(1) .category-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        &:nth-child(2) .category-header {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        &:nth-child(3) .category-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        &:nth-child(4) .category-header {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        &:nth-child(5) .category-header {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
    }
    .user_msg_menu_item {
        cursor: pointer;
        font-size: 14px;
        color: #333333;
        min-height: 88px;
        user-select: none;
        margin: 0 !important;
        padding: 20px 18px !important;
        overflow: hidden;
        display: flex;
        align-items: center;
        position: relative;
        background-color: white;
        .special_tag {
            position: absolute;
            width: 26px;
            height: 26px;
            right: 0;
            top: 0;
        }
        .badge {
            height: 50px;
            margin-right: 16px;

            :deep(.el-badge__content) {
                background: linear-gradient(135deg, #ff6b6b, #ee5a24);
                border: 2px solid white;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
        }
        .avatar {
            border-radius: 8px;
            border: 2px solid #f0f2f5;
            transition: all 0.3s ease;

            &:hover {
                border-color: #667eea;
                transform: scale(1.05);
            }
        }
        .user-desc {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            height: 100%;
            .user-msg {
                display: flex;
                align-items: center;
                margin-bottom: 6px;

                .nickname {
                    font-weight: 600;
                    flex: 1;
                    color: #2c3e50;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    font-size: 14px;
                    min-width: 0;
                }
                .last-time {
                    margin-left: auto;
                    color: #95a5a6;
                    font-size: 11px;
                    font-weight: 500;
                    background: #f8f9fa;
                    padding: 2px 6px;
                    border-radius: 8px;
                }
            }
            .last-message {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                color: #7f8c8d;
                font-size: 13px;
                line-height: 1.4;
                padding-right: 8px;
            }
        }
    }
    .user_msg_menu_item.is-active {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        border-left: 4px solid #667eea;
        transform: translateX(4px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);

        .nickname {
            color: #667eea;
            font-weight: 700;
        }

        .avatar {
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
    }
}

// 自定义滚动容器，隐藏滚动条
.scrollable-container {
    height: calc(100% - 60px);
    overflow-y: auto;
    overflow-x: hidden;

    // 隐藏滚动条但保持滚动功能
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */

    &::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
        width: 0;
        height: 0;
    }

    &::-webkit-scrollbar-track {
        display: none;
    }

    &::-webkit-scrollbar-thumb {
        display: none;
    }
}
</style>
