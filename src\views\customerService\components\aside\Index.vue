<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-06-09 16:26:40
 * @LastEditors: lexy
 * @LastEditTime: 2023-06-10 10:14:32
-->
<template>
    <aside-header @keyword-change="(val) => emits('keywordChange', val)" @search-focus="(val) => emits('searchFocus', val)" />
    <aside-main :message-users="messageUsers" @change="(val) => emits('change', val)" />
</template>
<script setup lang="ts">
import AsideHeader from './AsideHeader.vue'
import AsideMain from './AsideMain.vue'
import { MessageUser } from '@/views/customerService/types'
import { PropType } from 'vue'

defineProps({
    messageUsers: {
        type: Array as PropType<Array<MessageUser>>,
        default: () => [],
    },
})
const emits = defineEmits(['change', 'keywordChange', 'searchFocus'])
</script>
<style scoped></style>
