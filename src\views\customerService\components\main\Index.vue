<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-11-01 16:17:13
 * @LastEditors: lexy
 * @LastEditTime: 2023-06-09 17:20:02
-->
<template>
    <el-container>
        <el-header>
            <main-header :user="props.user" />
        </el-header>
        <el-main>
            <main-body :shop-info="props.shopInfo" :user="props.user" :messages="props.messages" @load-more="() => emits('loadMore')" />
        </el-main>
        <el-footer>
            <main-footer :user="props.user" :search-focus="props.searchFocus" @message-submit="messageSubmit" />
        </el-footer>
    </el-container>
</template>
<script setup lang="ts">
import { ref, PropType } from 'vue'
import MainHeader from './header/Index.vue'
import MainBody from './body/Index.vue'
import MainFooter from './footer/Index.vue'
import { MessageUser, ChatMessage, MessageAndShopAdmin } from '@/views/customerService/types'

const props = defineProps({
    user: {
        type: Object as PropType<MessageUser>,
        default: () => {},
    },
    messages: {
        type: Object as PropType<Array<MessageAndShopAdmin>>,
        default: () => {},
    },
    shopInfo: {
        type: Object,
        default: () => {},
    },
    searchFocus: {
        type: Boolean,
        default: false,
    },
})
const emits = defineEmits(['messageSubmit', 'loadMore'])
const messageSubmit = (chatMessage: ChatMessage) => {
    emits('messageSubmit', chatMessage)
}
</script>
<style scoped lang="scss">
.el-container {
    background-color: $rows-bg-color-grey;
    height: 100%;
    padding: 0;
}
.el-header {
    padding: 0;
    height: 59px;
}
.el-main {
    border-top: 1px solid var(--el-border-color);
    padding: 0;
}
.el-footer {
    padding: 0;
    border-top: 1px solid var(--el-border-color);
    min-height: 200px;
}
</style>
