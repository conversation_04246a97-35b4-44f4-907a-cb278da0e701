<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-06-09 16:26:40
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-10 11:11:14
-->
<template>
    <el-scrollbar ref="scrollbarRef" class="messages-admin-box" @scroll="scroll">
        <ul ref="contentRef" class="messages-admin-list">
            <body-contents :shop-info="props.shopInfo" :user="props.user" :messages="props.messages" />
        </ul>
    </el-scrollbar>
</template>
<script setup lang="ts">
import { MessageUser, MessageAndShopAdmin } from '@/views/customerService/types'
import BodyContents from './contents/Index.vue'
import { ref, PropType, computed, watch } from 'vue'

const props = defineProps({
    user: {
        type: Object as PropType<MessageUser>,
        default: () => {},
    },
    messages: {
        type: Array as PropType<Array<MessageAndShopAdmin>>,
        default: () => [],
    },
    shopInfo: {
        type: Object,
        default: () => {},
    },
})
const emits = defineEmits(['loadMore'])
const scrollbarRef = ref<any>(null)
const contentRef = ref<any>(null)
const userId = computed(() => props.user?.chatWithUserInfo.userId)
const lastTime = computed(() => {
    const messages = props.messages
    scroll2Bottom()
    return messages && messages.length > 0 ? messages[0].sendTime : ''
})

const scroll2Bottom = () => setTimeout(() => scrollbarRef.value?.setScrollTop(contentRef.value?.clientHeight), 200)
watch(() => userId.value, scroll2Bottom, { immediate: true })
watch(
    () => lastTime.value,
    () => {
        console.log(';;;;')
    },
)

const scroll = ({ scrollTop }: any) => {
    if (scrollTop !== 0) return
    emits('loadMore')
}
</script>
<style scoped lang="scss">
.messages-admin-box {
    padding: $rows-spacing-row-sm;
}
</style>
