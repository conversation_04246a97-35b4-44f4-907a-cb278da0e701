<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-06-09 16:26:40
 * @LastEditors: lexy
 * @LastEditTime: 2023-06-10 09:05:16
-->
<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-10-19 09:27:45
 * @LastEditors: lexy
 * @LastEditTime: 2023-05-17 14:43:45
-->
<script setup lang="ts">
import { computed, PropType } from 'vue'
import { MessageAndShopAdmin } from '@/views/customerService/types'

/**
 * msg 消息内容
 * isMine 是否是我的消息
 */
const $router = useRouter()
const props = defineProps({
    message: {
        type: Object as PropType<MessageAndShopAdmin>,
        required: true,
    },
    isMine: {
        type: Boolean,
        default: false,
    },
})
const product = computed(() => {
    const productMsg = props.message.message
    if (!productMsg) {
        return { id: '', name: '未正确获取商品信息', salePrices: [], pic: '' }
    }
    return JSON.parse(productMsg)
})
const handleNavToOrderList = (orderNo: string) => {
    if (orderNo) {
        $router.push({
            name: 'orderIndex',
            query: {
                orderNo,
            },
        })
    }
}
</script>
<template>
    <el-link type="info" :underline="false" @click="handleNavToOrderList(product.no)">
        <div class="message-content-product product-box">
            <div style="height: 130px"><el-image :src="product.pic" style="height: 100%" fit="scale-down" /></div>
            <div class="product-box__right">
                <span>订单号：{{ product.no }}</span>
                <div class="product-box__right--footer">
                    <span> {{ product.name ? product.name : '未正确获取商品信息' }} </span>
                    <div>实付￥{{ product.amountRealPay }}</div>
                </div>
            </div>
        </div>
        <!-- <div class="message-content-product">
            <div class="product-box">
                <div style="font-weight: 700; color: #000; margin-bottom: 5px">订单号:{{ product.no }}</div>
                <div style="display: flex; flex-direction: row; height: 130px">
                    <el-image :src="product.pic" fit="scale-down" />
                    <div class="product-info">
                        <div class="product-name">
                            {{ product.name ? product.name : '未正确获取商品信息' }}
                        </div>
                        <div class="product-prices"><text style="font-size: 16px">实付</text>￥{{ product.amountRealPay }}</div>
                    </div>
                </div>
            </div>
        </div> -->
    </el-link>
</template>
<style scoped lang="scss">
.message-content-product {
    margin: $rows-spacing-row-sm;
    height: 200px;
}
.product-box {
    background: $rows-text-color-inverse;
    border-radius: $rows-border-radius-sm;
    padding: $rows-spacing-row-sm;
    border: 1px solid var(--el-border-color);
    height: 100%;
    display: flex;
}
.product-box__right {
    margin-left: 15px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
.product-box__right--footer {
    display: flex;
    justify-content: space-between;
}
.product-info {
    color: $rows-text-color-grey;
    flex: 1;
    display: flex;
    padding-left: $rows-spacing-col-lg;
    flex-direction: column;
    justify-content: space-between;
    min-width: 0;
}
.product-info .product-name {
    height: 40px;
    font-size: 15px;
    width: 100%;
    @include utils-ellipsis(2);
}
.product-info .product-prices {
    height: 60px;
    font-size: 20px;
    color: $rows-color-error;
    @include utils-ellipsis(2);
}
</style>
