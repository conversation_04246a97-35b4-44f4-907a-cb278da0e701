<!--
 * @description: 订单预警消息组件
 * @Author: AI Assistant
 * @Date: 2025-01-30
-->
<template>
    <div class="message-content order-warning-content" :class="clazz">
        <div class="message-content-direction" :class="clazz"></div>
        <div class="order-warning-message">
            <div class="warning-header">
                <el-icon class="warning-icon"><Warning /></el-icon>
                <span class="warning-title">订单预警</span>
            </div>
            <div class="warning-details">
                <div v-for="(item, index) in warningItems" :key="index" class="warning-item">
                    <div class="product-info">
                        <span class="product-name">{{ item.productName }}</span>
                        <span class="product-num">数量: {{ item.productNum }}</span>
                    </div>
                    <div class="warning-type">
                        <el-tag :type="getWarningTypeColor(item.noticeType)" size="small">
                            {{ getWarningTypeText(item.noticeType) }}
                        </el-tag>
                    </div>
                    <div class="warning-count">
                        <span class="count-text">共 {{ item.total }} 条预警</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, PropType } from 'vue'
import { Warning } from '@element-plus/icons-vue'
import { MessageAndShopAdmin } from '@/views/customerService/types'

/**
 * msg 消息内容
 * isMine 是否是我的消息
 */
const props = defineProps({
    message: {
        type: Object as PropType<MessageAndShopAdmin>,
        required: true,
    },
    isMine: {
        type: Boolean,
        default: false,
    },
})

const clazz = computed(() => (props.isMine ? 'mine' : 'other'))

// 解析订单预警消息内容
const warningItems = computed(() => {
    try {
        if (props.message.message) {
            return JSON.parse(props.message.message)
        }
        return []
    } catch (error) {
        console.error('解析订单预警消息失败:', error)
        return []
    }
})

// 获取预警类型文本
const getWarningTypeText = (noticeType: string) => {
    const typeMap: Record<string, string> = {
        PENDING_SHIPMENT: '发货超时',
        PENDING_PICKUP: '揽收异常',
    }
    return typeMap[noticeType] || noticeType
}

// 获取预警类型颜色
const getWarningTypeColor = (noticeType: string) => {
    const colorMap: Record<string, string> = {
        PENDING_SHIPMENT: 'warning',
        PENDING_PICKUP: 'danger',
    }
    return colorMap[noticeType] || 'info'
}
</script>

<style scoped lang="scss">
.order-warning-content {
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    border-radius: 16px;
    padding: 20px;
    max-width: 420px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(226, 232, 240, 0.6);
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
    }

    &.mine {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        margin-left: auto;
        color: white;

        .message-content-direction {
            right: -10px;
            border-left-color: #667eea;
        }

        .warning-header .warning-title {
            color: white;
        }

        .product-name {
            color: rgba(255, 255, 255, 0.95) !important;
        }

        .product-num,
        .count-text {
            color: rgba(255, 255, 255, 0.8) !important;
        }
    }

    &.other {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        margin-right: auto;

        .message-content-direction {
            left: -10px;
            border-right-color: #ffffff;
        }
    }
}

.message-content-direction {
    position: absolute;
    top: 16px;
    width: 0;
    height: 0;
    border: 10px solid transparent;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));

    &.mine {
        right: -10px;
        border-left-color: #667eea;
        border-right: none;
    }

    &.other {
        left: -10px;
        border-right-color: #ffffff;
        border-left: none;
    }
}

.order-warning-message {
    .warning-header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 1px solid rgba(226, 232, 240, 0.6);
        position: relative;

        &::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 40px;
            height: 2px;
            background: linear-gradient(90deg, #ff9800, #ffb74d);
            border-radius: 1px;
        }

        .warning-icon {
            color: #ff9800;
            margin-right: 8px;
            font-size: 18px;
            background: rgba(255, 152, 0, 0.1);
            padding: 6px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .warning-title {
            font-weight: 700;
            color: #333;
            font-size: 15px;
            letter-spacing: 0.5px;
        }
    }

    .warning-details {
        .warning-item {
            margin-bottom: 16px;
            padding: 16px;
            background: rgba(248, 250, 252, 0.8);
            border-radius: 12px;
            border: 1px solid rgba(226, 232, 240, 0.5);
            transition: all 0.2s ease;

            &:hover {
                background: rgba(248, 250, 252, 1);
                transform: translateX(2px);
            }

            &:last-child {
                margin-bottom: 0;
            }

            .product-info {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;

                .product-name {
                    font-weight: 600;
                    color: #2d3748;
                    font-size: 14px;
                    flex: 1;
                    margin-right: 12px;
                    line-height: 1.4;
                }

                .product-num {
                    color: #718096;
                    font-size: 12px;
                    background: rgba(113, 128, 150, 0.1);
                    padding: 4px 8px;
                    border-radius: 6px;
                    font-weight: 500;
                }
            }

            .warning-type {
                margin-bottom: 8px;

                :deep(.el-tag) {
                    border-radius: 8px;
                    font-weight: 600;
                    font-size: 11px;
                    padding: 4px 10px;
                    border: none;
                }
            }

            .warning-count {
                .count-text {
                    color: #a0aec0;
                    font-size: 12px;
                    font-weight: 500;
                    background: rgba(160, 174, 192, 0.1);
                    padding: 2px 8px;
                    border-radius: 6px;
                    display: inline-block;
                }
            }
        }
    }
}
</style>
