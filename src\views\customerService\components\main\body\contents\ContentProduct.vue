<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-10-19 09:27:45
 * @LastEditors: lexy
 * @LastEditTime: 2023-06-10 09:06:02
-->
<script setup lang="ts">
import { computed, PropType } from 'vue'
import { MessageAndShopAdmin } from '@/views/customerService/types'
import useConvert from '@/composables/useConvert'

/**
 * msg 消息内容
 * isMine 是否是我的消息
 */
const $router = useRouter()
const props = defineProps({
    message: {
        type: Object as PropType<MessageAndShopAdmin>,
        required: true,
    },
    isMine: {
        type: Boolean,
        default: false,
    },
})
const { divTenThousand } = useConvert()
const product = computed(() => {
    const productMsg = props.message.message
    if (!productMsg) {
        return { id: '', name: '未正确获取商品信息', salePrices: [], pic: '' }
    }
    return JSON.parse(productMsg)
})
const handleNavToGoodsEdit = (id: string) => {
    if (id) {
        $router.push({
            name: 'releaseCommodityEdit',
            query: {
                id,
            },
        })
    }
}
</script>
<template>
    <el-link type="info" :underline="false" @click="handleNavToGoodsEdit(product.id)">
        <div class="message-content-product">
            <div class="product-box" style="display: flex; flex-direction: row">
                <el-image :src="product.pic" fit="scale-down" />
                <div class="product-info">
                    <div class="product-name">
                        {{ product.name ? product.name : '未正确获取商品信息' }}
                    </div>
                    <div class="product-prices">
                        {{
                            product.salePrices && product.salePrices.length > 0
                                ? '¥' + divTenThousand(Array.isArray(product.salePrices) ? product.salePrices[0] : product.salePrices) + '起'
                                : '未正确获取商品价格'
                        }}
                    </div>
                </div>
            </div>
        </div>
    </el-link>
</template>
<style scoped lang="scss">
.message-content-product {
    padding: $rows-spacing-row-sm;
    width: 350px;
    height: 200px;
}
.product-box {
    background: $rows-text-color-inverse;
    border-radius: $rows-border-radius-sm;
    padding: $rows-spacing-row-sm;
    border: 1px solid var(--el-border-color);
    height: 100%;
}
.product-info {
    color: $rows-text-color-grey;
    flex: 1;
    display: flex;
    padding-left: $rows-spacing-col-lg;
    flex-direction: column;
    justify-content: space-between;
    min-width: 0;
}
.product-info .product-name {
    height: 40px;
    font-size: 15px;
    width: 100%;
    @include utils-ellipsis(2);
}
.product-info .product-prices {
    height: 60px;
    font-size: 25px;
    color: $rows-color-error;
    @include utils-ellipsis(2);
}
</style>
