<template>
    <el-container>
        <el-header height="40px">
            <chat-toolbar
                v-if="props.user?.chatWithUserInfo.userId && Number(props.user.chatWithUserInfo.userId) > 0"
                @content-change="contentChange"
            />
        </el-header>
        <el-main style="position: relative">
            <el-input
                ref="elInputRef"
                v-model.trim="content"
                type="textarea"
                :disabled="!props.user || !props.user.chatWithUserInfo.userId || Number(props.user.chatWithUserInfo.userId) < 0"
                resize="none"
                maxlength="300"
                @blur="onBlur"
                @keydown.enter="onSubmit"
            >
            </el-input>
            <div class="send-btn">
                <el-button type="primary" @click="sendMessage">发送</el-button>
            </div>
        </el-main>
    </el-container>
</template>

<script setup lang="ts">
import { onMounted, PropType, ref, watch } from 'vue'
import ChatToolbar from './toolbar/Index.vue'
import { MessageType, MessageUser, ToolbarMessage, ToolbarMessageType, ChatMessage } from '../../../types'

const props = defineProps({
    user: {
        type: Object as PropType<MessageUser>,
        default: () => {},
    },
    searchFocus: {
        type: Boolean,
        default: false,
    },
})
const emits = defineEmits(['messageSubmit'])
const content = ref('')
const position = ref(0)
const elInputRef = ref<any>(null)
const textarea = ref(null)

onMounted(() => {
    content.value = ''
    textarea.value = elInputRef.value?.textarea
    position.value = content.value.length
})

watch(
    () => props.user,
    (user) => {
        if (!user || !user.chatWithUserInfo.userId) return
        elInputRef.value?.focus()
        clearMessage()
    },
)
//清空消息
const clearMessage = () => {
    content.value = ''
    position.value = 0
}
//
const submitMessage = (chatMessage: ChatMessage) => {
    emits('messageSubmit', chatMessage)
}
const onSubmit = (e: any) => {
    //非 enter 或者 按住了shift
    if (e.shiftKey) return
    e.preventDefault()
    sendMessage()
}
function sendMessage() {
    const value = content.value
    if (!value) return
    submitMessage({ messageType: MessageType.TEXT, message: content.value })
    clearMessage()
}
//
//获取失去焦点前的焦点
const onBlur = (e: any) => {
    //console.log('textarea', props.searchFocus)
    /*console.log(props.searchFocus)
    if (props.searchFocus) return*/
    // position.value = e.target.selectionStart
    // e.target.focus()
}
//内容改变
const contentChange = (toolContent: ToolbarMessage) => {
    switch (toolContent.type) {
        case ToolbarMessageType.EXPRESSION:
            changeContentValue(toolContent.content)
            return
        case ToolbarMessageType.IMAGE:
            submitMessage({ messageType: MessageType.IMAGE, message: toolContent.content })
            return
        default:
            break
    }
}

//插入新值
const changeContentValue = (newContent: any) => {
    if (!newContent) return
    const currentValue = content.value
    const cursorIndex = position.value
    content.value = currentValue.slice(0, cursorIndex) + newContent + currentValue.slice(cursorIndex)
    position.value = position.value + newContent.length
}
</script>

<style scoped lang="scss">
.el-container {
    height: 100%;
}

.el-header {
    padding: 0;
}

.el-main {
    padding: 0;
}

.el-main .el-textarea {
    height: 100%;
    --el-input-hover-border: $rows-bg-color-grey;
    --el-input-focus-border: $rows-bg-color-grey;
    --el-input-border-color: $rows-bg-color-grey;
    --el-input-bg-color: $rows-bg-color-grey;
    --el-input-icon-color: $rows-bg-color-grey;
    --el-input-placeholder-color: $rows-bg-color-grey;
    --el-input-hover-border-color: $rows-bg-color-grey;
    --el-input-clear-hover-color: $rows-bg-color-grey;
    --el-input-focus-border-color: $rows-bg-color-grey;
}
.send-btn {
    position: absolute;
    right: 50px;
    bottom: 30px;
}
</style>

<style>
.el-main .el-textarea .el-textarea__inner {
    width: 100%;
    height: 100%;
}
</style>
