<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-06-09 16:26:40
 * @LastEditors: lexy
 * @LastEditTime: 2023-06-10 09:03:19
-->
<template>
    <div class="customer-service-tool">
        <el-row align="middle">
            <el-col :span="1">
                <expression @expression-select="expressionSelect" />
            </el-col>
            <el-col :span="1">
                <image-uploader @image-select="imageSelect" />
            </el-col>
        </el-row>
    </div>
</template>

<script setup lang="ts">
import { ToolbarMessageType, ToolbarMessage } from '@/views/customerService/types'
import Expression from './expression/Index.vue'
import ImageUploader from './image/Index.vue'
const emits = defineEmits(['contentChange'])

const emitMessage = (message: ToolbarMessage) => {
    emits('contentChange', message)
}
const expressionSelect = (expression: string) => {
    emitMessage({ type: ToolbarMessageType.EXPRESSION, content: expression })
}
const imageSelect = (imageUrl: any) => {
    emitMessage({ type: ToolbarMessageType.IMAGE, content: imageUrl })
}
</script>

<style scoped lang="scss">
.customer-service-tool {
    background-color: $rows-bg-color-grey;
    height: 100%;
    color: $rows-text-color;
}
.customer-service-tool .el-row {
    height: 100%;
}
</style>
<style lang="scss">
.customer-service-tool .el-button {
    font-size: $rows-font-size-sm;
}
</style>
