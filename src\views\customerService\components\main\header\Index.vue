<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-06-09 16:26:40
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-09 13:23:48
-->
<template>
    <div>{{ props.user ? props.user.chatWithUserInfo.nickname : '' }}</div>
</template>
<script setup lang="ts">
import { PropType } from 'vue'
import { MessageUser } from '@/views/customerService/types'
const props = defineProps({
    user: {
        type: Object as PropType<MessageUser>,
        default: () => {},
    },
})
/*watch(
    () => props.user,
    () => {
        console.log('props.user', props.user)
    },
)*/
</script>
<style scoped lang="scss">
div {
    display: flex;
    justify-content: center;
    width: 100%;
    height: 100%;
    line-height: 100%;
    align-items: center;
    font-size: 18px;
}
.el-header {
    display: flex;
    align-items: center;
    border-top-right-radius: 8px;
}
</style>
