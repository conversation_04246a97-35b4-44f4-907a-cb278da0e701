<!--
 * @description:
 * @Author: lexy
 * @Date: 2023-04-14 09:51:08
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-07 11:35:08
-->
<template>
    <el-container class="customer-service">
        <!-- 调试信息 -->
        <div v-if="showDebugInfo" class="debug-info">
            <h4>调试信息:</h4>
            <p>shopInfo: {{ shopInfo ? '已加载' : '未加载' }}</p>
            <p>shopId: {{ shopInfo?.additionalInformation?.shopId || '无' }}</p>
            <p>用户列表数量: {{ messageUsersPage.records.length }}</p>
            <p>当前选中用户: {{ currentSelectUser?.chatWithUserInfo?.nickname || '无' }}</p>
            <p>消息数量: {{ adminMessagesPage.records.length }}</p>
            <button @click="showDebugInfo = false">隐藏调试信息</button>
        </div>

        <el-aside width="200px">
            <aside-index
                :message-users="messageUsersPage.records"
                @change="onChange"
                @keyword-change="onKeywordsChange"
                @search-focus="onSearchFocus"
            />
        </el-aside>
        <el-main>
            <main-index
                :shop-info="shopInfo"
                :user="currentSelectUser"
                :messages="adminMessagesPage.records"
                :search-focus="searchFocus"
                @message-submit="messageSubmit"
                @load-more="contentLoadMore"
            />
        </el-main>
    </el-container>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'

import AsideIndex from './components/aside/Index.vue'
import MainIndex from './components/main/Index.vue'
import {
    shopInfo,
    messageUsersPage,
    currentSelectUser,
    searchFocus,
    onChange,
    onKeywordsChange,
    onSearchFocus,
    adminMessagesPage,
    messageSubmit,
    initCustomerService,
    contentLoadMore,
} from './index'

// 添加路由参数调试
const route = useRoute()

// 调试信息显示控制
const showDebugInfo = ref(true)

onMounted(() => {
    console.log('客服页面组件已挂载')
    initCustomerService()
})
</script>

<style scoped lang="scss">
.debug-info {
    position: fixed;
    top: 10px;
    right: 10px;
    background: white;
    border: 1px solid #ccc;
    padding: 10px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 9999;
    max-width: 300px;

    h4 {
        margin: 0 0 10px 0;
        color: #333;
    }

    p {
        margin: 5px 0;
        font-size: 12px;
        color: #666;
    }

    button {
        margin-top: 10px;
        padding: 4px 8px;
        background: #409eff;
        color: white;
        border: none;
        border-radius: 2px;
        cursor: pointer;
    }
}

.customer-service.el-container {
    border: 1px solid var(--el-border-color);
    border-radius: $rows-border-radius-lg;
    width: 100%;
    min-height: 700px;
    overflow: hidden;
}
.el-aside {
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
    border-right: 1px solid var(--el-border-color);
    min-height: 600px;
    background-color: white;
    overflow: hidden !important;

    // 隐藏所有滚动条
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */

    &::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
    }
}
.el-main {
    width: 600px;
    padding: 0;
}
</style>
