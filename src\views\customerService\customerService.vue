<!--
 * @description:
 * @Author: lexy
 * @Date: 2023-04-14 09:51:08
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-07 11:35:08
-->
<template>
    <div class="customer-service-wrapper">
        <el-container class="customer-service">
            <el-aside width="200px">
                <aside-index
                    :message-users="messageUsersPage.records"
                    @change="onChange"
                    @keyword-change="onKeywordsChange"
                    @search-focus="onSearchFocus"
                />
            </el-aside>
            <el-main>
                <main-index
                    :shop-info="shopInfo"
                    :user="currentSelectUser"
                    :messages="adminMessagesPage.records"
                    :search-focus="searchFocus"
                    @message-submit="messageSubmit"
                    @load-more="contentLoadMore"
                />
            </el-main>
        </el-container>
    </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'

import AsideIndex from './components/aside/Index.vue'
import MainIndex from './components/main/Index.vue'
import {
    shopInfo,
    messageUsersPage,
    currentSelectUser,
    searchFocus,
    onChange,
    onKeywordsChange,
    onSearchFocus,
    adminMessagesPage,
    messageSubmit,
    initCustomerService,
    contentLoadMore,
} from './index'

onMounted(() => {
    console.log('客服页面组件已挂载')
    initCustomerService()
})
</script>

<style scoped lang="scss">
.customer-service-wrapper {
    width: 100%;
    height: 100%;
}

.debug-simple {
    position: fixed;
    top: 10px;
    right: 10px;
    background: #f0f0f0;
    padding: 8px;
    border-radius: 4px;
    z-index: 9999;
    font-size: 12px;

    p {
        margin: 2px 0;
        color: #333;
    }
}

.customer-service.el-container {
    border: 1px solid var(--el-border-color);
    border-radius: $rows-border-radius-lg;
    width: 100%;
    height: 800px; /* 增加高度从700px到800px */
    overflow: hidden;
}
.el-aside {
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
    border-right: 1px solid var(--el-border-color);
    min-height: 600px;
    background-color: white;
    overflow: hidden !important;

    // 隐藏所有滚动条
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */

    &::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
    }
}
.el-main {
    width: 600px;
    padding: 0;
}
</style>
