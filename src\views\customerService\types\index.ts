import { BaseMessage } from '@/libs/stomp/typs'
interface Page {
    size: number
    current: number
}

export class IPage<T> {
    current = 1
    size = 30
    total = 0
    records: Array<T> = []
    load: (page: Page) => Promise<any>
    constructor(load: (page: Page) => Promise<any>, size = 30) {
        this.load = load
        this.size = size
    }
    loadMore() {
        if (this.total <= this.current * this.size) {
            return
        }
        this.current = this.current + 1
        this.load({ current: this.current, size: this.size }).then((response) => {
            console.log('IPage loadMore 响应:', response)
            if (response && response.data) {
                const { total, records } = response.data
                this.total = total || 0
                this.records = this.records.concat(records || [])
                console.log('IPage loadMore 成功，新增记录数:', records?.length || 0)
            }
        }).catch(error => {
            console.error('IPage loadMore 失败:', error)
        })
    }
    async initLoad() {
        this.current = 1
        try {
            const response = await this.load({ current: this.current, size: this.size })
            console.log('IPage initLoad 响应:', response)

            if (response && response.data) {
                const { total, records } = response.data
                this.total = total || 0
                this.records = records || []
                console.log('IPage initLoad 成功，记录数:', this.records.length)
            } else {
                console.error('IPage initLoad 响应格式错误:', response)
                this.total = 0
                this.records = []
            }
        } catch (error) {
            console.error('IPage initLoad 失败:', error)
            this.total = 0
            this.records = []
        }
        return this.records
    }
    concatData(res: T) {
        this.records.unshift(res)
    }
}

/**
 * 用户类型
 */
export enum UserType {
    //消费者
    CONSUMER = 'CONSUMER',
    //店铺管理员
    SHOP_ADMIN = 'SHOP_ADMIN',
    //平台管理员
    PLATFORM_ADMIN = 'PLATFORM_ADMIN',
}

/**
 * 消息类型
 */
export enum MessageType {
    //文本
    TEXT = 'TEXT',
    //图片
    IMAGE = 'IMAGE',
    //商品
    PRODUCT = 'PRODUCT',
    // 订单
    ORDER = 'ORDER',
    // 订单预警
    ORDER_WARNING = 'ORDER_WARNING',
}

export interface MessageUser {
    chatWithUserInfo: IChatWithUserInfo
    lastMessage: ILastMessage
}

export interface ILastMessage {
    handled: false
    message: string
    messageType: string
    read: boolean
    receiver: IReceiver
    sendTime: string
    sender: ISender
    show: boolean
}
export interface IReceiver {
    receiverShopInfo: { shopId: string; shopName: string }
    receiverType: string
}
export interface ISender {
    senderType: string
    senderUserInfo: IChatWithUserInfo
    senderShopInfo: {
        shopId: string
        shopName: string
    }
}
export interface IChatWithUserInfo {
    avatar: string
    nickname: string
    userId: string
    userKey: string
    includeRights: boolean
}
/**
 * 工具栏内容类型枚举
 */
export enum ToolbarMessageType {
    //表情
    EXPRESSION,
    //图片
    IMAGE,
}

/**
 * 工具栏内容
 */
export interface ToolbarMessage {
    type: ToolbarMessageType
    content: string
}

export interface ChatMessage {
    messageType: MessageType
    message: string
}

/**
 * 聊天消息
 */
export interface Message {
    id: string
    shopId: string
    adminId: string
    userId: string
    senderType: UserType
    receiverType: UserType
    messageType: MessageType
    message: string
    read: boolean
    createTime: string
}

/**
 * 店铺管理员与消息列表
 */

export interface MessageAndShopAdmin {
    handled: boolean
    message: string
    messageType: 'IMAGE' | 'PRODUCT' | 'ORDER' | 'TEXT' | 'ORDER_WARNING'
    read: boolean
    receiver: IReceiver
    sendTime: string
    sender: ISender
    show: boolean
}

/**
 * 客服消息
 */
export interface CustomerServiceMessage extends BaseMessage {
    /**
     * 消息id
     */
    messageId: string

    /**
     * 发送方类型
     */
    senderType: UserType

    /**
     * 消息发送方id
     */
    senderId: string

    /**
     * 接收方类型
     */
    receiverType: UserType

    /**
     * 接收方id
     */
    receiverId: string

    /**
     * 消息内容类型
     */
    messageType: MessageType

    /**
     * 消息内容
     */
    message: string
}
