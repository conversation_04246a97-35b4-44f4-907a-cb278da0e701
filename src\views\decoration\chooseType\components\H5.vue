<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-02-11 09:10:20
 * @LastEditors: lexy
 * @LastEditTime: 2024-02-05 10:46:26
-->
<script setup lang="ts">
import { useDecorationStore } from '@/store/modules/decoration'

/*
 *variable
 */
const $props = defineProps({
    templateType: {
        type: String,
        default() {
            return 'PLATFORM'
        },
    },
    isO2o: {
        type: Boolean,
        default() {
            return false
        },
    },
})
const $decorationStore = useDecorationStore()
const toPath = computed(() => {
    return $props.templateType === 'PLATFORM' ? '/decoration/set' : $props.isO2o ? '/decoration/shop/o2o/set' : '/decoration/shop/set'
})
/*
 *lifeCircle
 */
// initDecorationType()

/*
 *function
 */

const handleNavToSet = () => {
    $decorationStore.SET_ENDPOINT_TYPE('H5_APP')
}
</script>

<template>
    <router-link target="_blank" :to="{ path: toPath }" @click="handleNavToSet">
        <el-button class="choose-card"> H5、APP 端 </el-button>
    </router-link>
</template>

<style scoped></style>
