<script setup lang="ts">
import EditorFormData from '../packages/components/index/formModel'
import { useDecorationStore } from '@/store/modules/decoration/index'
import { cloneDeep } from 'lodash-es'
import type { ComponentItem } from '../packages/components/index/formModel'
import { ElMessage } from 'element-plus'
import { VueDraggableNext } from 'vue-draggable-next'

const $decorationStore = useDecorationStore()
const $props = defineProps({
    activePagesType: {
        type: String,
        default: '',
    },
})
const lazyShow = ref(false)
onMounted(async () => {
    await nextTick()
    lazyShow.value = true
})
const $emit = defineEmits(['change'])
const first = ref(true)
const second = ref(true)
const basicComponentList: ComponentItem[] = [
    {
        icon: 'paihangbang_paiming',
        value: 'compose',
        label: '组合组件',
    },
    {
        icon: 'lunbotu',
        value: 'swiper',
        label: '轮播图',
    },
    {
        icon: 'shangpin',
        value: 'goods',
        label: '商品',
    },
    {
        icon: 'sousuo',
        value: 'search',
        label: '搜索',
    },
    {
        icon: 'biaotilan',
        value: 'titleBar',
        label: '标题栏',
    },
    {
        icon: 'zhanweifu',
        value: 'blankPaceholder',
        label: '空白占位',
    },
    {
        icon: 'fengefu',
        value: 'separator',
        label: '分隔符',
    },
    {
        icon: 'dianpudaohang',
        value: 'navigation',
        label: '导航模块',
    },
    {
        icon: 'mofang',
        value: 'cubeBox',
        label: '魔方',
    },
    {
        icon: '28fuwenbenkuang',
        value: 'richText',
        label: '富文本',
    },
    {
        icon: 'tupian',
        value: 'resizeImage',
        label: '图片',
    },
    {
        icon: 'zhibo1',
        value: 'video',
        label: '视频',
    },
    {
        icon: 'shangchengshezhi',
        value: 'shopGoods',
        label: '店铺商品',
    },
    {
        icon: 'dizhi',
        value: 'positioningStyle',
        label: '定位',
    },
]
const componentList = computed(() => {
    return $props.activePagesType === 'CUSTOMIZED_PAGE' ? basicComponentList.filter((item) => item.value !== 'shopGoods') : basicComponentList
})
// 营销组件
const marketComponents = ref<ComponentItem[]>([
    {
        icon: 'icon-miaosha1',
        value: 'secKill',
        label: '秒杀',
    },
    {
        icon: 'icon-zhibo1',
        value: 'live',
        label: '微信直播',
    },
    {
        icon: 'cylx-onlyPromotion',
        value: 'onlyPromotion',
        label: '会员专享',
    },
])
const activePageType = computed(() => {
    return $decorationStore.activePageType
})
/**
 * 点击添加组件
 */
const handleAddComponent = (currentComponent: ComponentItem) => {
    if (activePageType.value !== 'customize') {
        ElMessage.warning('该页面无法添加组件')
        return
    } else {
        const FormData = cloneDeep(EditorFormData[currentComponent.value])
        $emit('change', { ...currentComponent, id: Date.now(), formData: FormData })
    }
}
</script>

<template>
    <div class="editor__component editor__component_new">
        <div class="editor_component_wrap">
            <el-scrollbar style="height: 85%">
                <div class="editor_component_wrap_main">
                    <el-tabs class="demo-tabs">
                        <el-tab-pane label="基本组件">
                            <VueDraggableNext
                                v-if="lazyShow"
                                v-id="first"
                                :list="componentList"
                                class="component_box"
                                :group="{ name: 'custom', pull: 'clone', put: false }"
                                item-key="value"
                            >
                                <div v-for="item in componentList" :key="item.value" style="width: 93px" @click="handleAddComponent(item)">
                                    <div class="component--item">
                                        <div class="iconfont component--item--icon" :class="`icon-${item.icon}`"></div>
                                        <div>{{ item.label }}</div>
                                    </div>
                                </div>
                            </VueDraggableNext>
                        </el-tab-pane>
                        <el-tab-pane label="营销组件">
                            <VueDraggableNext
                                v-if="lazyShow"
                                v-show="second"
                                :list="marketComponents"
                                class="component_box"
                                :group="{ name: 'custom', pull: 'clone', put: false }"
                                item-key="value"
                            >
                                <div v-for="item in marketComponents" :key="item.value" style="width: 93px" @click="handleAddComponent(item)">
                                    <div class="component--item">
                                        <div
                                            :class="`${!item.icon.includes('cylx') ? 'iconfont' : 'cylx-iconfont'} ${item.icon}`"
                                            class="component--item--icon"
                                        ></div>
                                        <div>{{ item.label }}</div>
                                    </div>
                                </div>
                            </VueDraggableNext>
                        </el-tab-pane>
                    </el-tabs>
                </div>
            </el-scrollbar>
        </div>
    </div>
</template>

<style lang="scss" scoped>
@import '@/assets/css/decoration/editPage.scss';
.component--item--icon {
    font-size: 28px;
    text-align: center;
    margin-bottom: 19px;
    position: relative;
}
.editor__component_new .is-horizontal {
    display: none;
    overflow-x: hidden;
}
</style>
