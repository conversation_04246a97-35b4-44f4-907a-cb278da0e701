<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-08-02 13:40:43
 * @LastEditors: lexy
 * @LastEditTime: 2022-11-04 13:54:43
-->
<script setup lang="ts">
/*
 *variable
 */
const $props = defineProps({
    formData: {
        type: Object,
        default() {
            return {}
        },
    },
})
const inlineStyle = computed(() => {
    return `height:${$props.formData.height}px`
})
/*
 *lifeCircle
 */
/*
 *function
 */
</script>

<template>
    <!-- 空白占位 -->
    <div :style="inlineStyle"></div>
</template>

<style lang="scss" scoped></style>
