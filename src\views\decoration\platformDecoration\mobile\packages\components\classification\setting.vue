<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-10-15 15:24:55
 * @LastEditors: lexy
 * @LastEditTime: 2023-06-17 16:59:41
-->
<script setup lang="ts">
import { useVModel } from '@vueuse/core'
import category from '../goods/category.vue'
import classification from './classification'
import { doGetPlatformLevelAndComNum } from '@/apis/decoration'
import type { DeCategoryType, DeCategoryItem } from './classification'
import type { PropType } from 'vue'
import { ElMessage } from 'element-plus'
/*
 *variable
 */
const $props = defineProps({
    formData: {
        type: Object as PropType<DeCategoryType>,
        default() {
            return classification
        },
    },
})
const $emit = defineEmits(['update:formData'])
const formModel = useVModel($props, 'formData', $emit)
const dialogShow = ref(false)
// 类目拖动下角标
const classDragIndex = ref(-1)
/*
 *lifeCircle
 */

const allCategoryList = ref<DeCategoryItem[]>([])
async function initCategoryList() {
    const { code, data } = await doGetPlatformLevelAndComNum({ current: 1, size: Number.MAX_SAFE_INTEGER })
    if (code === 200) {
        allCategoryList.value = data.records
    } else {
        ElMessage.error('获取商品分类失败')
    }
}

// 初始化分类列表
onMounted(() => {
    if (!formModel.value.categoryType) {
        formModel.value.categoryType = !formModel.value.categoryList.length ? 'all' : 'selected'
    }
    initCategoryList()
})

watch(
    () => formModel.value.categoryType,
    async (newVal) => {
        if (newVal === 'all' && allCategoryList.value.length > 0 && allCategoryList.value.length !== formModel.value.categoryList.length) {
            formModel.value.categoryList = [...allCategoryList.value]
        }
    },
    { immediate: true },
)

/*
 *function
 */
const handleSelectedCategory = (e: DeCategoryItem[]) => {
    formModel.value.categoryList = e
}
const handleDel = (index: number) => {
    formModel.value.categoryList.splice(index, 1)
}
/**
 * @LastEditors: lexy
 * @description: 开始拖动，记录拖动的组件下角标
 * @param {*} i
 */
const handleDragClass = (i: number) => {
    classDragIndex.value = i
}
/**
 * @LastEditors: lexy
 * @description:  阻止默认行为，否则drop事件不会触发
 * @returns {*}
 */
const handleDragoverClass = (e: Event) => {
    e.preventDefault()
}
/**
 * @LastEditors: lexy
 * @description: 被放置的容器触发事件，交换两个组件的位置
 * @param {*} i
 */
const handleDropClass = (i: number) => {
    if (classDragIndex.value === i) {
        return false
    }
    const temp = formModel.value.categoryList[i]
    formModel.value.categoryList[i] = formModel.value.categoryList[classDragIndex.value]
    formModel.value.categoryList[classDragIndex.value] = temp
}
</script>

<template>
    <el-form v-model="formModel">
        <el-form-item label="样式" prop="style">
            <el-radio-group v-model="formModel.style">
                <el-radio :value="1">纯分类</el-radio>
                <el-radio :value="2">分类商品</el-radio>
                <el-radio :value="3">大图商品</el-radio>
                <el-radio :value="4">商品列表</el-radio>
                <el-radio :value="5">金刚区分类</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item label="分类" prop="categoryType">
            <el-radio-group v-model="formModel.categoryType">
                <el-radio :value="'all'">全部分类</el-radio>
                <el-radio :value="'selected'">指定分类</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item v-if="formModel.categoryType === 'selected'">
            <div class="category">
                <div class="category__header">
                    <el-button :round="true" @click="dialogShow = true">选择分类</el-button>
                    <div>已选分类(上下拖动可排序)</div>
                </div>
                <div class="category__content">
                    <div class="category__list">
                        <div
                            v-for="(item, index) in formModel.categoryList"
                            :key="item.platformCategoryFirstId"
                            :draggable="true"
                            class="category__item"
                            @dragstart="handleDragClass(index)"
                            @dragover="handleDragoverClass"
                            @drop="handleDropClass(index)"
                        >
                            <div>{{ item.platformCategoryFirstName }}</div>
                            <div class="category__item--del" @click="handleDel(index)">删除</div>
                        </div>
                    </div>
                </div>
            </div>
        </el-form-item>
    </el-form>
    <category v-model:dialogShow="dialogShow" @choose="handleSelectedCategory" />
</template>

<style lang="scss" scoped>
@include b(category) {
    width: 363px;
    box-sizing: border-box;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 20px;
    font-size: 14px;

    @include e(header) {
        @include flex(space-between);
        color: #ccc;
        margin-bottom: 4px;
    }

    @include e(list) {
        min-height: 100px;
        max-height: 200px;
        overflow-y: auto;
    }

    @include e(item) {
        @include flex(space-between);

        @include m(del) {
            color: #ff0000;
            cursor: pointer;
        }

        margin-bottom: 3px;
    }
}
</style>
