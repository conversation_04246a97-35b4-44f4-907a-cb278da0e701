import { LinkSelectItem } from '@/components/link-select/linkSelectItem'
import { itemType } from '@/components/q-select-goods/type'
export interface CubeBoxFormData {
    borderWidth: number
    layoutWidth: number
    layoutHeight: number
    showMethod: number
    pageMargin: number
    width: number
    subEntry: IBanners[]
}
export interface IBanners {
    x: number
    y: number
    width: number
    height: number
    img: string
    link?: LinkSelectItem
    linkName?: string
    mainTitle?: string
    subTitle?: string
    background?: string
    backgroundColor?: string
    goods?: itemType[]
    goodWidth?: number
    dataType: 'Image' | 'Good'
}

export default {
    // 图片间隙
    borderWidth: 0,
    // 列数
    layoutWidth: 2,
    // 行数
    layoutHeight: 1,
    //行数index
    showMethod: 0,
    //页面边距离
    pageMargin: 0,
    //展示个数
    width: 2,
    //图片数组
    subEntry: [
        {
            x: 0,
            y: 0,
            width: 1,
            height: 1,
            // 数据源类型
            dataType: 'Image',
            img: '',
            link: {
                id: null,
                type: null,
                name: '',
                url: '',
                append: '',
            },
            linkName: '',
        },
        {
            x: 1,
            y: 0,
            width: 1,
            height: 1,
            img: '',
            // 数据源类型
            dataType: 'Image',
            link: {
                id: null,
                type: null,
                name: '',
                url: '',
                append: '',
            },
            linkName: '',
        },
    ],
}
