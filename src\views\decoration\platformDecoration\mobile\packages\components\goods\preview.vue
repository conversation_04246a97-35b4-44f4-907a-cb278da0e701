<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-08-08 17:53:14
 * @LastEditors: lexy
 * @LastEditTime: 2024-04-16 16:57:03
-->
<script setup lang="ts">
import defaultGoodData from './goods'
import usePriceRange from '@/composables/usePriceRange'
import PricesLine from './pricesLine.vue'
import Coner from './coner.vue'
import type { PropType } from 'vue'
import { ApiGoodItemType, ListStyle } from './goods'
const empty_img = 'https://devoss.chongyoulingxi.com/system-front/mobile/def_commodity.png'
const def_category_img = 'https://devoss.chongyoulingxi.com/system-front/mobile/def_category.png'
const sortList = ['人气', '销量', '新品', '价格']
/*
 *variable
 */
const $props = defineProps({
    formData: {
        type: Object as PropType<typeof defaultGoodData>,
        default() {
            return defaultGoodData
        },
    },
})
const activeItemSort = ref<string>('')
// 商品
const goodsList = ref<ApiGoodItemType[] | undefined[]>([])
const pageStyle = computed(() => ({
    padding: `0px 10px`,
    fontSize: '15px',
    minHeight: '200px',
}))
/**
 * @LastEditors: lexy
 * @description: 动态显示商品边框样式
 */
const goodStyle = computed(() => {
    let margin = ''
    let width = ''
    const { listStyle } = $props.formData
    switch (listStyle) {
        case 'goods-style--four':
            margin = `0px 10px 0px 0px`
            break
        case 'goods-style--two':
            margin = `0px 0px ${10}px 0px`
            width = `calc(50% - ${10 / 2}px)`
            break
        case 'goods-style--five':
            margin = `0px 0px ${10}px 0px`
            width = `calc(50% - ${10 / 2}px)`
            break
        default:
            margin = `10px 0`
            break
    }
    return { margin, width }
})
/**
 * @LastEditors: lexy
 * @description: 模式2调整边距
 */
const goodStyle2 = computed(() => {
    const margin = `0px 0px 10px 0px`
    return { margin }
})
watch(
    () => $props.formData?.firstCatList,
    (newVal) => {
        if (newVal?.length > 0) {
            if ($props.formData?.ponentType === 1) {
                if (!$props.formData.currentCategoryId) {
                    $props.formData.currentCategoryId = newVal[0].platformCategoryFirstId
                }
                const hasIdInList = newVal.find((item) => item.platformCategoryFirstId === $props.formData.currentCategoryId)
                if (!hasIdInList) {
                    $props.formData.currentCategoryId = newVal[0].platformCategoryFirstId
                }
            } else {
                goodsList.value = $props.formData?.firstCatList
            }
        }
    },
    { immediate: true },
)
watch(
    () => [$props.formData.currentCategoryId, $props.formData.ponentType],
    () => {
        getGoodsList()
    },
    { immediate: true },
)
watch(
    () => $props.formData.featuredCategory,
    (newVal) => {
        if (newVal) {
            $props.formData.currentCategoryId = 'recommend'
        } else if (!!$props.formData.firstCatList?.length) {
            $props.formData.currentCategoryId = $props.formData.firstCatList[0].platformCategoryFirstId
        }
    },
    { immediate: true },
)
watch(
    () => $props.formData.sort,
    (val) => {
        if (val) {
            activeItemSort.value = sortList[0]
        }
    },
)
/*
 *lifeCircle
 */
onMounted(() => {
    getGoodsList()
})
/*
 *function
 */
const activeClass = (id: string) => {
    Object.assign($props.formData, { currentCategoryId: id })
}
function getGoodsList() {
    goodsList.value = []
    const { ponentType = 1, currentCategoryId = '', firstCatList = [], goods = [] } = $props.formData
    // 选择商品数量时对应显示预览效果
    if (ponentType === 1) {
        if (currentCategoryId === 'recommend') {
            // 暂定推荐列表默认展示10个元素
            goodsList.value = Array.from({ length: 10 }, () => undefined)
        } else if (firstCatList?.length > 0) {
            const item = firstCatList.find((i) => !!i && i.platformCategoryFirstId === currentCategoryId)
            if (item && item.productNum) {
                goodsList.value = Array.from({ length: item.productNum }, () => undefined)
            }
        }
    } else if (ponentType === 2) {
        goodsList.value = goods
    }
}

const waterfallHeight = (i: number) => {
    if ($props.formData.listStyle === 'goods-style--five') {
        return { height: [5, -5][i % 2] + 240 + 'px' }
    }
    return {}
}

const tabbarHeight = computed(() => `${$props.formData.categoryStyle === 'pic-text' ? 68 : 42}px`)
</script>

<template>
    <div class="goods__ponent-page">
        <!-- 头部分类 s -->
        <div v-if="$props.formData.ponentType === 1 && $props.formData.firstCatList?.length" class="tab__bar-box" :style="{ height: tabbarHeight }">
            <div class="con">
                <div
                    v-if="$props.formData.featuredCategory"
                    :class="[
                        'class__item',
                        $props.formData.categoryStyle === 'pic-text' ? 'class__pic' : 'class__text',
                        'recommend' === $props.formData.currentCategoryId
                            ? $props.formData.categoryStyle === 'pic-text'
                                ? 'pic-active'
                                : 'text-active'
                            : '',
                    ]"
                    @click="activeClass('recommend')"
                >
                    <div v-if="$props.formData.categoryStyle === 'pic-text'" class="category_img">
                        <img :src="def_category_img" />
                    </div>
                    <span class="item__name">推荐</span>
                </div>
                <div
                    v-for="(item, idx) in $props.formData.firstCatList"
                    :key="idx"
                    :class="[
                        'class__item',
                        $props.formData.categoryStyle === 'pic-text' ? 'class__pic' : 'class__text',
                        item.platformCategoryFirstId === $props.formData.currentCategoryId
                            ? $props.formData.categoryStyle === 'pic-text'
                                ? 'pic-active'
                                : 'text-active'
                            : '',
                    ]"
                    @click="activeClass(item.platformCategoryFirstId)"
                >
                    <div v-if="$props.formData.categoryStyle === 'pic-text'" class="category_img">
                        <img :src="item.pic || def_category_img" />
                    </div>
                    <span class="item__name">{{ item.platformCategoryFirstName }}</span>
                </div>
            </div>
            <div v-if="$props.formData.moreGoods" class="sort-btn" :style="{ height: tabbarHeight }">
                <div class="sort-btn__icon" />
            </div>
        </div>
        <div v-if="$props.formData.sort" class="sort-group">
            <div v-for="item in sortList" :key="item" class="sort-item" :class="{ selected: item === activeItemSort }" @click="activeItemSort = item">
                {{ item }}
            </div>
        </div>
        <!-- 头部分类 e -->
        <!-- 商品主体展示 s -->
        <!-- $props.formData.listStyle === ListStyle['goods-style--two'] && goodsList.length === 1 ? '' : $props.formData.listStyle -->
        <div
            v-if="
                ($props.formData.ponentType === 1 && $props.formData.firstCatList?.length) ||
                ($props.formData.ponentType === 2 && $props.formData.goods.length)
            "
            class="goods"
            :class="$props.formData.listStyle === ListStyle['goods-style--two'] && goodsList.length === 1 ? '' : $props.formData.listStyle"
            :style="{ ...pageStyle, scrollbarWidth: 'none' }"
        >
            <!-- 大图模式 -->
            <template v-if="$props.formData.listStyle === ListStyle['goods-style--one']">
                <div v-for="(item, idx) in goodsList" :key="idx" :class="['goods-item']">
                    <!-- tag -->
                    <coner :tag-show="$props.formData.showContent.tagShow" :tag-style="$props.formData.showContent.tagStyle"></coner>
                    <!-- tag -->
                    <!-- 大图模式 s -->
                    <div class="goods-item__large_box">
                        <div class="goods-item__large">
                            <img :class="[$props.formData.ponentType === 1 ? 'show__mall' : 'show__big']" :src="item?.pic || empty_img" />
                        </div>
                        <div class="goods-item__large_foot" style="padding: 0 7px">
                            <div class="goods-item__name" style="width: 100%">
                                {{ item ? item.productName : '商品名称' }}
                            </div>
                            <prices-line
                                :good="item"
                                :show-price="!!$props.formData.showPrice"
                                :is-preview="!item || !item.salePrices"
                                :show-sales="$props.formData.showSales"
                                :button-style="$props.formData.showContent.buttonStyle"
                            />
                        </div>
                        <div class="goods-item__large_foot--placeholder-node"></div>
                    </div>
                </div>
            </template>
            <!-- 大图模式 -->
            <!-- 一行两个 goodStyle2 goodStyle-->
            <template v-else-if="$props.formData.listStyle === ListStyle['goods-style--two']">
                <div
                    v-for="(item, idx) in goodsList"
                    :key="idx"
                    :class="['goods-item']"
                    :style="{ ...(idx === $props.formData.goods.length - 1 ? goodStyle2 : goodStyle) }"
                >
                    <coner :tag-show="$props.formData.showContent.tagShow" :tag-style="$props.formData.showContent.tagStyle"></coner>
                    <div class="goods-item__icon">
                        <div class="ipic" style="border-radius: 10px 10px 0 0">
                            <img :class="[$props.formData.ponentType === 1 ? 'show__mall' : 'show__big']" :src="item?.pic || empty_img" />
                        </div>
                    </div>

                    <div class="goods-item__foot" style="padding: 0 7px">
                        <div class="goods-item__name">
                            {{ item ? item.productName : '商品名称一行两个' }}
                        </div>
                        <div class="goods-item__bottom">
                            <prices-line
                                :good="item"
                                :show-price="!!$props.formData.showPrice"
                                :is-preview="true"
                                :show-sales="$props.formData.showSales"
                                :button-style="$props.formData.showContent.buttonStyle"
                            />
                        </div>
                    </div>
                </div>
            </template>
            <!-- 一行两个 -->
            <!-- 详细列表 -->
            <template v-else-if="$props.formData.listStyle === ListStyle['goods-style--three']">
                <div
                    v-for="(item, idx) in goodsList"
                    :key="idx"
                    :class="['goods-item', 'goods-item-three_shadow']"
                    :style="{ ...(idx === $props.formData.goods.length - 1 ? goodStyle2 : goodStyle) }"
                >
                    <coner :tag-show="$props.formData.showContent.tagShow" :tag-style="$props.formData.showContent.tagStyle"></coner>
                    <div class="goods-item__three">
                        <div class="goods-item__three_img">
                            <img :src="item?.pic || empty_img" />
                        </div>
                    </div>

                    <div class="goods-item__foot" style="padding: 0 7px">
                        <div class="goods-item__name">
                            {{ item ? item.productName : '商品名称' }}
                        </div>
                        <div class="goods-item__bottom">
                            <prices-line
                                :good="item"
                                :show-price="!!$props.formData.showPrice"
                                :is-preview="true"
                                :show-sales="$props.formData.showSales"
                                :button-style="$props.formData.showContent.buttonStyle"
                            />
                        </div>
                    </div>
                </div>
            </template>
            <!-- 详细列表 -->
            <!-- 横向滑动 -->
            <template v-else-if="$props.formData.listStyle === ListStyle['goods-style--four']">
                <div
                    v-for="(item, idx) in goodsList"
                    :key="idx"
                    :class="['goods-item', 'goods-item-three_shadow']"
                    :style="{ ...(idx === $props.formData.goods.length - 1 ? goodStyle2 : goodStyle) }"
                >
                    <coner :tag-show="$props.formData.showContent.tagShow" :tag-style="$props.formData.showContent.tagStyle"></coner>
                    <div class="goods-item__four">
                        <div class="goods-item__four_img">
                            <img :src="item?.pic || empty_img" />
                        </div>
                    </div>

                    <div class="goods-item__foot" style="padding: 0 7px">
                        <div class="goods-item__name" style="font-weight: 600; font-size: 11px">
                            {{ item ? item.productName : '商品名称' }}
                        </div>
                        <div class="goods-item__bottom">
                            <prices-line
                                :good="item"
                                :show-price="!!$props.formData.showPrice"
                                :is-preview="true"
                                :show-sales="$props.formData.showSales"
                                :button-style="$props.formData.showContent.buttonStyle"
                            />
                        </div>
                    </div>
                </div>
            </template>
            <!-- 横向滑动 -->
            <!-- 瀑布 -->
            <template v-else>
                <div
                    v-for="(item, idx) in goodsList"
                    :key="idx"
                    :class="['goods-item']"
                    :style="{ ...waterfallHeight(idx), ...(idx === $props.formData.goods.length - 1 ? goodStyle2 : goodStyle) }"
                >
                    <coner :tag-show="$props.formData.showContent.tagShow" :tag-style="$props.formData.showContent.tagStyle"></coner>
                    <div class="goods-item__icon">
                        <div class="ipic" style="border-radius: 10px 10px 0 0">
                            <img :class="[$props.formData.ponentType === 1 ? 'show__mall' : 'show__big']" :src="item?.pic || empty_img" />
                        </div>
                    </div>

                    <div class="goods-item__foot" style="padding: 0 7px">
                        <div class="goods-item__name">
                            {{ item ? item.productName : '商品名称' }}
                        </div>
                        <div class="goods-item__bottom" style="margin-top: 10px">
                            <prices-line
                                :good="item"
                                :show-price="!!$props.formData.showPrice"
                                :is-preview="true"
                                :show-sales="$props.formData.showSales"
                                :button-style="$props.formData.showContent.buttonStyle"
                            />
                        </div>
                    </div>
                </div>
            </template>
            <!-- 瀑布 -->
        </div>
        <!-- 商品主体展示 e -->
        <div
            v-if="
                ($props.formData.ponentType === 1 && !$props.formData.firstCatList?.length) ||
                ($props.formData.ponentType === 2 && !$props.formData.goods.length)
            "
            class="no__goods-item"
        >
            <img :src="empty_img" />
        </div>
    </div>
</template>

<style scoped lang="scss">
@import '@/assets/css/decoration/goods.scss';

.goods__ponent-page {
    .tab__bar-box {
        width: 100%;
        overflow-y: hidden;
        overflow-x: scroll;
        scrollbar-width: none;
        position: relative;
        margin-bottom: 12px;

        .con {
            display: inline-flex;
            flex-wrap: nowrap;
            align-items: center;
            width: auto;
            min-width: 100%;
            height: inherit;
            padding-right: 50px;
        }

        .class__item {
            display: inline-flex;
            justify-content: center;
            text-align: center;
            align-items: center;
            position: relative;
            font-size: 14px;
            cursor: pointer;
            height: inherit;
            min-width: 72px;
            transition: all 0.3s ease; // 添加整体过渡效果

            .category_img {
                width: 40px;
                height: 40px;
                border-radius: 100%;
                overflow: hidden;
                background-color: #fff;
                img {
                    width: inherit;
                    height: inherit;
                    object-fit: contain;
                    background: transparent;
                }
            }

            span {
                display: inline-block;
                white-space: nowrap;
                line-height: 140%;
                color: $cylx-text-gray-color;
                font-family: PingFangSC-Medium;
                transition: color 0.3s ease; // 添加文字颜色
            }

            // 添加悬停效果
            &:hover {
                transform: translateY(-2px);
            }
        }
        .class__pic {
            z-index: 8;
            flex-direction: column;
            height: 66px;
            gap: 4;
            padding: 2px;
            .category_img {
                background: linear-gradient(180deg, rgb(255, 255, 255) 0.763%, rgba(255, 255, 255, 0.86) 67.176%, rgba(255, 255, 255, 0) 100%);
            }
            span {
                font-size: 14px;
            }
        }

        .pic-active {
            .category_img {
                background: linear-gradient(180deg, rgb(232, 217, 245) 0.763%, rgba(255, 255, 255, 0.86) 67.176%, rgba(255, 255, 255, 0) 100%);
            }
            span {
                color: $theme-color;
                font-weight: 500;
            }
        }

        .class__text {
            z-index: 8;
            display: inline-block;
            position: relative;
            padding: 10px 4px;
            span {
                color: $cylx-text-color;
                font-size: 15px;
            }
        }

        .text-active {
            span {
                color: $theme-color;
            }
            &::after {
                content: '';
                position: absolute;
                top: 40px;
                bottom: 0px;
                left: calc(50% - 21px);
                width: 42px;
                height: 2px;
                border-radius: 2px;
                /* 主色/常规 */
                background: #a74be8;
                transform-origin: center bottom;
                animation: arcAppear 0.3s ease forwards; // 添加圆弧出现动画
            }
        }

        .sort-btn {
            content: '';
            position: sticky;
            left: calc(100% - 50px);
            right: 0;
            top: 0;
            width: 50px;
            bottom: 0;
            background-color: white;
            display: inline-flex;
            align-items: center;
            z-index: 10;
            .sort-btn__icon {
                width: 19px;
                height: 19px;
                margin-left: 15px;
                background-image: url('@/assets/image/decoration/menu.png');
                background-repeat: no-repeat;
                background-position: center;
                background-size: contain;
            }
        }
    }

    .goods-item__icon {
        width: 100%;
        height: 160px;

        .ipic {
            display: inline-block;
            width: 100%;
            height: 100%;
            background-color: rgba(233, 247, 253, 1);
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 10px;
            overflow: hidden;

            .show__mall {
                display: inline-block;
                width: 44px;
                height: 46px;
            }

            .show__big {
                display: inline-block;
                width: 100%;
                height: 100%;
            }
        }
    }

    .goods-style--three {
        .goods-item__icon {
            height: 128px;
            width: 128px;
            margin-right: 10px;
            flex: none;

            .ipic {
                height: 128px;
                overflow: hidden;
            }
        }
    }

    .spellpre__goods--delivery {
        color: #a3a3a3;
        font-size: 12px;
        font-weight: 400;
        padding-top: 10px;
    }
}
</style>
