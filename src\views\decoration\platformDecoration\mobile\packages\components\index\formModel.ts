/*
 * @description: 整合组件初始化数据
 * @Author: lexy
 * @Date: 2022-08-17 17:17:21
 * @LastEditors: lexy
 * @LastEditTime: 2024-02-03 16:46:32
 */
import blankPaceholder from '../blankHolder/blankPaceholder'
import cubeBox from '../cube-box/cubeBox'
import goods from '../goods/goods'
import navigation from '../navigation/navigation'
import resizeImage from '../resize-image/resize-image'
import richText from '../rich-text/rich-text'
import search from '../search/search'
import separator from '../separator/separator'
import swiper from '../swiper/swiper'
import titleBar from '../title-bar/title-bar'
import video from '../video/video'
import navBar from '../navBar/nav-bar'
import userCenter from '../userCenter/user-center'
import secKill from '../sec-kill/sec-kill'
import live from '../live/live'
import shopGoods from '../shopGoods/shopGoods'
import positioningStyle from '../positioningStyle/positioningStyle'
import compose from '../compose/compose'
import classification from '../classification/classification'
import onlyPromotion from '../onlyPromotion/onlyPromotion'
const defaultData = {
    blankPaceholder,
    cubeBox,
    goods,
    navigation,
    resizeImage,
    richText,
    search,
    separator,
    swiper,
    titleBar,
    video,
    navBar,
    userCenter,
    secKill,
    live,
    shopGoods,
    positioningStyle,
    compose,
    classification,
    onlyPromotion,
}
const defaultDataArr = Object.values(defaultData)
type FormDataType = typeof defaultDataArr[number]

export interface ComponentItem {
    icon: string
    id?: string
    label: string
    value: keyof typeof defaultData
    formData?: FormDataType
    showType?: boolean
    type?: number
}

export default defaultData
