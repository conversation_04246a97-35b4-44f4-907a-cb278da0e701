/*
 * @description: 所有展示组价
 * @Author: lexy
 * @Date: 2022-08-17 17:26:14
 * @LastEditors: lexy
 * @LastEditTime: 2023-06-19 19:43:45
 */
import { defineAsyncComponent } from 'vue'
export default {
    blankPaceholder: defineAsyncComponent(() => import('../blankHolder/preview.vue')),
    cubeBox: defineAsyncComponent(() => import('../cube-box/preview.vue')),
    goods: defineAsyncComponent(() => import('../goods/preview.vue')),
    navigation: defineAsyncComponent(() => import('../navigation/preview.vue')),
    resizeImage: defineAsyncComponent(() => import('../resize-image/preview.vue')),
    richText: defineAsyncComponent(() => import('../rich-text/preview.vue')),
    search: defineAsyncComponent(() => import('../search/preview.vue')),
    separator: defineAsyncComponent(() => import('../separator/preview.vue')),
    swiper: defineAsyncComponent(() => import('../swiper/preview.vue')),
    titleBar: defineAsyncComponent(() => import('../title-bar/preview.vue')),
    video: defineAsyncComponent(() => import('../video/preview.vue')),
    navBar: defineAsyncComponent(() => import('../navBar/preview.vue')),
    classification: defineAsyncComponent(() => import('../classification/preview.vue')),
    userCenter: defineAsyncComponent(() => import('../userCenter/preview.vue')),
    secKill: defineAsyncComponent(() => import('../sec-kill/preview.vue')),
    live: defineAsyncComponent(() => import('../live/preview.vue')),
    shopGoods: defineAsyncComponent(() => import('../shopGoods/preview.vue')),
    positioningStyle: defineAsyncComponent(() => import('../positioningStyle/preview.vue')),
    compose: defineAsyncComponent(() => import('../compose/preview.vue')),
    onlyPromotion: defineAsyncComponent(() => import('../onlyPromotion/preview.vue')),
}
