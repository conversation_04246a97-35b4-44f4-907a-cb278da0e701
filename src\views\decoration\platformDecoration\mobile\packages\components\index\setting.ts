/*
 * @description: 所有配置组价
 * @Author: lexy
 * @Date: 2022-08-17 17:26:14
 * @LastEditors: lexy
 * @LastEditTime: 2023-06-19 19:44:23
 */
import { defineAsyncComponent } from 'vue'
export default {
    blankPaceholder: defineAsyncComponent(() => import('../blankHolder/setting.vue')),
    cubeBox: defineAsyncComponent(() => import('../cube-box/setting.vue')),
    goods: defineAsyncComponent(() => import('../goods/setting.vue')),
    navigation: defineAsyncComponent(() => import('../navigation/setting.vue')),
    resizeImage: defineAsyncComponent(() => import('../resize-image/setting.vue')),
    richText: defineAsyncComponent(() => import('../rich-text/setting.vue')),
    search: defineAsyncComponent(() => import('../search/setting.vue')),
    separator: defineAsyncComponent(() => import('../separator/setting.vue')),
    swiper: defineAsyncComponent(() => import('../swiper/setting.vue')),
    titleBar: defineAsyncComponent(() => import('../title-bar/setting.vue')),
    video: defineAsyncComponent(() => import('../video/setting.vue')),
    navBar: defineAsyncComponent(() => import('../navBar/setting.vue')),
    classification: defineAsyncComponent(() => import('../classification/setting.vue')),
    userCenter: defineAsyncComponent(() => import('../userCenter/setting.vue')),
    secKill: defineAsyncComponent(() => import('../sec-kill/setting.vue')),
    live: defineAsyncComponent(() => import('../live/setting.vue')),
    shopGoods: defineAsyncComponent(() => import('../shopGoods/setting.vue')),
    positioningStyle: defineAsyncComponent(() => import('../positioningStyle/setting.vue')),
    compose: defineAsyncComponent(() => import('../compose/setting.vue')),
    onlyPromotion: defineAsyncComponent(() => import('../onlyPromotion/setting.vue')),
}
