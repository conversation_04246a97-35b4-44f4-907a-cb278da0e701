/*
 * @description:
 * @Author: lexy
 * @Date: 2022-12-06 13:31:18
 * @LastEditors: lexy
 * @LastEditTime: 2022-12-06 17:10:59
 */
/**
 * @LastEditors: lexy
 * @description: 直播间装修数据
 * @param {number} codeStyle 选择样式,1 大图模式 2 一行两个 3 横向滑动
 * @param {string} listTitle 列表标题
 * @param {number} listNum 展示直播数量
 * @param {boolean} borderRadius 是否圆角
 * @param {number} listMargin 列表的左右 margin 最小 0 最大值 30
 * @param {number} upDownMargin item 上下边距  最小 0 最大值 30
 */
const formData = { codeStyle: 1, listTitle: '精选直播', listNum: 6, borderRadius: true, listMargin: 0, upDownMargin: 0 }
export default formData
