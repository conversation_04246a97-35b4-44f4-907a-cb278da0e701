/*
 * @description:
 * @Author: lexy
 * @Date: 2023-05-15 15:03:51
 * @LastEditors: lexy
 * @LastEditTime: 2023-06-29 16:04:42
 */
/**
 * @LastEditors: lexy
 * @description: 控件nav默认值
 * @param {number} codeStyle 选择样式,1普通样式 2中间大图样式
 * @param {} menuList 底部导航列表
 * @param {string} selectColor 字体选中颜色
 * @param {string} defaultColor 字体未选择颜色
 * @param {string} text 导航名称
 * @param {string} iconType 图标类型 1-系统图标 2-自定义图标
 * @param {string} iconPath 未选中图标url
 * @param {string} selectedIconPath 选中图标url
 * @param {string} defIcon 系统图标
 * @param {string} actIcon 系统选中图标
 * @param {boolean} isHome  是否为首页
 * @param {number} sortIndex  下标位置
 */
import { navBarDefaultData } from '@/components/link-select/linkSelectItem'
const formData = {
    codeStyle: 1,
    selectColor: '#F64E3F',
    defaultColor: '#7A7E83',
    menuList: [
        {
            text: '首页',
            iconType: 1,
            codeStyle: 1,
            iconPath: 'https://devoss.chongyoulingxi.com/system-front/mobile/def_home.png',
            selectedIconPath: 'https://devoss.chongyoulingxi.com/system-front/mobile/sef_home.png',
            defIcon: 'https://devoss.chongyoulingxi.com/system-front/mobile/def_home_icon.png',
            actIcon: 'https://devoss.chongyoulingxi.com/system-front/mobile/act_home_icon.png',
            isHome: false,
            id: '',
            sortIndex: 0,
            isAdd: true,
            link: getLink('首页'),
        },
        {
            text: '购物车',
            iconType: 1,
            codeStyle: 1,
            iconPath: 'https://devoss.chongyoulingxi.com/system-front/mobile/def_cart.png',
            selectedIconPath: 'https://devoss.chongyoulingxi.com/system-front/mobile/sel_cart.png',
            defIcon: 'https://devoss.chongyoulingxi.com/system-front/mobile/def_cart.png',
            actIcon: 'https://devoss.chongyoulingxi.com/system-front/mobile/sel_cart.png',
            isHome: false,
            id: '',
            isAdd: true,
            sortIndex: 1,
            link: getLink('购物车'),
        },
    ],
}
export default formData
export const navBarItem = {
    text: '',
    iconType: 2,
    iconPath: '',
    selectedIconPath: '',
    defIcon: '',
    actIcon: '',
    isHome: false,
    id: '',
    isAdd: true,
    codeStyle: 1,
    link: {
        id: '1',
        type: 0,
        name: '',
        url: '/',
        append: 1,
    },
}

export const defaultNavBarData = {
    id: '',
    icon: '',
    value: 'navBar',
    label: '底部导航',
    formData,
}
function getLink(text: string) {
    return navBarDefaultData.filter((item) => {
        return item.name === text
    })[0]
}
