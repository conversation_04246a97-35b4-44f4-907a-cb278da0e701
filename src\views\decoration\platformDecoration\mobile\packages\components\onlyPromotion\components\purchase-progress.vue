<template>
    <div class="purchase-progress" :class="{ 'purchase-progress--sold-out': soldOut }">
        <!-- 抢购进度 -->
        <!-- percentage为100时前景色为灰 -->
        <div
            v-if="percentage !== undefined"
            class="purchase-progress__remaining"
            :style="{ fontVariantAlternates: percentage === 100 ? '#999' : '#333' }"
        >
            <el-progress
                :percentage="percentage"
                :color="soldOut ? '#C9CDD4' : '#F53F3F'"
                :show-text="false"
                :stroke-width="16"
                :style="{ width: '100%' }"
            />
            <!-- 剩余数量 【宽度不够直接不显示文字部分】 -->
            <div v-if="showLabel && props.showStock" style="min-width: 70px; text-align: right">
                <span v-if="percentage !== 100">剩余{{ 100 - percentage }}%</span>
                <span v-else class="purchase-progress__sold-out">已售罄</span>
            </div>
        </div>
        <div v-if="showBtn" class="purchase-progress__button" :style="{ backgroundColor: props.disable || soldOut ? '#f5f5f5' : '#fff8f8' }">
            <img :src="props.disable || soldOut ? Disable_PurchaseButton : PurchaseButton" alt="抢购" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue'
import PurchaseButton from '@/assets/image/decoration/purchase-btn.png'
import Disable_PurchaseButton from '@/assets/image/decoration/purchase-btn-disabled.png'

const props = defineProps({
    soldCount: {
        type: [Number, String],
        default: 0,
    },
    totalStock: {
        type: [Number, String],
        default: 0,
    },
    showLabel: {
        type: Boolean,
        default: true,
    },
    showBtn: {
        type: Boolean,
        default: true,
    },
    showStock: {
        type: Boolean,
        default: true,
    },
    disable: {
        type: Boolean,
        default: false,
    },
})

const soldOut = computed(() => {
    return props.soldCount >= props.totalStock
})

const percentage = computed(() => {
    const soldCount = Number(props.soldCount)
    const totalStock = Number(props.totalStock)
    if (soldCount >= totalStock) {
        return 100
    }
    // 要注意取整（四舍五入）、超出100%要显示100%
    return Math.round((soldCount / totalStock) * 100)
})
</script>

<style lang="scss" scoped>
:deep(.el-progress-bar__outer) {
    background-color: #ffece8;
}
.purchase-progress {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 27px;
    background-color: #fff8f8;
    border-radius: 8px;
    overflow: hidden;

    // 售罄状态整体样式
    &--sold-out {
        background-color: #f5f5f5;
    }

    &__remaining {
        display: flex;
        align-items: center;
        padding: 5px 4px;
        width: 100%;

        span {
            margin-left: 8px;
            /* 辅助信息12/Regular */
            color: rgb(29, 33, 41);
            font-family: 苹方-简;
            font-size: 12px;
            font-weight: 400;
            line-height: 140%;
            letter-spacing: 0%;
            text-align: right;
        }
    }

    &__button {
        img {
            width: 45.53px;
            height: 27px;
            object-fit: contain;
        }
    }
    &__sold-out {
        color: #999;
    }
}
</style>
