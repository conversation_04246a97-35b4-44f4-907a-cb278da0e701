<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-12-05 10:33:52
 * @LastEditors: lexy
 * @LastEditTime: 2023-02-02 09:52:55
-->
<script setup lang="ts">
import defaultSeckillData from './onlyPromotion'
import GoodPrice from '@/components/good-price/good-price.vue'
import PurchaseProgress from './components/purchase-progress.vue'
import { doGetOnlyPromotionGoods, doGetMemberType } from '@/apis/decoration'
import type { PropType } from 'vue'
import type { OnlyPromotionGoodSkuType } from './onlyPromotion'
import iconfont from '@/assets/css/font/iconfont.json'

/*
 *variable
 */
const $props = defineProps({
    formData: {
        type: Object as PropType<typeof defaultSeckillData>,
        default: defaultSeckillData,
    },
})
const goodsList = ref<OnlyPromotionGoodSkuType[]>([])
const { divTenThousand } = useConvert()
const currentTypeIndex = ref<number>(0)
const getGoodStyle = computed(() => {
    const classs = ['good-one', 'good-two', 'good-three']
    return classs[$props.formData.goodStyle - 1]
})

const goodsShowList = computed(() => {
    if ($props.formData.goodNum === 0 || goodsList.value?.length <= $props.formData.goodNum) {
        return goodsList.value || []
    } else {
        return goodsList.value.splice(0, $props.formData.goodNum)
    }
})

/*
 *lifeCircle
 */
// 自定义防抖函数
function debounce(fn: Function, delay: number) {
    let timer: NodeJS.Timeout | null = null
    return function (this: any, ...args: any[]) {
        if (timer) clearTimeout(timer)
        timer = setTimeout(() => {
            fn.apply(this, args)
        }, delay)
    }
}

const loadPlatformGoodsList = debounce(async () => {
    let params = { current: 1, size: 100 }
    if (currentTypeIndex.value) {
        const { id, name, paid } = tagGroups.value[currentTypeIndex.value]
        params = {
            ...params,
            memberType: { id, memberName: name, memberId: id, paid },
        }
    }
    const res = await doGetOnlyPromotionGoods(params)
    goodsList.value = res.data?.records || []
}, 300)

/*
 *function
 */
const tagGroups = ref<any[]>([])

const loadMemberType = async () => {
    const res = await doGetMemberType()
    if (res.code === 200) {
        tagGroups.value = [{ id: '', name: '全部' }, ...res.data]
    }
}
/**
 * 变更选中的参与会员类型筛选条件
 */
const changeCurrentType = (index: number) => {
    currentTypeIndex.value = index
}
/**
 * 格式化已售
 */
const formatSold = (allStock: string | number, rate: number) => Math.round(Number(allStock) * rate)

// 当分类展示时，获取分类列表
watch(
    () => $props.formData.showCategory,
    (newVal) => {
        if (newVal === 1) {
            loadMemberType()
        }
    },
    { immediate: true },
)

watch(
    () => currentTypeIndex.value,
    () => {
        // loadPlatformGoodsList 已经是防抖函数，直接调用即可
        loadPlatformGoodsList()
    },
    { immediate: true },
)

const headBackground = computed(() => {
    return $props.formData.bgStyle.type === 1
        ? `linear-gradient(${$props.formData.bgStyle.deg}deg, ${$props.formData.bgStyle.startColor}, ${$props.formData.bgStyle.endColor})`
        : `url(${$props.formData.bgStyle.url}) no-repeat center center`
})

/**
 * 获取图标类名
 * @param clazz 图标类名
 * @returns 图标类名
 */
const getIconClass = (clazz: string) => {
    return 'iconfont ' + iconfont.css_prefix_text + clazz
}

/**
 * 是否开始
 * @param startTime 活动开始时间
 */
const isStart = (startTime: string) => {
    const startTimestamp = new Date(startTime).getTime()
    const currentStamp = new Date().getTime()
    return startTimestamp <= currentStamp
}
</script>

<template>
    <div :class="[getGoodStyle, 'onlyPromotion']">
        <div v-if="$props.formData.showHeader" class="onlyPromotion__title" :style="{ background: headBackground }">
            <div class="onlyPromotion__title--left">
                <span v-if="$props.formData.titleStyle.type === 2">{{ $props.formData.titleStyle.text }}</span>
                <img v-if="$props.formData.titleStyle.type === 1 && $props.formData.titleStyle.url" :src="$props.formData.titleStyle.url" />
            </div>
            <div class="onlyPromotion__title--more">
                <span v-if="$props.formData.headerRight.text">{{ $props.formData.headerRight.text }}</span>
                <el-icon v-if="!$props.formData.headerRight.iconUrl"><i-ep-arrowRight /></el-icon>
                <i v-else :class="getIconClass(formData.headerRight.iconUrl)" />
            </div>
        </div>

        <div v-if="$props.formData.showCategory === 1" class="onlyPromotion__member-type-list">
            <div class="box">
                <span
                    v-for="(item, i) in tagGroups"
                    :key="item.id"
                    class="member-type"
                    :style="{ color: i === currentTypeIndex ? '#409EFF' : '#303133' }"
                    @click="changeCurrentType(i)"
                >
                    {{ item.name }}
                </span>
            </div>
        </div>

        <!-- 单列展示 start -->
        <div v-if="$props.formData.listStyle === 1" class="onlyPromotion__row">
            <div v-for="(item, index) in goodsShowList" :key="index" class="onlyPromotion__row-item">
                <div class="onlyPromotion__row-item--img">
                    <div v-if="$props.formData.showInfo.includes(5) && item.endTime" class="end-corner">截止:{{ item.endTime?.substring(5) }}</div>
                    <img class="product-img" :src="item.productPic" />
                    <div class="pic-border" :data-desc="item.desc"></div>
                </div>
                <div class="onlyPromotion__row-item--info">
                    <div v-if="$props.formData.showInfo.includes(1) && item.productName" class="onlyPromotion__row-item--title">
                        {{ item.productName }}
                    </div>
                    <div class="onlyPromotion__row-item--members">
                        <div
                            v-for="member in item.joinMember"
                            :key="member.id"
                            class="tag"
                            :style="member.labelJson && { color: member.labelJson?.fontColor, borderColor: member.labelJson?.labelColor }"
                        >
                            {{ member.name }}
                        </div>
                    </div>
                    <div v-if="$props.formData.showInfo.includes(3)" class="onlyPromotion__row-item--price">
                        <div class="price-tag">专享价</div>
                        <good-price :price="divTenThousand(item.minPrice).toString()" :unit-size="12" :integer-size="20" :decimal-size="12" />
                        <div v-if="$props.formData.showInfo.includes(4)" class="origin-price">¥{{ divTenThousand(item.actualPaidPrice) }}</div>
                    </div>
                    <!-- 抢购进度组件 -->
                    <PurchaseProgress
                        v-if="$props.formData.showInfo.includes(2) && item.stock !== undefined"
                        :total-stock="item.stock"
                        :sold-count="formatSold(item.stock, 0.6)"
                        :show-btn="$props.formData.showBtn === 1"
                        :show-stock="$props.formData.showInfo.includes(6)"
                        :disable="!isStart(item.startTime)"
                    />
                </div>
            </div>
        </div>
        <!-- 单列展示 end -->

        <!-- 两列展示 start -->
        <div v-if="$props.formData.listStyle === 2" class="onlyPromotion__col">
            <div v-for="(item, index) in goodsShowList" :key="index" class="onlyPromotion__col-item">
                <div class="onlyPromotion__col-item--img">
                    <div v-if="$props.formData.showInfo.includes(5) && item.endTime" class="end-corner">截止:{{ item.endTime.substring(5) }}</div>
                    <img class="product-img" :src="item.productPic" />
                    <div class="pic-border" :data-desc="item.desc"></div>
                </div>
                <div class="onlyPromotion__col-item--info">
                    <div v-if="$props.formData.showInfo.includes(1) && item.productName" class="onlyPromotion__col-item--title">
                        {{ item.productName }}
                    </div>
                    <div class="onlyPromotion__col-item--members">
                        <div
                            v-for="member in item.joinMember.filter((m) => m.include)"
                            :key="member.id"
                            class="tag"
                            :style="member.labelJson && { color: member.labelJson?.fontColor, borderColor: member.labelJson?.labelColor }"
                        >
                            {{ member.name }}
                        </div>
                    </div>
                    <div v-if="$props.formData.showInfo.includes(3)" class="onlyPromotion__row-item--price">
                        <div class="price-tag">专享价</div>
                        <good-price :price="divTenThousand(item.minPrice).toString()" :unit-size="12" :integer-size="20" :decimal-size="12" />
                        <div v-if="$props.formData.showInfo.includes(4)" class="origin-price">¥{{ divTenThousand(item.actualPaidPrice) }}</div>
                    </div>

                    <!-- 抢购进度组件 -->
                    <PurchaseProgress
                        v-if="$props.formData.showInfo.includes(2) && item.stock !== undefined"
                        :total-stock="item.stock"
                        :sold-count="formatSold(item.stock, 1)"
                        :show-label="$props.formData.showBtn !== 1"
                        :show-btn="$props.formData.showBtn === 1"
                        :show-stock="false"
                        :disable="!isStart(item.startTime)"
                        style="width: 158px; margin-left: -8px"
                    />
                </div>
            </div>
        </div>
        <!-- 两列展示 end -->
    </div>
</template>

<style lang="scss" scoped>
@include b(onlyPromotion) {
    box-sizing: border-box;
    height: 100px;
    width: 100%;
    background: #f7f8fa;
    @include e(title) {
        @include flex(space-between);
        align-items: center;
        padding: 0 15px;
        width: 100%;
        height: 40px;
        margin-bottom: 8px;
        background: #fff;

        @include m(left) {
            @include flex(flex-start);
            color: #333;
            font-size: 16px;
            img {
                width: 240px;
                height: 40px;
                object-fit: cover;
            }
        }
        @include m(circle) {
            display: inline-block;
            width: 20px;
            height: 20px;
            text-align: center;
            line-height: 20px;
            background: #000;
            border-radius: 50%;
            color: #fff;
        }
        @include m(m) {
            margin: 0 10px;
        }
        @include m(more) {
            color: #838383;
            display: flex;
            align-items: center;
            font-size: 14px;
            cursor: pointer;
            img {
                width: 16px;
                height: 16px;
                margin-left: 4px;
            }
        }
    }

    @include e(member-type-list) {
        width: 100%;
        scrollbar-width: none;
        .box {
            display: flex;
            gap: 4px;
            padding: 4px 15px;
            line-height: 22px;
            flex-wrap: nowrap;
            align-items: center;
            overflow-x: scroll;
            scrollbar-width: none;
        }
        .member-type {
            white-space: nowrap;
            padding: 4px 20px;
            border-radius: 100px;
            background: #f0f2f5;
            color: #303133;
            font-size: 12px;
            width: auto;
            text-align: center;
            /* bg-color */
            background: rgb(255, 255, 255);
        }
    }
    .tag {
        font-size: 11px;
        line-height: 140%;
        height: 16px;
        display: inline-block;
        padding: 0 4px;
        border-radius: 2px;
        border: 1px solid #4589f9;
    }

    // 单列展示
    @include e(row) {
        padding: 0 8px 8px;

        @include e(row-item) {
            display: flex;
            height: 130px;
            padding: 8px;
            margin-bottom: 8px;
            background: #fff;
            border-radius: 8px;

            @include m(img) {
                width: 114px;
                height: 114px;
                position: relative;
                border-radius: 8px;
                margin-right: 8px;
                overflow: hidden;

                .end-corner {
                    position: absolute;
                    /* 辅助信息&小标签9/Regular */
                    color: #fff;
                    font-family: 苹方-简;
                    font-size: 9px;
                    font-weight: 400;
                    line-height: 140%;
                    letter-spacing: 0%;
                    text-align: center;
                    border-radius: 8px 8px 8px 0px;
                    padding: 1.5px 4px;

                    /* 功能色/错误/常规 */
                    background: rgb(245, 63, 63);
                    z-index: 4;
                }

                .product-img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    z-index: 1;
                }

                .pic-border {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: url('@/assets/image/decoration/only-promotion-outside.png');
                    background-repeat: no-repeat;
                    background-size: 100% 100%;
                    z-index: 3;

                    &::after {
                        content: attr(data-desc);
                        position: absolute;
                        bottom: 0;
                        right: 0;
                        font-size: 9px;
                        width: 60px;
                        height: 14px;
                        font-weight: bold;
                        text-align: center;
                        color: rgb(53, 101, 2);
                    }
                }
            }

            @include m(info) {
                flex: 1;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
            }

            @include m(title) {
                @include utils-ellipsis(2);
                min-height: 17px;
                max-height: 34px;
                color: rgba(0, 0, 0, 0.7);
                font-family: 苹方-简;
                font-size: 12px;
                font-weight: 400;
                line-height: 140%;
                letter-spacing: -1%;
                text-align: left;
            }

            @include m(price) {
                display: flex;
                align-items: baseline;

                .price-tag {
                    border-radius: 4px 4px 0px 4px;
                    padding: 1.5px 4px;
                    margin-right: 4px;
                    align-self: center;

                    /* 中性色/文字/强调 */
                    background: rgb(29, 33, 41);
                    color: rgb(255, 246, 227);
                    font-family: 苹方-简;
                    font-size: 10px;
                    font-weight: 400;
                    line-height: 140%;
                    letter-spacing: 0%;
                    text-align: center;
                }

                .origin-price {
                    margin-left: 2px;
                    font-size: 11px;
                    color: #c9cdd4;
                    text-decoration: line-through;
                }
            }

            @include m(members) {
                width: 100%;
                display: flex;
                flex-wrap: wrap;
                align-items: center;
                flex-direction: row;
                color: #4589f9;
                gap: 4px 8px;
                max-height: 80px;
            }
        }
    }

    // 两列展示 纵向
    @include e(col) {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
        padding: 0 12px;

        @include e(col-item) {
            display: inline-block;
            background: #fff;
            border-radius: 8px;
            padding: 8px;
            width: 174px;

            @include m(img) {
                width: 158px;
                height: 158px;
                position: relative;
                border-radius: 8px;
                overflow: hidden;

                .end-corner {
                    position: absolute;
                    /* 辅助信息&小标签9/Regular */
                    color: #fff;
                    font-family: 苹方-简;
                    font-size: 9px;
                    font-weight: 400;
                    line-height: 140%;
                    letter-spacing: 0%;
                    text-align: center;
                    border-radius: 8px 8px 8px 0px;
                    padding: 1.5px 4px;

                    /* 功能色/错误/常规 */
                    background: rgb(245, 63, 63);
                }

                .end-corner {
                    position: absolute;
                    /* 辅助信息&小标签9/Regular */
                    color: #fff;
                    font-family: 苹方-简;
                    font-size: 9px;
                    font-weight: 400;
                    line-height: 140%;
                    letter-spacing: 0%;
                    text-align: center;
                    border-radius: 8px 8px 8px 0px;
                    padding: 1.5px 4px;

                    /* 功能色/错误/常规 */
                    background: rgb(245, 63, 63);
                    z-index: 4;
                }

                .product-img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    z-index: 1;
                }

                .pic-border {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: url('@/assets/image/decoration/only-promotion-outside.png');
                    background-repeat: no-repeat;
                    background-size: 100% 100%;
                    z-index: 3;

                    &::after {
                        content: attr(data-desc);
                        position: absolute;
                        bottom: 2px;
                        right: 6px;
                        font-size: 10px;
                        width: 50px;
                        height: 18px;
                        line-height: 18px;
                        font-weight: bold;
                        color: rgb(53, 101, 2);
                    }
                }
            }

            @include m(info) {
                padding: 8px;
            }

            @include m(title) {
                @include utils-ellipsis(1);
                font-size: 14px;
                height: 16px;
                line-height: 16px;
                color: #333;
                margin-bottom: 4px;
            }

            @include m(price) {
                display: flex;
                align-items: baseline;
                margin-bottom: 8px;

                .price-tag {
                    border-radius: 4px 4px 0px 4px;
                    padding: 1.5px 4px;
                    margin-right: 4px;
                    align-self: center;

                    /* 中性色/文字/强调 */
                    background: rgb(29, 33, 41);
                    color: rgb(255, 246, 227);
                    font-family: 苹方-简;
                    font-size: 10px;
                    font-weight: 400;
                    line-height: 140%;
                    letter-spacing: 0%;
                    text-align: center;
                }

                .origin-price {
                    margin-left: 8px;
                    font-size: 12px;
                    color: #999;
                    text-decoration: line-through;
                }
            }

            @include m(members) {
                width: 100%;
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                flex-direction: row;
                color: #4589f9;
                gap: 4px 8px;
            }
        }
    }
}
@include b(onlyPromotion-row) {
    @include e(corner) {
        @include m(new) {
            width: 32px;
            height: 16px;
            position: absolute;
            top: 4px;
            left: 0;
        }
        @include m(hot) {
            width: 32px;
            height: 35px;
            position: absolute;
            left: 0;
            top: 0;
        }
        @include m(purchase) {
            width: 34px;
            height: 20px;
            position: absolute;
            left: 6px;
            top: 6px;
        }
    }
}
@include b(onlyPromotion-col) {
    @include e(corner) {
        @include m(purchase) {
            width: 34px;
            height: 20px;
            position: absolute;
            left: 6px;
            top: 6px;
        }
        @include m(hot) {
            width: 32px;
            height: 35px;
            position: absolute;
            left: 0;
            top: 0;
        }
        @include m(new) {
            width: 32px;
            height: 16px;
            position: absolute;
            top: 4px;
            left: 0;
        }
    }
}
.good-one {
    background: #fff;
}
.good-two {
    background: #f8f8f8;
    & .onlyPromotion__row-item,
    .onlyPromotion__col-item {
        box-shadow: 0px 1px 56px 6px rgb(109 109 109 / 10%);
    }
}
.good-three {
    background: #fff;
    & .onlyPromotion__row-item,
    .onlyPromotion__col-item {
        border: 1px solid #eee;
    }
}

// 移除固定高度限制，让内容自适应
.onlyPromotion {
    height: auto !important;
}
</style>
