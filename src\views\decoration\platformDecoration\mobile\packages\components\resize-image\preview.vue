<!--
 * @description: 图片展示组件
 * @Author: lexy
 * @Date: 2022-08-16 17:56:22
 * @LastEditors: lexy 
 * @LastEditTime: 2024-01-25 14:25:43
-->
<script setup lang="ts">
import type { PropType } from 'vue'
import { useVModel } from '@vueuse/core'
import defaultResizeImage from './resize-image'
/*
 *variable
 */
const $props = defineProps({
    formData: {
        type: Object as PropType<typeof defaultResizeImage>,
        default() {
            return defaultResizeImage
        },
    },
})
const $emit = defineEmits(['update:formData'])
const upLoadImg = useVModel($props, 'formData', $emit)
// 初始化图片位置
const intTop = ref(0)
const intLeft = ref(0)
const moveType = ref(false)
// 移动后图片位置
const vw = ref(0)
const vh = ref(0)
const top = ref(0)
const left = ref(0)
const showType = ref(false)
watch(
    $props.formData,
    (newval) => {
        if (upLoadImg.value.img && newval) {
            showType.value = true
            intTop.value = Number(upLoadImg.value.top)
            intLeft.value = Number(upLoadImg.value.left)
            vw.value = Number(upLoadImg.value.width.match(/(\S*)px/)[1])
            vh.value = Number(upLoadImg.value.height.match(/(\S*)px/)[1])
        }
    },
    { immediate: true },
)
/*
 *lifeCircle
 */
/*
 *function
 */
const resize = (newRect) => {
    moveType.value = true
    // intTop.value = 0
    // intLeft.value = 0
    top.value = newRect.y
    left.value = newRect.x
    upLoadImg.value.top = newRect.y
    upLoadImg.value.left = newRect.x
    if (newRect.w && newRect.h) {
        upLoadImg.value.width = newRect.w + 'px'
        upLoadImg.value.height = newRect.h + 'px'
        vw.value = newRect.w
        vh.value = newRect.h
    }
}
</script>

<template>
    <div style="margin-top: 5px" :style="{ height: `${upLoadImg.boxHeight}px` }">
        <VueDragResize :w="vw" :h="vh" :x="intLeft" :y="intTop" :parent="true" @resizing="resize" @dragging="resize">
            <div class="box" :style="{ width: +vw + 'px', height: +vh + 'px' }">
                <img
                    v-if="showType"
                    :src="upLoadImg.img || 'https://devoss.chongyoulingxi.com/system-front/image_error.jpeg'"
                    :style="{
                        width: vw + 'px',
                        height: vh + 'px',
                    }"
                />
            </div>
        </VueDragResize>
    </div>
</template>

<style lang="scss" scoped></style>
