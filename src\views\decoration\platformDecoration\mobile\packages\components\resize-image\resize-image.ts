import type { LinkSelectItem } from '@/components/link-select/linkSelectItem'
export default {
    ImageCom: '图片',
    img: '',
    width: '240px',
    height: '100px',
    boxHeight: 240,
    boxWidth: 100,
    top: 0,
    left: 0,
    link: {
        id: '',
        type: '',
        name: '',
        url: '',
    },
}
export interface imageItem {
    title: string
    img: string
    link: LinkSelectItem
    linkName: string
}
