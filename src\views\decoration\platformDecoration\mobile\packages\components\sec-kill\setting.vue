<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-03-01 16:15:50
 * @LastEditors: lexy
 * @LastEditTime: 2023-09-21 14:52:38
-->
<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-12-05 10:33:57
 * @LastEditors: lexy
 * @LastEditTime: 2023-09-12 11:04:10
-->
<script setup lang="ts">
import { useVModel } from '@vueuse/core'
import DecorationSeckillSelectGood from '@/q-plugin/secondsKill/DecorationSeckillSelectGood.vue'
import { ElMessageBox } from 'element-plus'
import defaultSeckillData from './sec-kill'
import DateUtil from '@/utils/date'
import type { PropType } from 'vue'
/*
 *variable
 */
const $props = defineProps({
    formData: {
        type: Object as PropType<typeof defaultSeckillData>,
        default: defaultSeckillData,
    },
})
const $emit = defineEmits(['update:formData'])
const subForm = useVModel($props, 'formData', $emit)
const showDialog = ref(false)
const selectGoodRef = ref()
const $DateUtil = new DateUtil()
const selectGoodRefComputedProps = computed(() => selectGoodRef.value.qPlugins.currentComponentRef)
/*
 *lifeCircle
 */
/*
 *function
 */
const handleCancel = () => {
    ElMessageBox.confirm('确定要退出选择商品页面? 退出后，未保存的信息将不会保留!', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(() => {
        showDialog.value = false
        selectGoodRefComputedProps.value.search = {
            current: 1,
            total: 0,
            choosedSession: '',
        }
    })
}
const handleAddGoods = () => {
    showDialog.value = true
}
const handleConfirm = () => {
    showDialog.value = false
    subForm.value.goodList = selectGoodRefComputedProps.value.tempGoods
    subForm.value.seckillTime = selectGoodRefComputedProps.value.search.choosedSession
    subForm.value.seckillEndTime = $DateUtil.getYMDHMSs(
        new Date(new Date(selectGoodRefComputedProps.value.search.choosedSession).getTime() + 60 * 60 * 1000),
    )
}
</script>

<template>
    <div>
        <el-form :model="subForm">
            <el-form-item label="选择商品">
                <div class="choosedGood">
                    <el-image
                        v-for="item in subForm.goodList"
                        :key="item.productId"
                        style="width: 72px; height: 72px; margin-right: 10px; margin-bottom: 10px; border-radius: 10px"
                        :src="item.productPic"
                        fit="cover"
                    />
                    <div class="choosedGood__item" @click="handleAddGoods">+</div>
                </div>
            </el-form-item>
            <el-form-item label="列表样式">
                <el-radio-group v-model="subForm.display">
                    <el-radio :value="1">横向滚动</el-radio>
                    <el-radio :value="2">详细列表</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="组件内边距">
                <el-slider v-model="subForm.padding" :show-tooltip="false" :show-input="true" :max="30"></el-slider>
            </el-form-item>
            <el-form-item label="商品间距">
                <el-slider v-model="subForm.marginBottom" :show-tooltip="false" :show-input="true" :max="30"></el-slider>
            </el-form-item>
            <el-form-item label="商品样式">
                <el-radio-group v-model="subForm.goodStyle">
                    <el-radio :value="1">无边白底</el-radio>
                    <el-radio :value="2">卡片投影</el-radio>
                    <el-radio :value="3">描边白底</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="图片倒角">
                <el-radio-group v-model="subForm.border">
                    <el-radio :value="false">直角</el-radio>
                    <el-radio :value="true">圆角</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item v-if="subForm.display === 2" label="文本样式">
                <el-radio-group v-model="subForm.textStyle">
                    <el-radio :value="1">常规</el-radio>
                    <el-radio :value="2">加粗</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="显示内容">
                <el-checkbox v-model="subForm.showtag" :true-label="true" :false-label="false">显示内容</el-checkbox>
            </el-form-item>
            <el-form-item v-if="subForm.showtag" label="角标样式">
                <el-radio-group v-model="subForm.tagStyle">
                    <el-radio :value="1">新品</el-radio>
                    <el-radio :value="2">热卖</el-radio>
                    <el-radio :value="3">抢购</el-radio>
                </el-radio-group>
            </el-form-item>
        </el-form>
        <el-dialog v-model="showDialog" append-to-body :before-close="handleCancel">
            <decoration-seckill-select-good ref="selectGoodRef" :point-goods-list="subForm.goodList" :goods-visible="showDialog" />
            <template #footer>
                <el-button @click="handleCancel">取消</el-button>
                <el-button type="primary" @click="handleConfirm">确定</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<style lang="scss" scoped>
@include b(choosedGood) {
    width: 285px;
    padding: 18px;
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    border: 1px dashed #f2f2f2;
    @include e(item) {
        width: 72px;
        height: 72px;
        border: 1px solid #d5d5d5;
        margin-right: 10px;
        line-height: 70px;
        text-align: center;
        font-size: 20px;
        color: #000;
        cursor: pointer;
    }
}
</style>
