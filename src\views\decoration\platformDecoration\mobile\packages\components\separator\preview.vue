<!--
 * @description: 分隔符组件
 * @Author: lexy
 * @Date: 2022-08-13 17:15:14
 * @LastEditors: lexy
 * @LastEditTime: 2022-11-04 14:06:44
-->
<script setup lang="ts">
import type { PropType } from 'vue'
import defaultSeparatorData from './separator'
/*
 *variable
 */
const $props = defineProps({
    formData: {
        type: Object as PropType<typeof defaultSeparatorData>,
        default() {
            return defaultSeparatorData
        },
    },
})
const inlineBox = computed(() => {
    const { hasMargin } = $props.formData
    return `box-sizing: border-box; padding: 10px ${hasMargin ? 15 : 0}px`
})
const inlineStyle = computed(() => {
    const { borderColor, borderStyle } = $props.formData
    return `border-bottom:1px ${borderStyle} ${borderColor};`
})
/*
 *lifeCircle
 */
/*
 *function
 */
</script>

<template>
    <div :style="inlineBox">
        <div :style="inlineStyle"></div>
    </div>
</template>

<style lang="scss" scoped></style>
