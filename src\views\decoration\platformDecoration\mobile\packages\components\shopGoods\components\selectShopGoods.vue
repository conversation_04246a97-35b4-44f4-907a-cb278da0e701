<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-05-09 15:38:45
 * @LastEditors: lexy
 * @LastEditTime: 2024-04-16 15:38:43
-->
<template>
    <el-dialog
        v-model="dialogVisible"
        :title="isSelectGoods ? '选择商品' : '选择店铺'"
        append-to-body
        center
        destroy-on-close
        @open="openDialog()"
        @close="emit('cancel')"
    >
        <el-form :inline="true" :model="shopSearchParams" class="demo-form-inline">
            <el-form-item :label="isSelectGoods ? '商品名称' : '店铺ID'">
                <el-input v-if="isSelectGoods" v-model="goodsSearchParams.keyword" placeholder="请填写商品名称" />
                <el-input v-else v-model="shopSearchParams.no" placeholder="请填写店铺ID" />
            </el-form-item>
            <el-form-item v-if="!isSelectGoods" label="店铺名称">
                <el-input v-model="shopSearchParams.name" placeholder="请填写店铺名称" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="handleSearch">搜索</el-button>
            </el-form-item>
        </el-form>
        <el-table :data="tableData" highlight-current-row style="width: 100%; height: 300px" @current-change="selectRow">
            <el-table-column property="logo" :label="`${isSelectGoods ? '商品' : '店铺'}名称`">
                <template #default="{ row }">
                    <div style="display: flex; align-items: center">
                        <el-avatar style="vertical-align: middle; margin: 0 10px 0 0" :size="40" :src="isSelectGoods ? row.pic : row.logo" />
                        <div>
                            <template v-if="isSelectGoods">
                                <QTooltip :content="row.productName" width="150px" />
                                <div v-if="isSelectGoods" style="color: rgba(245, 7, 7, 1)">
                                    ￥{{ row.salePrices[0] / 10000 }}
                                    {{
                                        row.salePrices[0] === row.salePrices[row.salePrices.length - 1]
                                            ? ''
                                            : '-' + row.salePrices[row.salePrices.length - 1] / 10000
                                    }}
                                </div>
                            </template>
                            <template v-else>
                                <QTooltip :content="isSelectGoods ? row.productName : row.name" width="150px" />
                            </template>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column v-if="isSelectGoods" property="name" label="总库存">
                <template #default="{ row }"> {{ countPrice(row.stocks) }} </template>
            </el-table-column>
            <el-table-column label="是否选中" width="80px">
                <template #default="{ row }">
                    <el-icon v-if="row.id === currentRow?.id"><Select /></el-icon>
                </template>
            </el-table-column>
        </el-table>
        <page-manage
            :page-size="pageConfig.size"
            :page-num="pageConfig.current"
            :total="pageConfig.total"
            @handle-size-change="handleSizeChange"
            @handle-current-change="handleCurrentChange"
        />
        <div class="footer">
            <el-button @click="emit('cancel')">取消</el-button>
            <el-button type="primary" @click="emit('confirm', currentRow)">确定</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { doGetShopList } from '@/apis/shops'
import { ElMessage } from 'element-plus'
import { defineEmits, defineProps } from 'vue'
import { doGetRetrieveProduct } from '@/apis/good'
import { Select } from '@element-plus/icons-vue'
import QTooltip from '@/components/q-tooltip/q-tooltip.vue'
import { useVModel } from '@vueuse/core'
const emit = defineEmits(['update:visible', 'cancel', 'confirm'])

const props = defineProps({
    visible: {
        type: Boolean,
        default: false,
    },
    isSelectGoods: {
        type: Boolean,
        default: false,
    },
    shopId: {
        type: String,
        default: '',
    },
})
// 控制弹框变量
const dialogVisible = useVModel(props, 'visible', emit)

// 当前选中行
const currentRow = ref()
// 选择当前行
const selectRow = (val: any) => {
    currentRow.value = val
}
const tableData = ref<any>([])

interface searchParamType {
    no: string
    name: string
}

const countPrice = (arr: string[]) => {
    return arr.reduce((pre, item) => {
        return (pre += parseInt(item))
    }, 0)
}

const openDialog = () => {
    pageConfig.current = 1
    pageConfig.size = 20
    pageConfig.total = 0

    if (props.isSelectGoods) {
        console.log('props.shopId', props.shopId)
        console.log('props.isSelectGoods', props.isSelectGoods)

        if (props.shopId) {
            initGoodsList(goodsSearchParams.value)
        } else {
            ElMessage.warning('请重新选择店铺!')
        }
    } else {
        currentRow.value = { id: props.shopId || '' }
        initShopList(shopSearchParams.value)
    }
}

// 分页数据
const pageConfig = reactive({
    size: 20,
    current: 1,
    total: 0,
})
// 更改条数
const handleSizeChange = (val: number) => {
    pageConfig.current = 1
    pageConfig.size = val
    if (props.isSelectGoods) {
        initGoodsList(goodsSearchParams.value)
    } else {
        initShopList(shopSearchParams.value)
    }
}
// 更改当前页
const handleCurrentChange = (val: number) => {
    pageConfig.current = val
    if (props.isSelectGoods) {
        initGoodsList(goodsSearchParams.value)
    } else {
        initShopList(shopSearchParams.value)
    }
}
// 搜索
const handleSearch = () => {
    if (props.isSelectGoods) {
        initGoodsList(goodsSearchParams.value)
    } else {
        initShopList(shopSearchParams.value)
    }
}

// 店铺搜索框数据
const shopSearchParams = ref<searchParamType>({
    no: '',
    name: '',
})
// 获取店铺列表
async function initShopList(param: searchParamType) {
    const params = Object.assign(param, pageConfig)
    const { data } = await doGetShopList(params)
    tableData.value = data.records
    pageConfig.current = data.current
    pageConfig.total = data.total
    pageConfig.size = data.size
}

// 商品搜索框数据
const goodsSearchParams = ref({
    keyword: '',
    shopId: props.shopId,
})

// 获取商品列表
const initGoodsList = async (param: any) => {
    const params = Object.assign(param, pageConfig)

    const { data, code } = await doGetRetrieveProduct({ ...params, searchTotalStockGtZero: true })
    if (code !== 200) {
        return ElMessage.error('获取商品失败')
    }
    tableData.value = data.list
    pageConfig.current = data.pageNum
    pageConfig.total = data.total
    pageConfig.size = data.size
}
</script>

<style scoped lang="scss">
.footer {
    width: 180px;
    margin: 20px auto 0;
    display: flex;
    justify-content: space-between;
}
</style>
