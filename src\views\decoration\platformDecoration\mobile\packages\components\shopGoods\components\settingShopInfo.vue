<template>
    <div class="shop-info">
        <el-card class="card">
            <!-- 选择店铺 -->
            <el-form :model="shopItem">
                <el-form-item label="店铺" label-width="70px">
                    <div class="selectLine">
                        <span v-show="shopItem.shop?.id" class="selectLine__name">{{ shopItem.shop?.name }}</span>
                        <el-button v-show="shopItem.shop?.id" text :icon="Close" @click="deleteSelectedShop" />
                        <el-button type="primary" @click="openSelectOpen('店铺', 0)">{{ shopItem.shop?.id ? '更换' : '选择店铺' }}</el-button>
                    </div>
                </el-form-item>
                <!-- 选择商品 -->
                <el-form-item label="店铺商品" label-width="70px">
                    <div style="width: 100%">
                        <div v-for="(item, index) in shopItem.goods" :key="item.id + index" class="selectLine" style="margin-bottom: 15px">
                            <span v-show="item?.id" class="selectLine__name">{{ item?.name }}</span>
                            <el-button v-show="item?.id" text :icon="Close" @click="deleteShopGood(index)" />
                            <el-button type="primary" :disabled="!shopItem.shop?.id" @click="openSelectOpen('商品', index)">
                                {{ item?.id ? '更换' : '添加商品' }}
                            </el-button>
                        </div>
                    </div>
                </el-form-item>
            </el-form>
        </el-card>
        <!-- 删除按钮 -->
        <div v-if="shopIndex > 0" style="cursor: pointer" @click="emit('deleteShopItem', shopIndex)">
            <el-icon color="red"><Delete /></el-icon>
        </div>
        <!-- 弹框 -->
        <select-shop-goods
            v-model="dialogVisible"
            :is-select-goods="isSelectGoods"
            :shop-id="shopItem?.shop?.id"
            @cancel="dialogVisible = false"
            @confirm="confirm"
        />
    </div>
</template>

<script setup lang="ts">
import shopGoods, { ShopItemType } from '../shopGoods'
import { ref, watch } from 'vue'
import type { PropType } from 'vue'
import SelectShopGoods from './selectShopGoods.vue'
import { Close, Delete } from '@element-plus/icons-vue'
import goods from '../../goods/goods'
const emit = defineEmits(['deleteShopItem', 'updateShopItem'])

const props = defineProps({
    shopItem: {
        type: Object as PropType<ShopItemType>,
        default: () => ({ shop: {}, goods: shopGoods.shopInfo[0].goods }),
    },
    shopIndex: {
        type: Number,
        default: 0,
    },
})

// 控制弹框变量
const dialogVisible = ref(false)
// 判断是否是选择商品
const isSelectGoods = ref(true)
// 第几个button在选择商品
const currentIndex = ref(0)
// 打开弹框
const openSelectOpen = (value: string, index: number) => {
    isSelectGoods.value = value === '商品'
    dialogVisible.value = true
    // 只有选择商品时需要
    if (value === '商品') currentIndex.value = index
}

// 点击确定更改父组件中的数据
const confirm = (row: any) => {
    if (!row) return
    const { name, id, logo, productName, pic, salePrices, productId } = row
    // 更改的值
    let shopItem
    if (isSelectGoods.value) {
        const changeValue = props.shopItem.goods?.map((item, index) => {
            if (index === currentIndex.value) return { name: productName, onlyId: id, id: productId, logo: pic, price: salePrices[0] / 10000 }
            return item
        })
        shopItem = { ...props.shopItem, goods: changeValue }
    } else {
        shopItem = { goods: shopGoods.shopInfo[0].goods, shop: { name, id, logo } }
    }
    emit('updateShopItem', { shopItem, index: props.shopIndex })
    dialogVisible.value = false
}
const deleteSelectedShop = () => {
    emit('updateShopItem', { shopItem: { goods: shopGoods.shopInfo[0].goods, shop: {} }, index: props.shopIndex })
}
const deleteShopGood = (index: number) => {
    let shopItem = { ...props.shopItem, goods: props.shopItem.goods.toSpliced(index, 1, shopGoods.shopInfo[0].goods[0]) }
    emit('updateShopItem', { shopItem, index: props.shopIndex })
}
</script>

<style scoped lang="scss">
@include b(shop-info) {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
}
@include b(card) {
    width: 320px;
}

@include b(selectLine) {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    @include e(name) {
        width: 140px;
        text-align: left;
        @include utils-ellipsis(1);
    }
}

.el-card {
    &:deep(.el-card__body) {
        padding-bottom: 0;
    }
}
</style>
