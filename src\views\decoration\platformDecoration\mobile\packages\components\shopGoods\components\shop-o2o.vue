<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-05-08 15:24:12
 * @LastEditors: lexy
 * @LastEditTime: 2023-06-19 19:02:58
-->
<template>
    <div class="shop">
        <div class="shop__title">
            <div class="shop__title-image">
                <el-empty description="店铺" style="width: 100%; height: 100%" />
            </div>

            <view style="margin-left: 8px; flex: 1">
                <view style="margin-bottom: 5px; display: flex; align-items: center">
                    <text class="shop__title--name"> 店铺名称 </text>
                </view>
                <view style="display: flex">
                    <text>月销 2000</text>
                    <text v-if="positioningStyle" style="margin: 0 13px">起送￥20</text>
                    <text v-if="positioningStyle">距离1.2㎞</text>
                </view>
            </view>
        </div>
        <div style="border: 1px dashed #e7e7e7; margin: 8px -5px"></div>
        <van-swipe ref="vanSwipeRef">
            <van-swipe-item class="scroll-view_H">
                <goods v-for="item in 3" :key="item" :goods-item="shopItem" />
            </van-swipe-item>
            <van-swipe-item class="scroll-view_H">
                <goods v-for="item in 3" :key="item" :goods-item="shopItem" />
            </van-swipe-item>
            <template #indicator>
                <div></div>
            </template>
        </van-swipe>
        <div class="active">
            <div v-for="(item, index) in 2" :key="index" class="point" :class="{ current: index === current }" @click="changeSwipe(index)" />
            <div class="shop-goods--left-point" @click="move(-1, 0)"></div>
            <div class="shop-goods--right-point" @click="move(1, 1)"></div>
        </div>
    </div>
</template>

<script setup lang="ts">
import goods from './goods.vue'

defineProps({
    positioningStyle: {
        type: Boolean,
        default: false,
    },
})

const shopItem = {
    name: '商品名称',
    price: '88',
}

// 轮播图实例
const vanSwipeRef = ref()
// 当前位置
const current = ref(0)
// 切换swipe
const changeSwipe = (e: number) => {
    vanSwipeRef.value.swipeTo(e)
    current.value = e
}
const move = (step: number, border: number) => {
    current.value += current.value === border ? 0 : step
    vanSwipeRef.value.swipeTo(current.value)
}
</script>

<style scoped lang="scss">
$color-active-red: #fa3534;
$color-unactive-red: #ff9e9e;
@include b(shop) {
    width: 362px;
    padding: 6px;
    font-size: 14px;
    background-color: rgba(255, 255, 255, 1);
    border: 1px solid rgba(187, 187, 187, 1);
    border-radius: 8px;
    margin-bottom: 10px;
    @include e(title) {
        height: 60px;
        display: flex;
        @include m(name) {
            color: rgba(16, 16, 16, 1);
            font-size: 20px;
            margin-bottom: 8px;
            width: 270px;
            @include utils-ellipsis(1);
        }
    }
    @include e(title-image) {
        width: 60px;
        height: 60px;
        border: 1px solid#aaa;
        border-radius: 8px;
        overflow: hidden;
    }
    @include e(main) {
        margin-top: 8px;
        display: flex;
        justify-content: space-between;
    }
}

@include b(scroll-view_H) {
    display: flex;
    white-space: nowrap;
    margin-top: 12rpx;
    justify-content: space-around;
}

@include b(active) {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 36px;
    margin: 0 -6px;
}
.point {
    border: 3px solid $color-active-red;
    opacity: 0.28;
    width: 2px;
    height: 1px;
    margin-left: 10px;
    cursor: pointer;
    border-radius: 50%;
}
.current {
    opacity: 1;
    width: 2px;
}

.shop-goods--left-point {
    position: absolute;
    left: 5%;
    width: 20px;
    height: 20px;
    border: 1px solid $color-active-red;
    border-radius: 50%;
    &::before {
        content: '<';
        color: $color-active-red;
        line-height: 20px;
        display: block;
        text-align: center;
        padding-right: 2px;
    }
    &:hover {
        box-shadow: $color-active-red 0px 0px 0px 1px;
        transform: scale(1.1);
    }
}
.shop-goods--right-point {
    position: absolute;
    right: 5%;
    width: 20px;
    height: 20px;
    border: 1px solid $color-active-red;
    border-radius: 50%;
    &::before {
        content: '>';
        color: $color-active-red;
        line-height: 20px;
        display: block;
        text-align: center;
        padding-left: 2px;
    }
    &:hover {
        box-shadow: $color-active-red 0px 0px 0px 1px;
        transform: scale(1.1);
    }
}
</style>
