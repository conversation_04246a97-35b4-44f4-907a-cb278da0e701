<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-05-08 15:24:12
 * @LastEditors: lexy
 * @LastEditTime: 2023-06-19 17:01:04
-->
<template>
    <div class="shop" :class="{ only }">
        <div class="shop__title">
            <div class="shop__title-image">
                <img :src="shopItem.shop.logo" style="width: 100%; height: 100%" />
            </div>

            <div style="margin-left: 15px; flex: 1">
                <div class="shop__title--name">{{ shopItem.shop.name }}</div>
                <div style="display: flex">
                    <div>
                        <el-button plain size="small">
                            <van-icon name="star-o" color="#999" size="15" top="-3" />
                            <span style="margin-left: 8px">收藏店铺</span>
                        </el-button>
                    </div>
                    <div style="margin-left: 25px">
                        <el-button plain size="small">
                            <QIcon name="icon-dianpu" color="#000" size="15px" />
                            <span style="margin-left: 8px">进入店铺</span>
                        </el-button>
                    </div>
                </div>
            </div>
        </div>
        <div class="shop__main">
            <goods v-for="item in shopItem.goods" :key="item.id" :goods-item="item" />
        </div>
    </div>
</template>

<script setup lang="ts">
import goods from './goods.vue'

import shopGoods from '../shopGoods'
import { PropType } from 'vue'
defineProps({
    only: {
        type: Boolean,
        default: false,
    },
    shopItem: {
        type: Object as PropType<typeof shopGoods.shopInfo[0]>,
        default: shopGoods.shopInfo[0],
    },
})
</script>

<style scoped lang="scss">
@include b(shop) {
    width: 362px;
    padding: 6px;
    height: 252px;
    font-size: 14px;
    background-color: rgba(255, 255, 255, 1);
    border: 1px solid rgba(187, 187, 187, 1);
    border-radius: 8px;
    margin-bottom: 10px;
    @include e(title) {
        height: 60px;
        display: flex;
        @include m(name) {
            color: rgba(16, 16, 16, 1);
            font-size: 20px;
            margin-bottom: 8px;
            width: 270px;
            @include utils-ellipsis(1);
        }
    }
    @include e(title-image) {
        width: 60px;
        height: 60px;
        border: 1px solid#aaa;
        border-radius: 8px;
        overflow: hidden;
    }
    @include e(main) {
        margin-top: 8px;
        display: flex;
        justify-content: space-between;
    }
}
@include b(scroll-view_H) {
    white-space: nowrap;
    margin-top: 12rpx;
    overflow-x: auto;
}
.shop.only {
    border-bottom: 1px dotted rgba(187, 187, 187, 1);
    border-radius: 8px 8px 0 0;
    margin-bottom: 0px;
}
</style>
