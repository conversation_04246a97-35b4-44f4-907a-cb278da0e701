<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-05-08 15:18:43
 * @LastEditors: lexy
 * @LastEditTime: 2024-04-18 10:27:27
-->
<template>
    <div class="title">
        {{ formData.title }}
    </div>
    <div v-if="formData.showStyle === 'is-style-one'" class="shop-goods">
        <van-swipe ref="vanSwipeRef">
            <van-swipe-item v-for="item in formData.shopInfo" :key="item.shop.id">
                <shop only :shop-item="item" />
            </van-swipe-item>
            <template #indicator>
                <div></div>
            </template>
        </van-swipe>
        <div class="shop-goods__active">
            <div
                v-for="(item, index) in formData.shopInfo"
                :key="item.shop?.id"
                class="point"
                :class="{ current: index === current }"
                @click="changeSwipe(index)"
            />
        </div>
        <div class="shop-goods--left-point" @click="move(-1, 0)"></div>
        <div class="shop-goods--right-point" @click="move(1, formData.shopInfo.length - 1)"></div>
    </div>
    <div v-else-if="formData.showStyle === 'is-style-two'" class="shop-goods">
        <img :src="formData.shopBigImg" style="width: 100%; height: 350px; margin-bottom: 20px" />
        <shop v-for="item in formData.shopInfo" :key="item.shop?.id" :shop-item="item" />
    </div>
    <div v-else style="padding: 5px">
        <shop-o2o :positioning-style="formData.showStyle === 'is-style-four'" />
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import shop from './components/shop.vue'
import shopGoods from './shopGoods'
import shopO2o from './components/shop-o2o.vue'
import { doGetRetrieveProduct, doPostShopInfo } from '@/apis/good'
import { ElMessage } from 'element-plus'
import type { PropType } from 'vue'
const props = defineProps({
    formData: {
        type: Object as PropType<typeof shopGoods>,
        default() {
            return shopGoods
        },
    },
})
// 轮播图实例
const vanSwipeRef = ref()
// 当前位置
const current = ref(0)
// 切换swipe
const changeSwipe = (e: number) => {
    vanSwipeRef.value.swipeTo(e)
    current.value = e
}
const move = (step: number, border: number) => {
    current.value += current.value === border ? 0 : step
    vanSwipeRef.value.swipeTo(current.value)
}

watch(
    () => props.formData.shopInfo,
    () => {
        shops.value = props.formData.shopInfo
    },
)

/**
 * @: 获取商品 并组合
 */
const getGoods = async (goodsShop: { [key: string]: string }) => {
    const ids = Object.keys(goodsShop)
    const { code, data } = await doGetRetrieveProduct({ ids, searchTotalStockGtZero: true, size: ids.length })

    const goods: { [key: string]: any[] } = {}
    if (code !== 200) {
        ElMessage.error('获取商品列表失败')
        return shopGoods
    }

    data.list?.forEach((item: any) => {
        const { id, productName, productId, pic, salePrices } = item

        const price = Array.isArray(salePrices) ? salePrices[0] : salePrices
        const obj = {
            onlyId: id,
            id: productId,
            name: productName,
            logo: pic,
            price: divTenThousand(price) as unknown as string,
        }
        if (goods[goodsShop[item.id]]) {
            goods[goodsShop[item.id]].push(obj)
        } else {
            goods[goodsShop[item.id]] = [obj]
        }
    })

    return goods
}

/**
 * @LastEditors: lexy
 * @description: 获取店铺基本信息
 * @param {*} shopId
 * @returns {*}
 */
const shops = ref<typeof shopGoods.shopInfo>([])

const { divTenThousand } = useConvert()

async function initShopInfo() {
    const shopInfo = props.formData.shopInfo
    if (!shopInfo?.[0]?.shop?.id) return
    const shopGoods: { [key: string]: string[] } = {}
    const goodsShop: { [key: string]: string } = {}

    const shopIds = props.formData.shopInfo.map((item) => {
        // 记录shop下面的商品id
        shopGoods[item.shop.id] = item.goods?.map((goods) => goods.onlyId).filter((onlyId) => onlyId)

        // 记录商品id上面的shop
        shopGoods[item.shop.id]?.forEach((onlyId) => {
            goodsShop[onlyId] = item.shop.id
        })

        return item.shop.id
    })

    const goods = await getGoods(goodsShop)
    const { data, code, msg } = await doPostShopInfo(shopIds)
    if (code !== 200) return ElMessage.error('获取商品列表失败')
    shopInfo.forEach((item) => {
        data.forEach((element: any) => {
            const { name, logo, id, shopType } = element
            if (element.id === item.shop.id) {
                item.shop = { name, logo, id, shopType }
            }
        })
        if (goods) item.goods = goods[item.shop.id]
    })
    shops.value = shops.value.filter((item) => item.shop.shopType)
}

onBeforeMount(() => {
    initShopInfo()
})
</script>

<style scoped lang="scss">
$color-active-red: #fa3534;
$color-unactive-red: #ff9e9e;
@include b(shop-goods) {
    position: relative;
    padding: 0 5px;
    background-color: #fff;
    margin-bottom: 10px;
    @include e(active) {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 36px;
        width: 362px;
        border: 1px solid rgba(187, 187, 187, 1);
        border-radius: 0 0 8px 8px;
        border-top: transparent;
    }
}
.point {
    border: 3px solid $color-active-red;
    opacity: 0.28;
    width: 2px;
    height: 1px;
    margin-left: 10px;
    cursor: pointer;
    border-radius: 50%;
}
.current {
    opacity: 1;
    width: 2px;
}
.shop-goods--left-point {
    position: absolute;
    left: 5%;
    top: 90%;
    width: 20px;
    height: 20px;
    border: 1px solid $color-active-red;
    border-radius: 50%;
    &::before {
        content: '<';
        color: $color-active-red;
        line-height: 20px;
        display: block;
        text-align: center;
        padding-right: 2px;
    }
    &:hover {
        box-shadow: $color-active-red 0px 0px 0px 1px;
        transform: scale(1.1);
    }
}
.shop-goods--right-point {
    position: absolute;
    right: 5%;
    top: 90%;
    width: 20px;
    height: 20px;
    border: 1px solid $color-active-red;
    border-radius: 50%;
    &::before {
        content: '>';
        color: $color-active-red;
        line-height: 20px;
        display: block;
        text-align: center;
        padding-left: 2px;
    }
    &:hover {
        box-shadow: $color-active-red 0px 0px 0px 1px;
        transform: scale(1.1);
    }
}
.title {
    font-size: 16px;
    color: #333;
    line-height: 22px;
    margin-bottom: 7px;
    padding-left: 10px;
}
</style>
