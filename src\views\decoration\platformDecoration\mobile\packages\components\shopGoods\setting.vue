<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-08-11 00:31:39
 * @LastEditors: lexy
 * @LastEditTime: 2023-06-19 19:09:44
-->
<script setup lang="ts">
import { useVModel } from '@vueuse/core'
import type { PropType } from 'vue'
import settingShopInfo from './components/settingShopInfo.vue'
// 店铺默认值
import defaultshopGoods from './shopGoods'

// 选择素材 e
import selectMaterial from '@/views/material/selectMaterial.vue'
const dialogVisible = ref(false)
const selectMaterialFn = (val: boolean) => {
    dialogVisible.value = val
}
const buttonlFn = () => {
    dialogVisible.value = true
}
// @cropped-file-change="" 裁剪后返回的单个素材
// @checked-file-lists=""  选中素材返回的素材合集
const croppedFileChange = (val: string) => {
    formData.value.shopBigImg = val
}
const checkedFileLists = (val: string[]) => {
    formData.value.shopBigImg = val?.shift() || ''
}
// 选择素材 d
const $props = defineProps({
    formData: {
        type: Object as PropType<typeof defaultshopGoods>,
        default() {
            return defaultshopGoods
        },
    },
})
const $emit = defineEmits(['update:formData'])
const formData = useVModel($props, 'formData', $emit)

// 样式
const showStyles = [
    {
        label: '轮播展示(指定店铺)',
        value: 'is-style-one',
    },
    {
        label: '平铺展示(指定店铺)',
        value: 'is-style-two',
    },
    {
        label: '全部线上店铺(按店铺商品总销量由大到小)',
        value: 'is-style-three',
    },
    {
        label: '全部O2O店铺(按定位距离由近到远)',
        value: 'is-style-four',
    },
]
// 添加商品
const addShopItem = () => {
    formData.value.shopInfo = [...formData.value.shopInfo, defaultshopGoods.shopInfo[0]]
}

// 删除商品
const deleteShopItem = (index: number) => {
    formData.value.shopInfo.splice(index, 1)
}

type Tparam = { index: number; shopItem: typeof defaultshopGoods.shopInfo[0] }
// 修改商品
const updateShopItem = ({ index, shopItem }: Tparam) => {
    formData.value.shopInfo[index] = shopItem
}

// 添加商品大图
const handleImgChange = async (imgSrc: string) => {
    formData.value.shopBigImg = imgSrc
}

const isO2O = computed(() => formData.value.showStyle !== 'is-style-three' && formData.value.showStyle !== 'is-style-four')

watch(
    () => formData.value.shopInfo,
    (newVal) => {
        newVal?.forEach(({ goods }, i) => {
            if (!goods?.length) {
                formData.value.shopInfo[i] = {
                    ...formData.value.shopInfo[i],
                    goods: [...defaultshopGoods.shopInfo[0].goods],
                }
            } else if (goods.length < 3) {
                let goods = defaultshopGoods.shopInfo[0].goods.slice(0, 3 - formData.value.shopInfo[i].goods?.length)
                formData.value.shopInfo[i] = {
                    ...formData.value.shopInfo[i],
                    goods: formData.value.shopInfo[i].goods.concat(goods),
                }
            }
        })
    },
    { immediate: true },
)
</script>

<template>
    <div>
        <el-form :model="formData" label-width="100px">
            <el-form-item label="样式展示">
                <el-radio-group v-model="formData.showStyle">
                    <el-radio v-for="item in showStyles" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="标题">
                <el-input v-model="formData.title" :maxlength="5" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item v-show="formData.showStyle === 'is-style-two'" label="活动页主图">
                <!-- <q-upload class="homeSwiperForm-add" @change="handleImgChange">
                    <img v-if="formData.shopBigImg" :src="formData.shopBigImg" />
                    <div v-else>
                        <span>+ 添加商品大图</span>
                        <p style="margin-top: 5px">建议宽度750像素</p>
                    </div>
                </q-upload> -->
                <div v-if="!formData.shopBigImg" class="selectMaterialStyle" @click="buttonlFn">
                    <span class="selectMaterialStyle__span">
                        <span>+ 添加商品大图</span><br />
                        <span style="font-size: 12px; color: #a7a7a7">建议宽度750像素</span>
                    </span>
                </div>
                <img v-else :src="formData.shopBigImg" alt="" class="selectMaterialStyle" @click="buttonlFn" />
            </el-form-item>
            <el-form-item v-show="isO2O">
                <el-button type="primary" @click="addShopItem">添加店铺商品</el-button>
            </el-form-item>
        </el-form>
        <div v-show="isO2O">店铺商品信息</div>
        <div v-show="isO2O">
            <template v-for="(item, index) in formData.shopInfo" :key="item.shop.id + index">
                <setting-shop-info :shop-item="item" :shop-index="index" @delete-shop-item="deleteShopItem" @update-shop-item="updateShopItem" />
            </template>
        </div>
        <!-- 选择素材 e -->
        <select-material
            :dialog-visible="dialogVisible"
            :upload-files="1"
            @select-material-fn="selectMaterialFn"
            @cropped-file-change="croppedFileChange"
            @checked-file-lists="checkedFileLists"
        />
        <!-- 选择素材 d -->
    </div>
</template>

<style lang="scss" scoped>
@include b(homeSwiperForm-add) {
    width: 100%;
    height: 300px;
    margin-bottom: 10px;
    :deep(.el-upload) {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: inherit;
        height: 300px;
    }
    span {
        color: #3088f0;
        cursor: pointer;
    }
    p {
        font-size: 12px;
        color: #a7a7a7;
    }
}
@include b(selectMaterialStyle) {
    width: 260px;
    height: 300px;
    border-radius: 5px;
    overflow: hidden;
    border: 1px dashed #ccc;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    @include e(span) {
        color: #3088f0;
        font-size: 14px;
    }
}
</style>
