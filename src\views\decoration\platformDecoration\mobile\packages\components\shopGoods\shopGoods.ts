/**
 * @LastEditors: pongyong
 * @description: 商品店铺栏默认值
 * @params showStyle 选择样式
 * @params titleName 标题名称
 * @params backgroundColor 字体背景颜色
 * @params color 字体颜色
 * @params goods 商品信息
 */
export default {
    showStyle: 'is-style-one',
    shopBigImg: '',
    title: '',
    shopInfo: [
        {
            shop: { name: '', id: '', logo: '', shopType: '' },
            goods: [
                { name: '', id: '', logo: '', price: '', onlyId: '' },
                { name: '', id: '', logo: '', price: '', onlyId: '' },
                { name: '', id: '', logo: '', price: '', onlyId: '' },
            ],
        },
    ],
}

export type ShopItemType = {
    shop: ShopInfoType
    goods: ShopGoodInfoType[]
}

export type ShopInfoType = {
    name: string
    id: string
    logo?: string
    shopType: string
}

export type ShopGoodInfoType = {
    name: string
    id: string
    logo?: string
    price: string
    onlyId: string
}
