<template>
    <div
        class="user"
        :style="{
            overflowY: 'scroll',
            left: '0px',
        }"
    >
        <div class="user__radius" :style="headStyleConfig.headBackGround">
            <!-- <top-bar /> -->
            <div class="center">个人中心</div>
            <div class="info">
                <img :src="Avatar" class="info__avatar" alt="info" />
                <div class="info__name">快乐小狗</div>
                <!-- <div
            @click="pickCodeClickHandle"
            class="info__qrcode"
            v-if="formData.codeStyle === 2 && formData.qrcodeVisible"
          >
          </div> -->
            </div>
            <div class="data">
                <div class="data__item">
                    <div class="data__number">60.30</div>
                    <div class="data__text">余额</div>
                </div>
                <div>｜</div>
                <div class="data__item">
                    <div class="data__number">1024</div>
                    <div class="data__text">积分</div>
                </div>
                <div>｜</div>
                <div class="data__item">
                    <div class="data__number">50</div>
                    <div class="data__text">收藏</div>
                </div>
                <div>｜</div>
                <div class="data__item">
                    <div class="data__number">100</div>
                    <div class="data__text">足迹</div>
                </div>
            </div>
            <div v-if="$props.formData.hideCartInlet" class="radius">
                <div class="radius__inner" :style="{ backgroundColor: headStyleConfig.cardColor }">
                    <div class="radius__inner--item" :style="{ color: headStyleConfig.textColor }">
                        <span class="radius__inner--text">尊享会员</span>
                        <span class="radius__inner--split">|</span>
                        <span class="radius__inner--text" :style="{ color: headStyleConfig.textColor }">{{ formData.getCartText }}</span>
                    </div>
                    <span
                        class="radius__inner--item radius__inner--btn"
                        :style="{
                            backgroundColor: headStyleConfig.textColor,
                            color: headStyleConfig.cardColor,
                        }"
                        >立即开通</span
                    >
                </div>
            </div>
        </div>
        <div class="user__cover">
            <div class="order">
                <div class="order__all">
                    <div class="order__all--me">我的订单</div>
                    <div class="order__all--check">
                        查看全部订单
                        <span>></span>
                    </div>
                </div>
                <div class="order__quick">
                    <div v-for="item in orderInfo" :key="item.id" class="order__quick--item">
                        <img :src="item.url" class="order__quick--img" alt="waitIcon" />
                        <div class="order__quick--text">
                            {{ item.name }}
                        </div>
                    </div>
                </div>
            </div>
            <div class="user__menu">
                <div v-if="$props.formData.menuStyle === 1" class="user__menu--expand">
                    <Menu>
                        <template v-for="expandItem in $props.formData.menuList" :key="expandItem.menuName">
                            <menu-item v-if="expandItem.showMenu" :class="{ splitFlag: expandItem.splitFlag }" :img-url="expandItem.menuIconUrl"
                                >{{ expandItem.menuName }}
                            </menu-item>
                        </template>
                    </Menu>
                </div>
                <GridMenu v-if="formData.menuStyle === 2" :grid-menu="$props.formData.menuScratchable"></GridMenu>
                <div class="version">
                    <div class="version__img">
                        <img class="miniBottomLog" :src="LogoTitle" alt="miniBottomLog" />
                    </div>
                    <!-- <div class="version__text">宠有灵犀商城系统{{ version }}</div> -->
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import Menu from './components/menu.vue'
import MenuItem from './components/menuItem.vue'
import GridMenu from './components/gridMenu.vue'
// import TopBar from '@/views/decoration/components/UserCenter/PhoneView/TopBar.vue'
// import { getShopInfo } from '@/api/businessCenter/setting'
import userCenterDefaultData from './user-center'
import type { UserCenterType } from './user-center'
import type { PropType } from 'vue'
import LogoTitle from '@/assets/image/sign/title_icon_horizontal.png'
import Avatar from '@/assets/image/avatar/avatar.webp'
const $props = defineProps({
    formData: {
        type: Object as PropType<UserCenterType>,
        default: userCenterDefaultData,
    },
})
const headStyleConfig = computed(() => {
    return {
        headBackGround:
            $props.formData.headStyle === 1
                ? 'background-image: -webkit-linear-gradient( -90deg, rgb(189,58,228) 0%, rgb(233,157,255) 51%, rgb(241,206,255) 100%)'
                : `background-image: url(${$props.formData.customStyle.backgroundImage})`,
        cardColor: $props.formData.headStyle === 1 ? '#45403C' : $props.formData.customStyle.cardColor,
        textColor: $props.formData.headStyle === 1 ? '#E4CB98' : $props.formData.customStyle.textColor,
    }
})
const orderInfo = computed(() => {
    return $props.formData.orderInfo
})
const version = '1.0.0'
</script>

<style lang="scss" scoped>
@import '@/assets/css/decoration/userCenter.scss';
</style>
