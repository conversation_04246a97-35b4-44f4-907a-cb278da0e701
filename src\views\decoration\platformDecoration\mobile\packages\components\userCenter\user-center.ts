/*
 * @description:
 * @Author: lexy
 * @Date: 2022-11-04 15:07:15
 * @LastEditors: lexy
 * @LastEditTime: 2023-05-30 14:41:46
 */
import type { LinkSelectItem } from '@/components/link-select/linkSelectItem'
const CDNHost = import.meta.env.VITE_CDN_URL
/**
 * @LastEditors: lexy
 * @description: 用户中心配置
 * @param {CustomStyleType} customStyle 头部自定义风格
 * @param {string} getCartText 领卡文案
 * @param {number} headStyle 头部风格 1系统风格 2自定义风格
 * @param {number} hideCartInlet 非会员显示领卡入口 0为隐藏 1为显示
 * @param {number} menuStyle 菜单栏样式 1列表式 2位九宫格
 * @param {UserCenterMenuItem[]} menuVos 菜单栏列表
 */
export interface UserCenterType {
    customStyle: CustomStyleType
    getCartText: string
    headStyle: number
    id: string
    hideCartInlet: number
    menuStyle: number
    menuList: UserCenterMenuItem[]
    menuScratchable: UserCenterMenuItem[]
    orderInfo: OrderItem[]
}
/**
 * @LastEditors: lexy
 * @description: 菜单栏类型
 * @param {number} allowUse 0不可用 1可用
 * @param {string} defaultIcon 默认图标地址
 * @param {boolean} hideMenu 菜单是否展示 0隐藏1显示
 * @param {string} menuIconUrl 菜单当前图标url
 * @param {string} menuName 菜单名称
 * @param {string} sortIndex 排序位置
 * @param {boolean} splitFlag 分隔
 */
export interface UserCenterMenuItem {
    id: string | null
    allowUse: number
    defaultIcon: string
    showMenu: boolean
    linkSelectItem: LinkSelectItem
    menuIconUrl: string
    menuName: string
    sortIndex: number
    splitFlag: boolean
}
/**
 * @LastEditors: lexy
 * @description:自定义头部类型
 * @param {string} backgroundImage 背景图片
 * @param {string} cardColor 卡面颜色
 * @param {string} textColor 文字颜色
 */
export interface CustomStyleType {
    backgroundImage: string
    cardColor: string
    textColor: string
}
enum OrderInfoKey {
    // 待付款
    unpay,
    // 待发货
    waitSend,
    // 待提货
    waitPick,
    // 售后
    afterSale,
    //评价
    waitRate,
}
export interface OrderItem {
    id: keyof typeof OrderInfoKey
    name: string
    url: string
    link: LinkSelectItem
    key: string
}
export default {
    /** 自定义风格样式,json存储 */
    customStyle: {
        backgroundImage: 'https://devoss.chongyoulingxi.com/system-front/mobile/def_non_icon.png',
        cardColor: '#CD7A27',
        textColor: '#F1C8C8',
    },
    getCartText: '',
    headStyle: 1,
    hideCartInlet: 0,
    id: '',
    menuStyle: 1,
    menuList: [
        {
            id: '1',
            allowUse: 1,
            defaultIcon: `${CDNHost}/system-front/mobile/票券.png`,
            showMenu: true,
            linkSelectItem: {
                id: '',
                name: '',
                url: '',
                type: 2, // 类型 type 对应链接选择器的 index
            },
            menuIconUrl: `${CDNHost}/system-front/mobile/票券.png`,
            menuName: '领券中心',
            sortIndex: 0,
            splitFlag: false,
        },
        {
            id: '2',
            allowUse: 1,
            defaultIcon: `${CDNHost}/system-front/mobile/优惠券.png`,
            showMenu: true,
            linkSelectItem: {
                id: '',
                name: '',
                url: '',
                type: 0,
            },
            menuIconUrl: `${CDNHost}/system-front/mobile/优惠券.png`,
            menuName: '我的优惠券',
            sortIndex: 1,
            splitFlag: false,
        },
        {
            id: '3',
            allowUse: 1,
            defaultIcon: `${CDNHost}/system-front/mobile/购物车.png`,
            showMenu: true,
            linkSelectItem: {
                id: '',
                name: '',
                url: '',
                type: 0,
            },
            menuIconUrl: `${CDNHost}/system-front/mobile/购物车.png`,
            menuName: '购物车',
            sortIndex: 2,
            splitFlag: false,
        },
        {
            id: '4',
            allowUse: 1,
            defaultIcon: `${CDNHost}/system-front/mobile/文章.png`,
            showMenu: true,
            linkSelectItem: {
                id: '',
                name: '',
                url: '',
                type: 0,
            },
            menuIconUrl: `${CDNHost}/system-front/mobile/文章.png`,
            menuName: '地址管理',
            sortIndex: 3,
            splitFlag: false,
        },
        {
            id: '5',
            allowUse: 1,
            defaultIcon: `${CDNHost}/system-front/mobile/设置.png`,
            showMenu: true,
            linkSelectItem: {
                id: '',
                name: '',
                url: '',
                type: 0,
            },
            menuIconUrl: `${CDNHost}/system-front/mobile/设置.png`,
            menuName: '设置',
            sortIndex: 4,
            splitFlag: false,
        },
    ],
    menuScratchable: [
        {
            id: '1',
            allowUse: 1,
            defaultIcon: `${CDNHost}/system-front/mobile/票券.png`,
            showMenu: true,
            linkSelectItem: {
                id: '',
                name: '领券中心',
                url: '/pluginPackage/coupon/couponCenter/CouponsCenter',
                type: 0,
            },
            menuIconUrl: `${CDNHost}/system-front/mobile/票券.png`,
            menuName: '领券中心',
            sortIndex: 0,
            splitFlag: false,
        },
        {
            id: '2',
            allowUse: 1,
            defaultIcon: `${CDNHost}/system-front/mobile/优惠券.png`,
            showMenu: true,
            linkSelectItem: {
                id: '',
                name: '我的优惠券',
                url: '/pluginPackage/coupon/myCoupon/MyCoupon',
                type: 0,
            },
            menuIconUrl: `${CDNHost}/system-front/mobile/优惠券.png`,
            menuName: '我的优惠券',
            sortIndex: 1,
            splitFlag: false,
        },
        {
            id: '3',
            allowUse: 1,
            defaultIcon: `${CDNHost}/system-front/mobile/购物车.png`,
            showMenu: true,
            linkSelectItem: {
                id: '',
                name: '购物车',
                url: '/',
                type: 0,
            },
            menuIconUrl: `${CDNHost}/system-front/mobile/购物车.png`,
            menuName: '购物车',
            sortIndex: 2,
            splitFlag: false,
        },
        {
            id: '4',
            allowUse: 1,
            defaultIcon: `${CDNHost}/system-front/mobile/文章.png`,
            showMenu: true,
            linkSelectItem: {
                id: '',
                name: '',
                url: '',
                type: 0,
            },
            menuIconUrl: `${CDNHost}/system-front/mobile/文章.png`,
            menuName: '地址管理',
            sortIndex: 3,
            splitFlag: false,
        },
        {
            id: '5',
            allowUse: 1,
            defaultIcon: `${CDNHost}/system-front/mobile/设置.png`,
            showMenu: true,
            linkSelectItem: {
                id: '',
                name: '',
                url: '',
                type: 0,
            },
            url: `${CDNHost}/system-front/mobile/设置.png`,
            menuName: '设置',
            sortIndex: 4,
            splitFlag: false,
        },
    ],
    orderInfo: [
        {
            id: 'unpay',
            name: '待付款',
            url: `${CDNHost}/system-front/mobile/待付款.png`,
            link: {
                id: '',
                name: '',
                url: '/pluginPackage/order/orderList/orderList?id=1',
                type: 0,
            },
            key: 'unpaid',
        },
        {
            id: 'waitSend',
            name: '待发货',
            url: `${CDNHost}/system-front/mobile/待发货.png`,
            link: {
                id: '',
                name: '',
                url: '/pluginPackage/order/orderList/orderList?id=2',
                type: 0,
            },
            key: 'undelivered',
        },
        {
            id: 'waitPick',
            name: '待收货',
            url: `${CDNHost}/system-front/mobile/待收货.png`,
            link: {
                id: '',
                name: '',
                url: '/pluginPackage/order/orderList/orderList?id=3',
                type: 0,
            },
            key: 'unreceived',
        },
        {
            id: 'waitRate',
            name: '待评价',
            url: `${CDNHost}/system-front/mobile/待评价.png`,
            link: {
                id: '',
                name: '',
                url: '/pluginPackage/order/orderList/orderList?id=5',
                type: 0,
            },
            key: 'unrate',
        },
        {
            id: 'afterSale',
            name: '售后',
            url: `${CDNHost}/system-front/mobile/售后.png`,
            link: {
                id: '',
                name: '',
                url: '/pluginPackage/order/afterSales/AfterSales',
                type: 0,
            },
            key: 'unhandledAfs',
        },
    ],
} as UserCenterType
