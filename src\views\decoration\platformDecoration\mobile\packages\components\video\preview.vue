<script setup lang="ts">
import { PropType } from 'vue'
import defaultVideoData from './video'
/*
 *variable
 */
const $props = defineProps({
    formData: {
        type: Object as PropType<typeof defaultVideoData>,
        default() {
            return defaultVideoData
        },
    },
})
/*
 *lifeCircle
 */
/*
 *function
 */
</script>

<template>
    <div class="videoCom" :style="{ padding: $props.formData.radioTC === 2 ? '10px' : '' }">
        <img src="https://devoss.chongyoulingxi.com/system-front/image_error.jpeg" style="width: 100%; height: 200px" />
        <i
            class="iconfont iconzhibo1 videoIcon"
            :style="{ bottom: $props.formData.radioTC === 2 ? '25px' : '10px', left: $props.formData.radioTC === 2 ? '20px' : '5px' }"
        ></i>
    </div>
</template>

<style lang="scss" scoped></style>
