/*
 * @description: 装修类型
 * @Author: lexy
 * @Date: 2022-09-20 11:30:21
 * @LastEditors: lexy
 * @LastEditTime: 2024-02-03 17:55:14
 */
export enum FUNCTIONTYPE {
    TABBAR,
    CLASSIFY_PAGE,
    PERSONAL_CENTER,
    PAGE,
}
export enum DECORATION_TYPE {
    WECHAT_MINI_APP,
    PC,
    OTHERS,
}

export enum ENDPOINT_TYPE {
    WECHAT_MINI_APP,
    H5_APP,
    PC_MALL,
}
export enum PAGES_TYPE {
    RECOMMENDED_MALL_HOME_PAGE = '商城首页(推荐)',
    SAME_CITY_MALL_HOME_PAGE = '商城首页(同城)',
    PRODUCT_CATEGORY_PAGE = '商品分类',
    BOTTOM_NAVIGATION_PAGE = '底部导航',
    PERSONAL_CENTER_PAGE = '个人中心',
    CUSTOMIZED_PAGE = '自定义页面',
}
/**
 * @LastEditors: lexy
 * @description: 装修页面类型
 * @param customize 自定义页面
 * @param classification 分类
 * @param control 控件
 */
export enum PAGETYPE {
    customize,
    classification,
    control,
    userCenter,
}
export type PageType = keyof typeof PAGETYPE
export interface SubmitForm {
    id: string
    functionType: keyof typeof FUNCTIONTYPE
    properties: any
    isDef?: boolean
    isLocal?: boolean
    pageName?: string
    platforms: DecorationType
}
export type DecorationType = keyof typeof DECORATION_TYPE | string
export type EndpointType = keyof typeof ENDPOINT_TYPE | string
/**
 * @LastEditors: lexy
 * @description: 检索商品接口返回
 */
export interface ApiRetrieveComItemType {
    createTime: string
    id: string
    initSalesVolume: number
    pic: string
    platformCategoryFirstId: string
    platformCategorySecondId: string
    platformCategoryThirdId: string
    productId: string
    productName: string
    salePrices: string[]
    salesVolume: string
    shopId: string
    shopName: string
    specs: string
    status: 'SELL_ON' | 'SELL_OFF'
}

export interface ApiRetrieveTemplateList {
    id: string
    name: string
    description: string
    enabled: boolean
    pages: {
        pageId: string
        pageType: keyof typeof PAGES_TYPE
    }[]
    templateType: string
    endpointType: string
}
