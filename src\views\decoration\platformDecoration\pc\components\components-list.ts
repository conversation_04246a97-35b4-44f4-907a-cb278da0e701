import { ComponentItem } from './packages/index/formModel'
const LOGO_IMAGE = import.meta.env.VITE_RESUME_LOGO

export const basicComponentList: ComponentItem[] = [
    {
        icon: 'linear-new-other-cover-flow-banner',
        value: 'swiper',
        label: '轮播图',
        width: 950,
        show: true,
        loadingHeight: '582px',
    },
    {
        icon: 'yingxiaogongju-tuangoumiaosha',
        value: 'seckill',
        label: '秒杀活动',
        width: 480,
        show: true,
        space: 24,
        loadingHeight: '64px',
    },
    {
        icon: 'shangpin3',
        value: 'goods',
        label: '商品组件',
        width: 950,
        show: true,
        space: 24,
        loadingHeight: '601px',
    },
    {
        icon: 'tuijian',
        value: 'recommend',
        label: '推荐商品',
        width: 950,
        show: true,
        space: 10,
        loadingHeight: '601px',
    },
    {
        icon: 'dianpu5',
        value: 'shop',
        width: 804,
        space: 24,
        show: true,
        label: '推荐店铺',
        loadingHeight: '601px',
    },
]

/**
 * @: 获取默认底部组件
 */
export const getDefaultEndComponents = (): ComponentItem[] => [
    {
        formData: { img: 'https://medusa-small-file-1253272780.cos.ap-shanghai.myqcloud.com/gruul/2024/03/12/65eff2048531567dcea2fae1.jpeg' },
        icon: 'a-37-fuwu-xianxing',
        id: '1710222827274',
        label: '服务保障',
        loadingHeight: '84px',
        show: true,
        space: 5,
        value: 'guarantee',
        width: 480,
    },
    {
        icon: 'toolbar',
        id: '1710152754261',
        label: '底部信息',
        loadingHeight: '434px',
        value: 'footerInfo',
        show: true,
        width: 804,
        formData: {
            QRcode: 'https://medusa-small-file-1253272780.cos.ap-shanghai.myqcloud.com/gruul/2024/03/12/65efef5582c4d47178297549.jpeg',
            logo: {
                img: LOGO_IMAGE,
                logoInfo: [
                    { title: '杭州宠有灵犀有限公司', link: '' },
                    { title: '<EMAIL>', link: '' },
                    { title: '浙江省杭州市振宁路农业大厦一号楼506', link: '' },
                ],
            },
            module: [
                {
                    moduleName: '关于我们',
                    moduleTitle: [
                        { title: '公司动态', link: '' },
                        { title: '增值电信业务经营许可', link: '' },
                    ],
                },
                {
                    moduleName: '产品',
                    moduleTitle: [
                        { title: '社区团购', link: '' },
                        { title: 'O2O外卖配送', link: '' },
                        { title: 'B2C单商户商城', link: '' },
                        { title: 'B2B2C多商户商城', link: '' },
                    ],
                },
            ],
            icon: 'toolbar',
            id: '1710152754261',
            label: '底部信息',
            loadingHeight: '434px',
            value: 'footerInfo',
            width: 804,
        },
    },
    {
        formData: [{ title: '杭州宠有灵犀有限公司', link: '' }],
        icon: 'copyright',
        id: '1710152878157',
        label: '版权信息',
        loadingHeight: '432px',
        show: true,
        top: true,
        value: 'copyright',
        width: 804,
    },
]

/**
 * @: 获取默认顶部组件
 */
export const getDefaultStartComponents = (): ComponentItem[] => [
    {
        borderColor: '#666',
        formData: { message: '这里是欢迎语~', settledIn: true },
        icon: 'zhanghaodenglu',
        id: '1709953988727',
        label: '登录栏',
        loadingHeight: '80px',
        show: true,
        top: true,
        value: 'loginLine',
        width: 480,
    },
    {
        borderColor: '#fff',
        formData: {
            car: true,
            logo: LOGO_IMAGE,
            search: true,
        },
        icon: 'sousuo1',
        show: true,
        id: '1710152416242',
        label: '搜索栏',
        loadingHeight: '180px',
        value: 'search',
        width: 480,
    },
    {
        borderColor: '#fff',
        formData: [{ text: '所有分类', type: 'system', link: '所有分类', id: 1710152404895 }],
        icon: 'daohangliebiao',
        id: '1710152892549',
        label: '导航栏',
        show: true,
        loadingHeight: '530px',
        top: true,
        value: 'navigation',
        width: 530,
    },
]
