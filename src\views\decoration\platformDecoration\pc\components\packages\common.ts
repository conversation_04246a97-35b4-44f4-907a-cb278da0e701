import { UploadProps, ElMessage } from 'element-plus'

// 上传url
export const uploadUrl = import.meta.env.VITE_BASE_URL + 'gruul-mall-carrier-pigeon/oss/upload'

/**
 * @: 上传前校验
 */
export const beforeAvatarUpload: UploadProps['beforeUpload'] = (rawFile) => {
    const rawFileType = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/bmp']
    if (!rawFileType.includes(rawFile.type)) {
        ElMessage.error('上传素材类型必须是 jpg、gif、jpeg、png、webp、bmp其中之一')
        return false
    } else if (rawFile.size / 1024 / 1024 > 10) {
        ElMessage.error('上传素材大小不能超过 10MB!')
        return false
    }
    return true
}
