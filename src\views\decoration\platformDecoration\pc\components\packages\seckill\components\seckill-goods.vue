<script setup lang="ts">
import price from '@/views/decoration/platformDecoration/pc/components/price/index.vue'
import { ElCarousel } from 'element-plus'
import { chunk } from 'lodash'
import { SeckillProductRes } from '@/apis/decoration/type'

const props = defineProps<{
    list: SeckillProductRes[]
    isSeckill: boolean
}>()

const carouselRef = ref<InstanceType<typeof ElCarousel>>()

// 上一页
const pre = () => carouselRef.value?.prev()

// 下一页
const next = () => carouselRef.value?.next()

const goodsList = computed<Array<SeckillProductRes[]>>(() => chunk(props.list, 5))
const { divTenThousand } = useConvert()
</script>

<template>
    <div v-if="isSeckill" class="parent">
        <QIcon v-if="goodsList.length > 1" :svg="true" name="icon-a-zuhe1374" class="pre arrow" size="40px" @click="pre" />

        <div class="more cp">查看更多<QIcon name="icon-chevron-right" style="top: 1.3px"></QIcon></div>

        <el-carousel ref="carouselRef" :autoplay="false" style="min-width: 1010px" height="260px" arrow="never" indicator-position="none">
            <el-carousel-item v-for="(item, index) in goodsList" :key="index">
                <div class="item">
                    <div v-for="(ite, ind) in item" :key="ind" class="goods">
                        <img class="goods__img" :src="ite.productPic" />

                        <div class="goods__name">{{ ite.productName }}</div>

                        <div class="goods__price">
                            <price :price="divTenThousand(ite.price)" :sale-price="divTenThousand(ite.secKillPrice)" />
                        </div>

                        <!-- 分割线 -->
                        <div class="goods__line"></div>
                    </div>
                </div>
            </el-carousel-item>
        </el-carousel>

        <QIcon v-if="goodsList.length > 1" :svg="true" name="icon-a-zuhe13741" class="next arrow" size="40px" @click="next" />
    </div>

    <div v-else class="parent un-seckill">暂无活动~</div>
</template>

<style lang="scss" scoped>
@include b(goods) {
    position: relative;
    width: 202px;
    height: 100%;
    padding: 30px 16px 16px;
    background-color: #fff;
    text-align: center;
    @include e(img) {
        width: 146px;
        height: 140px;
        margin-bottom: 8px;
    }

    @include e(name) {
        font-size: 12px;
        text-align: justify;
        height: 34px;
        margin-bottom: 10px;

        @include utils-ellipsis(2);
    }

    @include e(price) {
        width: 130px;
        margin: 0 auto;
    }

    @include e(line) {
        position: absolute;
        top: 50%;
        right: 0;
        transform: translateY(-50%);
        height: 101px;
        width: 1px;
        background-color: #000000;
        opacity: 0.05;
    }
}

@include b(parent) {
    position: relative;
    background-color: #fff;
    height: 260px;
    width: 1010px;
}

@include b(arrow) {
    top: 50%;
    transform: translateY(-50%);
    position: absolute;
    z-index: 4;
    cursor: pointer;
    font-size: 40px;
    opacity: 0.5;
}

@include b(pre) {
    left: -9px;
}

@include b(next) {
    right: -8px;
}

@include b(more) {
    position: absolute;
    color: #8c8c8c;
    font-size: 12px;
    z-index: 2;
    right: 16px;
    top: 9px;
}

@include b(item) {
    display: flex;
}

@include b(un-seckill) {
    background-image: url('@/assets/image/decoration/un-seckill.png');
    background-position: center 30px;
    background-repeat: no-repeat;
    line-height: 420px;
    font-size: 16px;
    color: #999999;
    text-align: center;
}
</style>
