<script setup lang="ts">
import CountDown from '@/components/order/count-down/index.vue'
import { PropType } from 'vue'

const props = defineProps({
    name: {
        type: String,
        default: '',
    },
    isSeckill: {
        type: Boolean,
        default: false,
    },
    info: {
        type: Object as PropType<{ secKillStatus: string; startTime: string }>,
        default: () => ({}),
    },
})

const times: ['hours', 'minutes', 'seconds'] = ['hours', 'minutes', 'seconds']

const startTime = computed(() => {
    if (!props.info.startTime) return ''
    const time = props.info.startTime.split(' ')[1]
    return time.split(':')[0]
})
</script>

<template>
    <div v-if="isSeckill" class="info">
        <div class="info__name">
            {{ name || '限时秒杀' }}
        </div>

        <div class="info__time">{{ startTime }}点场</div>

        <div class="info__line" />

        <div class="info__text">{{ info.secKillStatus === 'NOT_STARTED' ? '距离开始' : '距离结束' }}</div>

        <div class="info__countdown">
            <count-down v-if="info.startTime" :create-time="info.startTime" :pay-timeout="info.secKillStatus === 'NOT_STARTED' ? '0' : 60 * 60 + ''">
                <template #default="{ timeTable }">
                    <div class="countdown">
                        <div v-for="item in times" :key="item" class="countdown__time">
                            <div class="countdown__time--item">{{ timeTable[item] }}</div>
                            <div v-if="item !== 'seconds'" class="countdown__time--dot">:</div>
                        </div>
                    </div>
                </template>
            </count-down>
        </div>
    </div>

    <div v-else class="info un-seckill">
        <div class="info__name">
            {{ name || '限时秒杀' }}
        </div>
    </div>
</template>

<style lang="scss" scoped>
@include b(info) {
    padding: 40px 24px 33px;
    min-width: 190px;
    height: 100%;
    color: #fff;
    background: url('@/assets/image/decoration/seckill.png');
    text-align: center;
    font-size: 16px;

    @include e(name) {
        font-size: 22px;
    }

    @include e(time) {
        margin-top: 27px;
    }

    @include e(line) {
        height: 2px;
        width: 16px;
        margin: 16px auto;
        background-color: #fff;
    }

    @include e(text) {
        margin: 16px auto 22px;
    }
}

@include b(countdown) {
    display: flex;
    justify-content: space-between;

    @include e(time) {
        display: flex;
        align-items: center;
        color: #fff;
        font-size: 20px;
        flex: 1;
        @include m(item) {
            width: 32px;
            height: 32px;
            border-radius: 2px;
            background: #000;
            line-height: 32px;
            text-align: center;
        }
        @include m(dot) {
            padding: 0 9px;
        }
    }
}

@include b(un-seckill) {
    line-height: 190px;
}
</style>
