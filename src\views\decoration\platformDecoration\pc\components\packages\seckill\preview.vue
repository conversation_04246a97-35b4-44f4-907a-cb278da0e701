<script setup lang="ts">
import seckillInfo from './components/seckill-info.vue'
import seckillGoods from './components/seckill-goods.vue'
import seckill from './seckill'
import { doGetSecondsKill, doGetSeckillSessionsProduct } from '@/apis/decoration/index'
import { SeckillProductRes } from '@/apis/decoration/type'
import type { PropType } from 'vue'

defineProps({
    formData: {
        type: Object as PropType<typeof seckill>,
        default: seckill,
    },
})

const isSeckill = ref(false)

const list = ref<SeckillProductRes[]>([])

/**
 * @: 获取秒杀数据
 */
const sKillInfo = ref<{ secKillStatus: string; startTime: string }>()
const getSecondsKill = async () => {
    try {
        const res = await doGetSecondsKill()
        isSeckill.value = !!res.data.length
        if (res.data.length) {
            sKillInfo.value = res.data[0]
            getSeckillProduct()
        }
    } catch (error) {
        console.log('error', error)
    }
}

onBeforeMount(() => {
    getSecondsKill()
})

/**
 * @: 获取秒杀商品
 */
const getSeckillProduct = async () => {
    const { data } = await doGetSeckillSessionsProduct({ shopId: '', startTime: sKillInfo.value?.startTime || '', size: 12, current: 1 })
    list.value = data.records
}
</script>

<template>
    <div class="seckill">
        <div class="main">
            <!-- 秒杀信息 -->
            <seckill-info :name="formData.name" :is-seckill="isSeckill" :info="sKillInfo" />
            <seckill-goods :list="list" :is-seckill="isSeckill" />
        </div>
    </div>
</template>

<style lang="scss" scoped>
@include b(seckill) {
    height: 260px;
    .main {
        display: flex;
    }
}
</style>
