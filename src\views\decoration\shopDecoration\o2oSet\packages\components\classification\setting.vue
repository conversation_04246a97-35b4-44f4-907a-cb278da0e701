<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-10-15 15:24:55
 * @LastEditors: lexy
 * @LastEditTime: 2023-11-09 10:16:22
-->
<script setup lang="ts">
import { useVModel } from '@vueuse/core'
import category from '../goods/category.vue'
import classification from './classification'
import type { DeCategoryType, DeCategoryItem } from './classification'
import type { PropType } from 'vue'
/*
 *variable
 */
const $props = defineProps({
    formData: {
        type: Object as PropType<DeCategoryType>,
        default() {
            return classification
        },
    },
})
const $emit = defineEmits(['update:formData'])
const formModel = useVModel($props, 'formData', $emit)
const dialogShow = ref(false)
// 类目拖动下角标
const classDragIndex = ref(-1)
/*
 *lifeCircle
 */
/*
 *function
 */
const handleSelectedCategory = (e: DeCategoryItem[]) => {
    formModel.value.categoryList = e
}
const handleDel = (index: number) => {
    formModel.value.categoryList.splice(index, 1)
}
/**
 * @LastEditors: lexy
 * @description: 开始拖动，记录拖动的组件下角标
 * @param {*} i
 */
const handleDragClass = (i: number) => {
    classDragIndex.value = i
}
/**
 * @LastEditors: lexy
 * @description:  阻止默认行为，否则drop事件不会触发
 * @returns {*}
 */
const handleDragoverClass = (e: Event) => {
    e.preventDefault()
}
/**
 * @LastEditors: lexy
 * @description: 被放置的容器触发事件，交换两个组件的位置
 * @param {*} i
 */
const handleDropClass = (i: number) => {
    if (classDragIndex.value === i) {
        return false
    }
    const temp = formModel.value.categoryList.splice(classDragIndex.value, 1, formModel.value.categoryList[i])
    formModel.value.categoryList.splice(i, 1, ...temp)
}
</script>

<template>
    <el-form v-model="formModel">
        <el-form-item label="样式">
            <el-radio-group v-model="formModel.style">
                <el-radio :label="2">分类商品</el-radio>
                <el-radio :label="3">大图商品</el-radio>
                <el-radio :label="4">商品列表</el-radio>
                <el-radio :label="5">金刚区分类</el-radio>
            </el-radio-group>
        </el-form-item>
    </el-form>
</template>

<style lang="scss" scoped>
@include b(category) {
    width: 363px;
    box-sizing: border-box;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 20px;
    font-size: 14px;
    @include e(header) {
        @include flex(space-between);
        color: #ccc;
        margin-bottom: 4px;
    }
    @include e(list) {
        min-height: 100px;
        max-height: 200px;
        overflow-y: auto;
    }
    @include e(item) {
        @include flex(space-between);
        @include m(del) {
            color: #ff0000;
            cursor: pointer;
        }
        margin-bottom: 3px;
    }
}
</style>
