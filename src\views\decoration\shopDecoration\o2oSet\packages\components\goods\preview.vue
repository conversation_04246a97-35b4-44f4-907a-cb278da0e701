<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-08-08 17:53:14
 * @LastEditors: lexy
 * @LastEditTime: 2023-06-09 14:39:58
-->
<script setup lang="ts">
import { PropType, ref, computed, watch } from 'vue'
import defaultGoodData from './goods'
import { ApiGoodItemType, ListStyle } from './goods'
import PricesLine from './pricesLine.vue'
import Coner from './coner.vue'
const def_good_pic = 'https://devoss.chongyoulingxi.com/system-front/mobile/def_commodity.png'
/*
 *variable
 */
const $props = defineProps({
    formData: {
        type: Object as PropType<typeof defaultGoodData>,
        default() {
            return defaultGoodData
        },
    },
})
const goodsList = ref<any[] | ApiGoodItemType[]>([])
const pageStyle = computed(() => {
    return {
        padding: `0px 10px`,
        minHeight: '261px',
        fontSize: '15px',
    }
})
/**
 * @LastEditors: lexy
 * @description: 动态显示商品边框样式
 */
const goodStyle = computed(() => {
    let margin = ''
    let width = ''
    const { listStyle } = $props.formData
    switch (listStyle) {
        case 'goods-style--four':
            margin = `0px 10px 0px 0px`
            break
        case 'goods-style--two':
            margin = `0px 0px ${10}px 0px`
            width = `calc(50% - ${10 / 2}px)`
            break
        case 'goods-style--five':
            margin = `0px 0px ${10}px 0px`
            width = `calc(50% - ${10 / 2}px)`
            break
        default:
            margin = `10px 0`
            break
    }
    return { margin, width }
})
/**
 * @LastEditors: lexy
 * @description: 模式2调整边距
 */
const goodStyle2 = computed(() => {
    const margin = `0px 0px 10px 0px`
    return { margin }
})

watch(
    $props,
    () => {
        getGoodsList()
    },
    {
        immediate: true,
    },
)
/*
 *lifeCircle
 */
// onMounted(() => {
//     getGoodsList()
// })
/*
 *function
 */
const activeClass = (id: string) => {
    Object.assign($props.formData, { currentCategoryId: id })
}
function getGoodsList() {
    const { ponentType = 1, currentCategoryId = '', firstCatList = [], goods = [] } = $props.formData
    // 选择商品数量时对应显示预览效果
    if (ponentType === 1) {
        const item = firstCatList.find((i) => i.id === currentCategoryId)
        if (item && item.productNum !== undefined) {
            goodsList.value = Array.from({ length: item.productNum }, () => undefined)
        }
    } else if (ponentType === 2) {
        goodsList.value = goods
    }
}

const waterfallHeight = (i: number) => {
    if ($props.formData.listStyle === 'goods-style--five') {
        return { height: [5, -5][i % 2] + 240 + 'px' }
    }
    return {}
}
</script>

<template>
    <div class="goods__ponent-page">
        <div
            v-if="
                ($props.formData.ponentType === 1 && !($props.formData.firstCatList && $props.formData.firstCatList.length)) ||
                ($props.formData.ponentType === 2 && !$props.formData.goods.length)
            "
            class="no__goods-item"
        >
            <img :src="def_good_pic" />
        </div>
        <!-- 头部分类 s -->
        <div v-if="$props.formData.ponentType === 1 && $props.formData.firstCatList && $props.formData.firstCatList.length" class="tab__bar-box">
            <div class="con">
                <div
                    v-for="(item, idx) in $props.formData.firstCatList"
                    :key="idx"
                    :class="['class__item', item.id === $props.formData.currentCategoryId ? ['', '', 'class__new-active'] : '']"
                    @click="activeClass(item.id)"
                >
                    <span class="item__name">{{ item.name }}</span>
                </div>
            </div>
        </div>
        <!-- 头部分类 e -->
        <!-- 商品主体展示 s -->
        <!-- $props.formData.listStyle === ListStyle['goods-style--two'] && goodsList.length === 1 ? '' : $props.formData.listStyle -->
        <div
            v-if="
                ($props.formData.ponentType === 1 && $props.formData.firstCatList && $props.formData.firstCatList.length) ||
                ($props.formData.ponentType === 2 && $props.formData.goods.length)
            "
            class="goods"
            :class="$props.formData.listStyle === ListStyle['goods-style--two'] && goodsList.length === 1 ? '' : $props.formData.listStyle"
            :style="pageStyle"
        >
            <!-- 大图模式 -->
            <template v-if="$props.formData.listStyle === ListStyle['goods-style--one']">
                <div v-for="(item, idx) in goodsList" :key="idx" :class="['goods-item']">
                    <!-- tag -->
                    <coner :tag-show="$props.formData.showContent.tagShow" :tag-style="$props.formData.showContent.tagStyle"></coner>
                    <!-- tag -->
                    <!-- 大图模式 s -->
                    <div class="goods-item__large_box">
                        <div class="goods-item__large">
                            <img :class="[$props.formData.ponentType === 1 ? 'show__mall' : 'show__big']" :src="item.img || def_good_pic" />
                        </div>
                        <div class="goods-item__large_foot" style="padding: 0 7px">
                            <div class="goods-item__name" style="width: 100%">
                                {{ item ? item.productName : '商品名称' }}
                            </div>
                            <prices-line
                                :good="item"
                                :show-price="$props.formData.showPrice"
                                :is-preview="true"
                                :show-sales="$props.formData.showSales"
                                :button-style="$props.formData.showContent.buttonStyle"
                            />
                        </div>
                        <div class="goods-item__large_foot--placeholder-node"></div>
                    </div>
                </div>
            </template>
            <!-- 大图模式 -->
            <!-- 一行两个 goodStyle2 goodStyle-->
            <template v-else-if="$props.formData.listStyle === ListStyle['goods-style--two']">
                <div
                    v-for="(item, idx) in goodsList"
                    :key="idx"
                    :class="['goods-item']"
                    :style="{ ...(idx === $props.formData.goods.length - 1 ? goodStyle2 : goodStyle) }"
                >
                    <coner :tag-show="$props.formData.showContent.tagShow" :tag-style="$props.formData.showContent.tagStyle"></coner>
                    <div class="goods-item__icon">
                        <div class="ipic" style="border-radius: 10px 10px 0 0">
                            <img :class="[$props.formData.ponentType === 1 ? 'show__mall' : 'show__big']" :src="item.img || def_good_pic" />
                        </div>
                    </div>

                    <div class="goods-item__foot" style="padding: 0 7px">
                        <div class="goods-item__name">
                            {{ item ? item.productName : '商品名称一行两个' }}
                        </div>
                        <div class="goods-item__bottom">
                            <prices-line
                                :good="item"
                                :show-price="$props.formData.showPrice"
                                :is-preview="true"
                                :show-sales="$props.formData.showSales"
                                :button-style="$props.formData.showContent.buttonStyle"
                            />
                        </div>
                    </div>
                </div>
            </template>
            <!-- 一行两个 -->
            <!-- 详细列表 -->
            <template v-else-if="$props.formData.listStyle === ListStyle['goods-style--three']">
                <div
                    v-for="(item, idx) in goodsList"
                    :key="idx"
                    :class="['goods-item', 'goods-item-three_shadow']"
                    :style="{ ...(idx === $props.formData.goods.length - 1 ? goodStyle2 : goodStyle) }"
                >
                    <coner :tag-show="$props.formData.showContent.tagShow" :tag-style="$props.formData.showContent.tagStyle"></coner>
                    <div class="goods-item__three">
                        <div class="goods-item__three_img">
                            <img :src="item.img || def_good_pic" />
                        </div>
                    </div>

                    <div class="goods-item__foot" style="padding: 0 7px">
                        <div class="goods-item__name">
                            {{ item ? item.productName : '商品名称' }}
                        </div>
                        <div class="goods-item__bottom">
                            <prices-line
                                :good="item"
                                :show-price="$props.formData.showPrice"
                                :is-preview="true"
                                :show-sales="$props.formData.showSales"
                                :button-style="$props.formData.showContent.buttonStyle"
                            />
                        </div>
                    </div>
                </div>
            </template>
            <!-- 详细列表 -->
            <!-- 横向滑动 -->
            <template v-else-if="$props.formData.listStyle === ListStyle['goods-style--four']">
                <div
                    v-for="(item, idx) in goodsList"
                    :key="idx"
                    :class="['goods-item', 'goods-item-three_shadow']"
                    :style="{ ...(idx === $props.formData.goods.length - 1 ? goodStyle2 : goodStyle) }"
                >
                    <coner :tag-show="$props.formData.showContent.tagShow" :tag-style="$props.formData.showContent.tagStyle"></coner>
                    <div class="goods-item__four">
                        <div class="goods-item__four_img">
                            <img :src="item.img || def_good_pic" />
                        </div>
                    </div>

                    <div class="goods-item__foot" style="padding: 0 7px">
                        <div class="goods-item__name" style="font-weight: 600; font-size: 11px">
                            {{ item ? item.productName : '商品名称' }}
                        </div>
                        <div class="goods-item__bottom">
                            <prices-line
                                :good="item"
                                :show-price="$props.formData.showPrice"
                                :is-preview="true"
                                :show-sales="$props.formData.showSales"
                                :button-style="$props.formData.showContent.buttonStyle"
                            />
                        </div>
                    </div>
                </div>
            </template>
            <!-- 横向滑动 -->
            <!-- 瀑布 -->
            <template v-else>
                <div
                    v-for="(item, idx) in goodsList"
                    :key="idx"
                    :class="['goods-item']"
                    :style="{ ...waterfallHeight(idx), ...(idx === $props.formData.goods.length - 1 ? goodStyle2 : goodStyle) }"
                >
                    <coner :tag-show="$props.formData.showContent.tagShow" :tag-style="$props.formData.showContent.tagStyle"></coner>
                    <div class="goods-item__icon">
                        <div class="ipic" style="border-radius: 10px 10px 0 0">
                            <img :class="[$props.formData.ponentType === 1 ? 'show__mall' : 'show__big']" :src="item.img || def_good_pic" />
                        </div>
                    </div>

                    <div class="goods-item__foot" style="padding: 0 7px">
                        <div class="goods-item__name">
                            {{ item ? item.productName : '商品名称' }}
                        </div>
                        <div class="goods-item__bottom" style="margin-top: 10px">
                            <prices-line
                                :good="item"
                                :show-price="!!$props.formData.showPrice"
                                :is-preview="true"
                                :show-sales="$props.formData.showSales"
                                :button-style="$props.formData.showContent.buttonStyle"
                            />
                        </div>
                    </div>
                </div>
            </template>
            <!-- 瀑布 -->
        </div>
        <!-- 商品主体展示 e -->
    </div>
</template>

<style scoped lang="scss">
@import '@/assets/css/decoration/goods.scss';

$color-red: #fa3534;

.goods__ponent-page {
    .tab__bar-box {
        height: 50px;
        width: 100%;
        overflow: hidden;

        .con {
            width: auto;
            height: 60px;
        }

        .class__item {
            display: inline-flex;
            align-items: center;
            position: relative;
            color: #6b6b6b;
            font-size: 12px;
            padding: 0px 12px;
            padding-top: 15px;
            cursor: pointer;
            padding-bottom: 4px;
            span {
                display: inline-block;
                border-bottom: 3px solid transparent;
                border-radius: 1px;
            }
            &::before {
                content: '';
                position: absolute;
                right: 0;
                width: 1px;
                height: 14px;
                background: #ccc;
            }
        }

        .class__new-active {
            color: $color-red;
            font-size: 12px;
            font-weight: 600;
            span {
                border-color: $color-red;
            }
        }

        .first__class {
            padding-left: 0;
            background-size: calc(100% - 11px) 8px;
            background-position: 0px 1.16em;
        }
    }

    .goods-item__icon {
        width: 100%;
        height: 160px;
        .ipic {
            display: inline-block;
            width: 100%;
            height: 100%;
            background-color: rgba(233, 247, 253, 1);
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 10px;
            .show__mall {
                display: inline-block;
                width: 44px;
                height: 46px;
            }

            .show__big {
                display: inline-block;
                width: 100%;
                height: 100%;
            }
        }
    }

    .goods-style--three {
        .goods-item__icon {
            height: 128px;
            width: 128px;
            margin-right: 10px;
            flex: none;

            .ipic {
                height: 128px;
            }
        }
    }

    .spellpre__goods--delivery {
        color: #a3a3a3;
        font-size: 12px;
        font-weight: 400;
        padding-top: 10px;
    }
}
</style>
