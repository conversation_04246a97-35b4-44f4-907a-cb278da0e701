<template>
    <div class="price-line">
        <view class="price-line__left">
            <!-- 现价区域 -->
            <div class="price-line__main">
                <span class="price-line__current">
                    <span class="price-line__symbol">¥</span>
                    <template v-if="isMoreThanTenThousand">
                        <span class="price-line__value">{{ tenThousandPrice }}万</span>
                    </template>
                    <template v-else>
                        <span class="price-line__integer">{{ priceInteger }}</span>
                        <span v-if="priceDecimal" class="price-line__decimal">.{{ priceDecimal }}</span>
                    </template>
                </span>
                <!-- 价格区间标识 -->
                <span v-if="showPriceRange" class="price-line__range-tag">起</span>
            </div>

            <!-- 划线价 -->
            <div v-if="showPrice && computedLinePrice" class="price-line__old">
                <span class="price-line__old-symbol">¥</span>
                <span class="price-line__old-value">{{ computedLinePrice }}</span>
            </div>
        </view>

        <!-- 销量 -->
        <div v-if="!!showSales" class="price-line__sales">已售{{ formatSales(salesVolume) }}</div>
        <shopping-button v-else :button-style="props.buttonStyle"></shopping-button>
    </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import type { PropType } from 'vue'
import ShoppingButton from './shoppingButton.vue'
import useConvert from '@/composables/useConvert'

interface PriceItem {
    salePrices?: string[]
    prices?: string[]
    salesVolume?: number
}

const props = defineProps({
    // 商品数据
    good: {
        type: Object as PropType<PriceItem>,
        default: () => ({}),
    },
    // 是否显示划线价
    showPrice: {
        type: Boolean,
        default: true,
    },
    // 是否显示销量
    showSales: {
        type: Boolean,
        default: true,
    },
    // 是否为预览模式
    isPreview: {
        type: Boolean,
        default: false,
    },
    buttonStyle: {
        type: Number,
        default: 1,
    },
})

const { divTenThousand } = useConvert()

// 处理价格显示
const currentPrice = computed(() => {
    if (props.isPreview) return '99.00'
    if (!props.good?.salePrices?.length) return '0'
    return Math.min(...props.good.salePrices.map((p) => Number(p))).toFixed(2)
})

// 是否显示价格区间标识
const showPriceRange = computed(() => {
    if (props.isPreview) return true
    return props.good && props.good.salePrices && props.good.salePrices?.length > 1
})

// 处理价格整数和小数部分
const priceInteger = computed(() => currentPrice.value.split('.')[0])
const priceDecimal = computed(() => currentPrice.value.split('.')[1])

// 处理万元以上价格显示
const isMoreThanTenThousand = computed(() => Number(priceInteger.value) > 10000)
const tenThousandPrice = computed(() => {
    if (!isMoreThanTenThousand.value) return ''
    return divTenThousand(currentPrice.value)
        .toFixed(2)
        ?.replace(/\.?0+$/, '')
})

// 处理划线价
const computedLinePrice = computed(() => {
    if (props.isPreview) return '199.00'
    if (!props.good?.prices?.length) return ''
    const maxPrice = Math.max(...props.good.prices.map((p) => Number(p)))
    return maxPrice > 10000 ? divTenThousand(maxPrice) + '万' : maxPrice.toFixed(2)
})

// 格式化销量
const formatSales = (value?: number) => {
    if (props.isPreview) return '999+'
    const num = Number(value || 0)
    if (num >= 10000) {
        return (num / 10000).toFixed(1) + '万'
    }
    return num.toString()
}

const salesVolume = computed(() => props.good?.salesVolume || 0)
</script>

<style lang="scss" scoped>
.price-line {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    width: 100%;
    height: 26px;

    &__left {
        display: flex;
        align-items: flex-end;
        gap: 2px;
        flex: 1;
        min-width: 0; // 防止内容溢出
    }

    &__main {
        display: flex;
        align-items: flex-end;
        line-height: 24px;
    }

    &__current {
        color: #f53f3f;
        font-weight: 500;
        line-height: 24px;
    }

    &__symbol {
        font-size: 12px;
    }

    &__integer {
        font-size: 16px;
    }

    &__decimal {
        font-size: 12px;
    }

    &__range-tag {
        font-size: 11px;
        color: #f53f3f;
        margin-left: 2px;
        line-height: 24px;
    }

    &__old {
        color: #86909c;
        font-size: 11px;
        line-height: 24px;
        text-decoration: line-through;
    }

    &__sales {
        color: #6b7785;
        font-size: 12px;
        text-align: right;
        line-height: 24px;
        flex-shrink: 0;
    }
}

// 预览模式样式调整
.prediv-mode {
    .price-line {
        transform: scale(0.5);
        transform-origin: left bottom;
    }
}
</style>
