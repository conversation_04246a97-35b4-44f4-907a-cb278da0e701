/*
 * @description: 整合组件初始化数据
 * @Author: lexy
 * @Date: 2022-08-17 17:17:21
 * @LastEditors: lexy
 * @LastEditTime: 2024-02-03 18:01:12
 */
import classification from '../classification/classification'
import swiper from '../swiper/swiper'

const defaultData = {
    swiper,
    classification,
}
const defaultDataArr = Object.values(defaultData)
type FormDataType = typeof defaultDataArr[number]

export interface ComponentItem {
    icon: string
    id?: string
    label: string
    value: keyof typeof defaultData
    formData?: FormDataType
    showType?: boolean
    type?: number
}
export default defaultData
