/*
 * @description: 分类默认数据
 * @Author: lexy
 * @Date: 2022-10-15 15:25:02
 * @LastEditors: lexy
 * @LastEditTime: 2023-06-13 11:19:11
 */
export type DeCategoryType = {
    style: number
    categoryType: 'all' | 'selected'
    categoryList: DeCategoryItem[]
}
export type DeCategoryItem = {
    id: string
    name: string
    productNum: number
}
export interface ApiCategoryData {
    id: string
    name: string
    categoryImg?: string
    children: ApiCategoryData[]
}
export type CommodityItem = {
    name: string
    pic: string
    id: string
    salePrices: string[]
}
export default {
    style: 1,
    categoryType: 'all',
    categoryList: [],
}
