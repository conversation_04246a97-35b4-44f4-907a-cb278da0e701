<script setup lang="ts">
import { ref, PropType, onMounted, watch } from 'vue'
import bannerFormData from './cubeBox'
import type { ShowCubeListWrap } from './cubeBox'
const empty_img = 'https://devoss.chongyoulingxi.com/system-front/mobile/def_commodity.png'
/*
 *variable
 */
const $props = defineProps({
    formData: {
        type: Object as PropType<any>,
        default: bannerFormData,
    },
})
const pageMarginStyle = ref({
    width: 0,
    height: 0,
    margin: 0,
})
const showCubeListWrap = ref<ShowCubeListWrap[]>([])
const showCubeList = ref(false)
watch(
    $props,
    () => {
        drawCube()
    },
    { deep: true },
)
/*
 *lifeCircle
 */
onMounted(() => {
    if ($props.formData) {
        showCubeList.value = showTxt()
        if (showCubeList.value) {
            drawCube()
        }
    }
})
/*
 *function
 */

function drawCube() {
    if ($props.formData) {
        showCubeList.value = showTxt()
        const perviewLayoutWidth = 370
        const item = $props.formData
        const layoutWidth = item.layoutWidth
        const layoutHeight = item.layoutHeight
        // perviewLayoutWidth = perviewLayoutWidth - item.pageMargin * 2;
        const wrapWith = perviewLayoutWidth + item.borderWidth - item.pageMargin * 2
        const styleWidth = wrapWith / layoutWidth
        const styleHeight = layoutHeight !== 1 ? perviewLayoutWidth / layoutHeight : styleWidth
        drawCubeWrap(styleWidth, styleHeight, wrapWith)
    }
}
function showTxt() {
    return !!$props.formData.subEntry.filter((item) => !!item.img || !!item.goods?.length).length
}
function drawCubeWrap(divWidth: number, divHeight: number, wrapWith: number) {
    const item = $props.formData
    const subEntry = item.subEntry
    showCubeListWrap.value = []
    let maxY = 0,
        maxIndex = 0,
        maxHeght = 0
    if (subEntry.length) {
        for (let i = 0; i < subEntry.length; i++) {
            const a = subEntry[i]
            const goodsWidth = divWidth ? divWidth * a.width - item.borderWidth - 24 : 0
            const goodsHeight = divHeight ? divHeight * a.height - item.borderWidth - 24 - 28 : 0
            const { num = 1, ...extraGoodStyle } = getGoodItemStyle(goodsWidth, goodsHeight)
            const coverDiv = {
                dataType: a.dataType || 'Image',
                top: a.y * divHeight + 'px',
                left: a.x * divWidth + item.pageMargin + 'px',
                width: divWidth * a.width - item.borderWidth + 'px',
                height: divHeight * a.height - item.borderWidth + 'px',
                paddingTop: (divHeight * a.height) / 2 + 'px',
                img: a[`img`] ? a[`img`] : '',
                borderWidth: item.borderWidth / 2 + 'px',
                ...(a.dataType === 'Image'
                    ? {}
                    : {
                          mainTitle: a.mainTitle || '',
                          subTitle: a.subTitle || '',
                          background: a.background || a.backgroundColor || '#fff',
                          goods: a.goods?.slice(0, num) || [],
                          goodsWidth,
                          goodsHeight,
                          goodStyle: extraGoodStyle,
                      }),
            }
            if (maxY <= a.y) {
                maxY = a.y
                maxIndex = i
            }
            showCubeListWrap.value.push(coverDiv)
        }

        maxHeght = maxY + subEntry[maxIndex].height < item.layoutHeight ? maxY + subEntry[maxIndex].height : item.layoutHeight
        pageMarginStyle.value = {
            width: wrapWith,
            height: divHeight * maxHeght,
            margin: -item.borderWidth / 2,
        }
    }
}

const getGoodItemStyle = (boxWidth: number, boxHeight: number) => {
    const radtio = boxWidth / boxHeight
    if (boxWidth > 100 || boxHeight > 100) {
        if (radtio > 1) {
            let goodsNum = Math.ceil(radtio) || 1
            // 宽>高 横行平铺
            return {
                height: boxHeight + 'px',
                num: goodsNum,
            }
        }
        if (radtio > 0) {
            if (radtio < 8 / 9) {
                let goodsNum = Math.ceil(1 / radtio) || 1
                // 宽<高 纵向平铺
                return {
                    width: boxWidth + 'px',
                    num: goodsNum,
                }
            }
        }
    }
    return {
        width: '100%',
        height: '100%',
    }
}
</script>

<template>
    <div class="rc-design-react-preview rc-design-component-default-preview">
        <div v-if="!showCubeList" class="rc-design-component-default-preview__text">点击编辑魔方</div>
        <div v-else class="cap-cube-wrap">
            <div
                class="cap-cube"
                :style="{
                    width: pageMarginStyle.width + 'px',
                    height: pageMarginStyle.height + 'px',
                    margin: pageMarginStyle.margin + 'px',
                }"
            >
                <div
                    v-for="(item, index) in showCubeListWrap"
                    :key="index"
                    :class="{
                        'cap-cube__item': true,
                        'cap-cube__goods': item.dataType === 'Good',
                    }"
                    :style="{
                        width: item.width,
                        height: item.height,
                        top: item.top,
                        left: item.left,
                        margin: item.borderWidth,
                        ...(item.dataType === 'Image' ? { backgroundImage: `url(${item.img})` } : { background: item.background, padding: '12px' }),
                    }"
                >
                    <template v-if="item.dataType === 'Image'">
                        <img v-if="item.img" class="cap-cube__table-image cap-cube__table-image--invisible" :src="item.img" />
                    </template>
                    <template v-else>
                        <div class="cap-cube__title-group">
                            <strong>{{ item.mainTitle }}</strong>
                            <span>{{ item.subTitle }}</span>
                        </div>
                        <div
                            class="cap-cube__goods-group"
                            :style="{
                                width: `${item.goodsWidth}px`,
                                height: `${item.goodsHeight}px`,
                                'flex-direction': item.goodsWidth > item.goodsHeight ? 'row' : 'column',
                            }"
                        >
                            <div
                                v-for="({ pic, productId }, i) in item.goods || []"
                                :key="`${productId}=>${i}`"
                                class="cap-cube__good"
                                :style="{
                                    'background-color': '#fff',
                                    'background-image': `url(${pic || empty_img})`,
                                    ...item.goodStyle,
                                }"
                            />
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scope>
@import '@/assets/css/decoration/cubeBox.scss';
</style>
