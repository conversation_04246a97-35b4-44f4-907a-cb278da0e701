<!--
 * @description: 魔方配置
 * @Author: lexy
 * @Date: 2022-08-15 16:19:54
 * @LastEditors: lexy
 * @LastEditTime: 2024-02-04 13:29:52
-->
<script setup lang="ts">
import { CircleClose } from '@element-plus/icons-vue'
import { ref, PropType, computed, watch, onMounted } from 'vue'
import { useDecorationStore } from '@/store/modules/decoration'
// import QUpload from '@/components/q-upload/q-upload.vue'
import { useVModel } from '@vueuse/core'
// import LinkSelect from '@/components/link-select/link-select.vue'
import templateList from './template'
import bannerFormData from './cubeBox'
import type { IBanners, ShowCubeList, ShowCubeListWrap } from './cubeBox'

// 选择素材 e
import selectMaterial from '@/views/material/selectMaterial.vue'
const empty_img = 'https://devoss.chongyoulingxi.com/system-front/mobile/def_commodity.png'

const dialogVisible = ref(false)
const selectMaterialFn = (val: boolean) => {
    dialogVisible.value = val
}
// const parameterId = ref('')
const buttonlFn = () => {
    dialogVisible.value = true
}
// @cropped-file-change="" 裁剪后返回的单个素材
// @checked-file-lists=""  选中素材返回的素材合集
const croppedFileChange = (val: any) => {
    const index = activeShowCubeListWrap.value
    activeItem.value[`subEntry`][index].img = val
    showCubeListWrap.value[index].img = val
}
const checkedFileLists = (val: any) => {
    const index = activeShowCubeListWrap.value
    activeItem.value[`subEntry`][index].img = val
    showCubeListWrap.value[index].img = val
}
// 选择素材 d
/*
 *variable
 */
const $props = defineProps({
    formData: {
        type: Object as PropType<any>,
        default() {
            return bannerFormData
        },
    },
})
const $emit = defineEmits(['update:formData'])
const activeItem = useVModel($props, 'formData', $emit)
const $decorationStore = useDecorationStore()
// 模板选择列表
const chooseTemplate = templateList
// 展示格子
const showCubeList = ref<Array<ShowCubeList[]>>([])
// 格子蒙层
const showCubeListWrap = ref<ShowCubeListWrap[]>([])
// 蒙层下标
const activeShowCubeListWrap = ref(0)
// 鼠标移动到密度上下标记录
const mouseOverSeleted = ref<number | null>(null)
// 判断密度是是不是开始选取
const isSeletedMidu = ref(false)
// 开始选取时候的坐标
const coordinates = ref({
    //x 行
    rowStart: 0,
    //y  列
    columnStart: 0,
})
// 对于选中块结算单位
const ranksNumber = ref({
    rowCount: 1,
    columnCount: 1,
})
// 初始化后单个模块的宽度
const styleWidth = ref(0)
// 初始化后单个模块的高度
const styleHeight = ref(0)
const options = [
    // 尺寸属性
    {
        value: '4x4',
        label: '4x4',
    },
    {
        value: '5x5',
        label: '5x5',
    },
    {
        value: '6x6',
        label: '6x6',
    },
    {
        value: '7x7',
        label: '7x7',
    },
]
const miduValue = ref('4x4')
const activeComIndex = computed(() => {
    // 针对多个魔方处理缓存数据(当前组件下标)
    return $decorationStore.activeComIndex
})
// 切换相同组件清除缓存
watch(activeComIndex, () => {
    mouseOverSeleted.value = null
    activeShowCubeListWrap.value = 0
    showCubeListWrap.value = []
    showCubeList.value = []
    isSeletedMidu.value = false
    coordinates.value = {
        rowStart: 0,
        columnStart: 0,
    }
    ranksNumber.value = {
        rowCount: 0,
        columnCount: 0,
    }
})
watch(
    activeItem,
    () => {
        drawCube(false)
        if (activeItem.value.showMethod === 7) {
            miduValue.value = `${activeItem.value.layoutWidth}x${activeItem.value.layoutHeight}`
        }
    },
    { deep: true },
)
/*
 *lifeCircle
 */
onMounted(() => {
    if (activeItem.value.showMethod === 7) {
        miduValue.value = `${activeItem.value.layoutWidth}x${activeItem.value.layoutHeight}`
    }
    drawCube()
})
/*
 *function
 */
/**
 * @LastEditors: lexy
 * @description: 选择魔方密度转变
 * @returns {*}
 */
const handleChangeMidu = () => {
    const arr = miduValue.value.split('x')
    activeItem.value[`layoutWidth`] = Number(arr[0])
    activeItem.value[`layoutHeight`] = Number(arr[1])
    activeItem.value[`subEntry`] = []
    mouseOverSeleted.value = null
    activeShowCubeListWrap.value = 0
    showCubeListWrap.value = []
    showCubeList.value = []
    isSeletedMidu.value = false
    drawCube(true)
}
/**
 * @LastEditors: lexy
 * @description: 选择模块属性
 * @param {CubeBoxFormData} item
 * @param {number} index
 */
const handleChangeTemplate = (item: typeof templateList[0], index: number) => {
    mouseOverSeleted.value = null
    activeShowCubeListWrap.value = 0
    showCubeListWrap.value = []
    showCubeList.value = []
    isSeletedMidu.value = false
    activeItem.value.showMethod = index
    if (index === 7) {
        miduValue.value = '4x4'
        handleChangeMidu()
    } else {
        activeItem.value[`layoutWidth`] = item[`layoutWidth`]
        activeItem.value[`layoutHeight`] = item[`layoutHeight`]
        if (!activeItem.value[`subEntry`].length) {
            activeItem.value[`subEntry`] = item[`subEntry`]
        } else {
            const dist =
                item[`subEntry`]!.length > activeItem.value[`subEntry`].length ? activeItem.value[`subEntry`].length : item[`subEntry`]!.length
            for (let i = 0; i < dist; i++) {
                const item1 = item[`subEntry`]![i]
                item1[`img`] = activeItem.value[`subEntry`][i][`img`] || ''
                item1[`link`] = activeItem.value[`subEntry`][i][`link`]
                item1[`linkName`] = activeItem.value[`subEntry`][i][`linkName`]
            }
            activeItem.value[`subEntry`] = item[`subEntry`]
        }
    }
}
/**
 * @LastEditors: lexy
 * @description: 魔方密度选择后产生后针对魔方绘制画出对应的魔方
 * @param {number} index
 */
const handleShowCubeListWrap = (index: number) => {
    activeShowCubeListWrap.value = index
}
/**
 * @LastEditors: lexy
 * @description: 点击任意方块度时候判断该方块是起始位置还是结束位置
 */
const handleSelectMidu = (index1: number, index2: number) => {
    const selected = showCubeList.value[index1][index2].selected
    if (isSeletedMidu.value && !selected) {
        return
    }
    if (selected) {
        //再次确认
        let { rowStart, columnStart } = getItemStart()
        mouseOverSeleted.value = null
        isSeletedMidu.value = false
        let rowEnd = index1
        let columnEnd = index2
        // eslint-disable-next-line no-self-assign
        rowStart > rowEnd ? ([rowStart, rowEnd] = [rowEnd, rowStart]) : ([rowStart, rowEnd] = [rowStart, rowEnd])
        //如果开始列大于结束列，则开始列用结束列，结束列用开始列
        // eslint-disable-next-line no-self-assign
        columnStart > columnEnd ? ([columnStart, columnEnd] = [columnEnd, columnStart]) : ([columnStart, columnEnd] = [columnStart, columnEnd])
        activeItem.value.subEntry.push({
            dataType: 'Image',
            x: rowStart,
            y: columnStart,
            width: ranksNumber.value.rowCount,
            height: ranksNumber.value.columnCount,
            img: '',
            link: {
                id: null,
                type: null,
                name: '',
                url: '',
                append: '',
            },
            linkName: '',
        })
        showCubeList.value.forEach((item1, index3) => {
            //所有归零
            item1.forEach((item2, index4) => {
                showCubeList.value[index3][index4].start = 0
            })
        })
        showCubeList.value.forEach((t, i) => {
            if (t.length) {
                t.forEach((t1, j) => {
                    if (t1.selected === true) {
                        showCubeList.value[i][j][`finish`] = true
                    }
                })
            }
        })
        ranksNumber.value = {
            rowCount: 1,
            columnCount: 1,
        }
    } else {
        mouseOverSeleted.value = null
        showCubeList.value[index1][index2].selected = true
        showCubeList.value[index1][index2].start = 1
        isSeletedMidu.value = true
        coordinates.value = {
            rowStart: index1,
            columnStart: index2,
        }
    }
}
/**
 * @LastEditors: lexy
 * @description: 鼠标在任意方块的上滑动计算方块队列
 * @param {number} index1
 * @param {number} index2
 */
const handleOnMouseOver = (index1: number, index2: number) => {
    showCubeList.value.forEach((item1, index3) => {
        item1.forEach((item2, index4) => {
            if (!item2.finish) {
                showCubeList.value[index3][index4].selected = false
            }
        })
    })
    if (isSeletedMidu.value) {
        //鼠标移动
        let rowStart = 0,
            columnStart = 0
        showCubeList.value.forEach((item1, index3) => {
            item1.forEach((item2, index4) => {
                if (item2.start) {
                    rowStart = index3
                    columnStart = index4
                }
            })
        })
        let rowEnd = index1
        let columnEnd = index2
        //如果开始行大于结束行，则开始行用结束行，结束行用开始行
        // eslint-disable-next-line no-self-assign
        rowStart > rowEnd ? ([rowStart, rowEnd] = [rowEnd, rowStart]) : ([rowStart, rowEnd] = [rowStart, rowEnd])
        //如果开始列大于结束列，则开始列用结束列，结束列用开始列
        // eslint-disable-next-line no-self-assign
        columnStart > columnEnd ? ([columnStart, columnEnd] = [columnEnd, columnStart]) : ([columnStart, columnEnd] = [columnStart, columnEnd])

        let rowCount = 0 //总行数
        const columnCount = [] //总列数
        let isAdd = true
        for (let forRowStart = rowStart; forRowStart <= rowEnd; forRowStart++) {
            rowCount++
            //遍历列
            for (let i = columnStart; i <= columnEnd; i++) {
                //当前行列坐标
                // const currentCoordinates = forRowStart + ":" + i;
                //检测当前的模块是否被占用，如果被占用，那么整个都不能选择
                if (showCubeList.value[forRowStart][i].finish) {
                    isAdd = false
                    break
                }
                if (!inArray(i, columnCount)) columnCount.push(i)
                showCubeList.value[forRowStart][i].selected = true
            }
        }
        if (!isAdd) {
            let rowStart1 = 0,
                columnStart1 = 0
            showCubeList.value.forEach((item1, index1) => {
                item1.forEach((item2, index2) => {
                    if (!item2.finish) {
                        showCubeList.value[index1][index2].selected = false
                    }
                    if (item2.start) {
                        rowStart1 = index1
                        columnStart1 = index2
                    }
                })
            })
            showCubeList.value[rowStart1][columnStart1].selected = true
        }
        ranksNumber.value = {
            rowCount: rowCount,
            columnCount: columnCount.length,
        }
    }
}
/**
 * @LastEditors: lexy
 * @description: 点击魔方块的showCubeListWrap下标
 * @param {number} index
 */
const handleOnMouseOverSeleted = (index: number) => {
    mouseOverSeleted.value = index
}
/**
 * @LastEditors: lexy
 * @description: 监听该模快是否被删除
 */
const handleDelSelected = (i: number) => {
    const { x, y, height, width } = activeItem.value['subEntry']

    showCubeList.value[x][y].selected = false
    showCubeList.value[x][y].finish = 0
    showCubeList.value[x][y].start = 0
    for (let j = 1; j < width; j++) {
        showCubeList.value[x + j][y].selected = false
        showCubeList.value[x + j][y].finish = 0
        showCubeList.value[x][y].start = 0
    }
    for (let l = 1; l < height; l++) {
        showCubeList.value[x][y + l].selected = false
        showCubeList.value[x][y + l].finish = 0
        showCubeList.value[x][y].start = 0
    }
    showCubeList.value.forEach((item1, index3) => {
        //清除选中的
        item1.forEach((item2, index4) => {
            if (!item2.finish) {
                showCubeList.value[index3][index4].selected = false
            }
            showCubeList.value[index3][index4].start = 0
            showCubeList.value[index3][index4].finish = 0
        })
    })
    mouseOverSeleted.value = null
    isSeletedMidu.value = false
    activeShowCubeListWrap.value = 0
    ranksNumber.value = {
        rowCount: 1,
        columnCount: 1,
    }
    activeItem.value[`subEntry`].splice(i, 1)
}
function getItemStart() {
    let rowStart = 0,
        columnStart = 0
    showCubeList.value.forEach((item1, index3) => {
        item1.forEach((item2, index4) => {
            if (item2.start) {
                rowStart = index3
                columnStart = index4
            }
        })
    })
    return { rowStart, columnStart }
}
/**
 * @LastEditors: lexy
 * @description: 魔方密度选择后产生相对应魔方数据
 * @param {boolean} isChangeMidu
 */
function drawCube(isChangeMidu = false) {
    if (activeItem.value) {
        // this.formData = Object.assign({}, this.activeItem);
        const perviewLayoutWidth = 322
        // if(this.showCubeList)
        if (showCubeList.value.length && activeItem.value.showMethod === 7 && !isChangeMidu) {
            //拦截密度选择截取
        } else {
            showCubeList.value = []
            const item = activeItem.value
            const layoutWidth = item.layoutWidth
            const layoutHeight = item.layoutHeight
            //画魔方
            const drawStyleWidth = Math.ceil(perviewLayoutWidth / layoutWidth)
            const drawStyleHeight = layoutHeight !== 1 ? Math.ceil(perviewLayoutWidth / layoutHeight) : drawStyleWidth
            for (let i = 0; i < layoutWidth; i++) {
                const ul = []
                for (let j = 0; j < layoutHeight; j++) {
                    const li = {
                        style: {
                            width: drawStyleWidth + 'px',
                            height: drawStyleHeight + 'px',
                        },
                        coordinates: i + ':' + j,
                        selected: false,
                        finish: 0,
                        start: 0,
                    }
                    ul.push(li)
                }
                showCubeList.value.push(ul)
            }
            styleWidth.value = drawStyleWidth
            styleHeight.value = drawStyleHeight
        }
        drawCubeWrap(styleWidth.value, styleHeight.value)
    }
}
/**
 * @LastEditors: lexy
 * @description: 魔方密度选择后产生后针对对应魔方数据绘画出对应的魔方
 * @param {number} divWidth
 * @param {number} divHeight
 */
function drawCubeWrap(divWidth: number, divHeight: number) {
    const item = activeItem.value
    const subEntry = item.subEntry
    const showMethod = activeItem.value.showMethod
    showCubeListWrap.value = []
    if (subEntry.length) {
        for (let i = 0; i < subEntry.length; i++) {
            const { dataType = 'Image', y, x, height, width, img, goods = [], mainTitle = '', subTitle = '', background = '' } = subEntry[i] // 数据获取
            const goodsWidth = divWidth ? divWidth * width - item.borderWidth - 24 : 0
            const goodsHeight = divHeight ? divHeight * height - item.borderWidth - 24 - 28 : 0
            const { num = 1, ...extraGoodStyle } = getGoodItemStyle(goodsWidth, goodsHeight)
            const coverDiv = {
                top: y * divHeight + 'px',
                left: x * divWidth + 'px',
                width: divWidth * width + 'px',
                height: divHeight * height + 'px',
                paddingTop: (divHeight * height) / 2 + 'px',
                img: img || '',
                text: '',
                borderWidth: '',
                dataType,
                ...(dataType === 'Image'
                    ? {}
                    : {
                          mainTitle,
                          subTitle,
                          background,
                          goods: goods?.slice(0, num) || [],
                          goodsWidth,
                          goodsHeight,
                          goodStyle: extraGoodStyle,
                      }),
            }
            showCubeList.value[x][y].selected = true
            showCubeList.value[x][y].finish = 1
            for (let j = 1; j < width; j++) {
                showCubeList.value[x + j][y].selected = true
                showCubeList.value[x + j][y].finish = 1
            }
            for (let l = 1; l < height; l++) {
                showCubeList.value[x][y + l].selected = true
                showCubeList.value[x][y + l].finish = 1
            }
            if (showMethod === 0 || showMethod === 1 || showMethod === 2) {
                coverDiv[`text`] = '宽度' + Math.ceil(750 / item.layoutWidth) + '像素'
            } else {
                coverDiv[`text`] = Math.ceil((750 / item.layoutWidth) * width) + '*' + Math.ceil((750 / item.layoutHeight) * height) + '或同等比例'
            }
            showCubeListWrap.value.push(coverDiv)
        }
    }
}
function inArray(index: number, arr: number[]) {
    let i = 0
    const n = arr.length
    for (; i < n; ++i) if (arr[i] === index) return true
    return false
}
/**
 * 修改数据源类型
 * @param tab   当前选中tab
 * @param event 事件
 */
const handleChangeDataType = (tab: TabsPaneContext) => {
    const { subEntry } = activeItem.value
    var temp: IBanners = {
        ...subEntry[activeShowCubeListWrap.value],
    }
    if (tab.paneName === 'Good') {
        temp = {
            ...temp,
            dataType: 'Good',
            goods: Array.from({ length: temp.height < 100 ? 2 : 4 }, (v, i) => ({
                id: `${i + 1}`,
                productId: `${i + 1}`,
                shopId: 'temp-' + i,
                pic: empty_img,
                platformCategoryFirstId: '',
                salePrices: ['19.9'],
                prices: ['29.9'],
                productName: '商品名称',
            })),
            mainTitle: '',
            subTitle: '',
            backgroundColor: '',
            background: '',
        }
    } else {
        temp = {
            ...temp,
            dataType: 'Image',
            link: {
                id: '',
                type: 0,
                name: '',
                url: '',
                append: 0,
            },
            linkName: '',
        }
    }

    activeItem.value['subEntry'][activeShowCubeListWrap.value] = temp
}
const getGoodItemStyle = (boxWidth: number, boxHeight: number) => {
    const radtio = boxWidth / boxHeight
    if (boxWidth > 100 || boxHeight > 100) {
        if (radtio > 1) {
            let goodsNum = Math.ceil(radtio) || 1
            // 宽>高 横行平铺
            return {
                height: boxHeight + 'px',
                num: goodsNum,
            }
        }
        if (radtio > 0) {
            if (radtio < 8 / 9) {
                let goodsNum = Math.ceil(1 / radtio) || 1
                // 宽<高 纵向平铺
                return {
                    width: boxWidth + 'px',
                    num: goodsNum,
                }
            }
        }
    }
    return {
        width: '100%',
        height: '100%',
    }
}
</script>

<template>
    <div clas="zent-design-editor-item">
        <div class="zent-design-editor__control-group">
            <div class="zent-design-editor__control-group-container">
                <div class="zent-design-editor__control-group-label zent-design-editor__control-group-label--top" title="选择模版">模版</div>
                <div class="zent-design-editor__control-group-control">
                    <div>
                        <div
                            v-for="(item, index4) in chooseTemplate"
                            :key="`template${index4}`"
                            class="rc-design-select-templates"
                            :class="{ active: index4 === formData.showMethod }"
                            :data-length="index4 !== 7 ? item[`subEntry`]?.length : 0"
                            @click="handleChangeTemplate(item, Number(index4))"
                        >
                            <div class="rc-design-select-templates__image-block">
                                <img :src="item.img" alt="temp" />
                            </div>
                            <div class="rc-design-select-templates__title">
                                {{ item.title }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div v-if="formData.showMethod === 7" class="zent-design-editor__control-group">
            <div class="zent-design-editor__control-group-container">
                <div class="zent-design-editor__control-group-label">
                    <!-- react-text: 2624 -->
                    密度:
                    <!-- /react-text -->
                </div>
                <div class="zent-design-editor__control-group-control">
                    <div class="zent-popover-wrapper zent-select" style="display: inline-block">
                        <el-select v-model="miduValue" placeholder="请选择" style="width: 100px" @change="handleChangeMidu">
                            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
                        </el-select>
                    </div>
                </div>
            </div>
        </div>
        <div class="zent-design-editor__control-group">
            <div class="zent-design-editor__control-group-container">
                <div class="zent-design-editor__control-group-label zent-design-editor__control-group-label--top">
                    <!-- react-text: 2907 -->
                    布局:
                    <!-- /react-text -->
                </div>
                <div class="zent-design-editor__control-group-control">
                    <div class="rc-design-component-cube clearfix">
                        <ul v-for="(item, index1) in showCubeList" :key="index1" class="cube-row">
                            <li
                                v-for="(item1, index2) in item"
                                :ref="`cube-item-${index1}-${index2}`"
                                :key="`cube-item-${index2}`"
                                class="cube-item"
                                :class="{ 'item-selected': item1.selected }"
                                :style="{
                                    width: item1.style.width,
                                    height: item1.style.height,
                                }"
                                :data-coordinates="item1.coordinates"
                                :data-ref="`cube-item-${index1}-${index2}`"
                                :data-selected="item1.selected ? 1 : 0"
                                :data-start="item1.start"
                                @click.stop="handleSelectMidu(index1, index2)"
                                @mouseover="handleOnMouseOver(index1, index2)"
                            >
                                <span v-if="!item1.selected" class="plus-icon" :style="{ 'line-height': item1.style.height }">+</span>
                            </li>
                        </ul>
                        <div
                            v-for="(item, index3) in showCubeListWrap"
                            :key="`cube-selected-${index3}`"
                            class="cube-selected"
                            :class="{
                                'cube-selected-click': activeShowCubeListWrap === index3,
                                'cap-cube__goods': item.dataType === 'Good',
                            }"
                            :style="{
                                width: item.width,
                                height: item.height,
                                top: item.top,
                                left: item.left,
                            }"
                            @click.stop="handleShowCubeListWrap(index3)"
                            @mouseover.stop="handleOnMouseOverSeleted(index3)"
                        >
                            <el-icon
                                v-if="activeItem.showMethod === 7 && mouseOverSeleted === index3 && activeShowCubeListWrap === index3"
                                class="rc-design-editor-card-item-delete el-icon-error"
                                @click.stop="handleDelSelected(index3)"
                            >
                                <CircleClose />
                            </el-icon>
                            <img v-if="item.dataType === 'Image' && item.img" :src="item.img" :style="{ width: item.width, height: item.height }" />
                            <template v-else-if="item.dataType === 'Good' && item.goods">
                                <div class="cap-cube__title-group">
                                    <strong>{{ item.mainTitle }}</strong>
                                    <span>{{ item.subTitle }}</span>
                                </div>
                                <div
                                    class="cap-cube__goods-group"
                                    :style="{
                                        width: `${item.goodsWidth}px`,
                                        height: `${item.goodsHeight}px`,
                                        'flex-direction': item.goodsWidth > item.goodsHeight ? 'row' : 'column',
                                    }"
                                >
                                    <div
                                        v-for="({ pic, productId }, i) in item.goods"
                                        :key="`${productId}=>${i}`"
                                        class="cap-cube__good"
                                        :style="{
                                            'background-color': '#fff',
                                            'background-image': `url(${pic || empty_img})`,
                                            ...item.goodStyle,
                                        }"
                                    />
                                </div>
                            </template>
                            <div v-else class="cube-selected-text">
                                {{ item.text }}
                            </div>
                            <div
                                v-if="
                                    activeShowCubeListWrap === index3 &&
                                    ((activeItem.dataType === 'Image' && activeItem[`subEntry`][index3].img) ||
                                        (activeItem.dataType === 'Good' && activeItem[`subEntry`][index3].goods?.length))
                                "
                                class="iconfont iconshangpinxiangqing-baozhuang iconshangpinxiangqing-baozhuang-2"
                            ></div>
                        </div>
                    </div>
                    <div class="zent-design-editor__control-group-help-desc">
                        {{ formData.showMethod === 7 ? '选定布局区域，在下方添加图片，建议添加比例一致的图片' : '选定布局区域，在下方添加图片' }}
                    </div>
                </div>
            </div>
        </div>
        <div class="zent-design-editor__control-group">
            <div class="zent-design-editor__control-group-container">
                <div class="zent-design-editor__control-group-label">间隙:</div>
                <div class="zent-design-editor__control-group-control">
                    <el-slider v-model="activeItem.borderWidth" show-input class="zent-slider" :max="30"></el-slider>
                </div>
            </div>
        </div>
        <div class="zent-design-editor__control-group">
            <div class="zent-design-editor__control-group-container">
                <div class="zent-design-editor__control-group-label">边距:</div>
                <div class="zent-design-editor__control-group-control">
                    <el-slider v-model="activeItem.pageMargin" show-input class="zent-slider" :max="30"></el-slider>
                </div>
            </div>
        </div>
        <template v-if="activeItem.subEntry.length > 0">
            <el-tabs v-model="activeItem.subEntry[activeShowCubeListWrap].dataType" type="card" @tab-click="handleChangeDataType">
                <el-tab-pane label="图片" name="Image">
                    <div class="rc-design-editor-card-item">
                        <div class="rc-design-component-editor_subentry-item clearfix">
                            <div class="subentry-item-editor-form-content">
                                <div class="zent-design-editor__control-group subentry-control-group">
                                    <div class="zent-design-editor__control-group-container flex" style="align-items: flex-start">
                                        <div class="zent-design-editor__control-group-label">图片：</div>
                                        <div class="zent-design-editor__control-group-control" style="flex-grow: 0">
                                            <div
                                                v-if="!activeItem[`subEntry`][activeShowCubeListWrap].img"
                                                class="selectMaterialStyle"
                                                style="width: 60px; height: 60px"
                                                @click="buttonlFn"
                                            >
                                                <span class="selectMaterialStyle__span">+ </span>
                                            </div>
                                            <img
                                                v-else
                                                alt=""
                                                class="selectMaterialStyle"
                                                style="width: 60px; height: 60px"
                                                :src="activeItem[`subEntry`][activeShowCubeListWrap].img"
                                                @click="buttonlFn"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-tab-pane>
                <el-tab-pane v-if="formData.showMethod !== 7" label="商品" name="Good">
                    <div class="rc-design-editor-card-item">
                        <div class="rc-design-component-editor_subentry-item clearfix">
                            <div class="subentry-item-editor-form-content">
                                <div class="zent-design-editor__control-group subentry-control-group">
                                    <div class="zent-design-editor__control-group-container flex" style="align-items: flex-start">
                                        <div class="zent-design-editor__control-group-label">主标题：</div>
                                        <div class="zent-design-editor__control-group-control">
                                            <el-input
                                                v-model="activeItem[`subEntry`][activeShowCubeListWrap].mainTitle"
                                                type="text"
                                                class="zent-input"
                                                placeholder="最多支持6个字符"
                                                show-word-limit
                                                maxlength="6"
                                            />
                                            <el-color-picker
                                                v-model="activeItem[`subEntry`][activeShowCubeListWrap].mainTitleColor"
                                                size="large"
                                                show-alpha
                                                @change="(color:string) => (activeItem[`subEntry`][activeShowCubeListWrap].mainTitleColor = color)"
                                            />
                                        </div>
                                    </div>
                                    <div class="zent-design-editor__control-group-container flex" style="align-items: flex-start">
                                        <div class="zent-design-editor__control-group-label">副标题：</div>
                                        <div class="zent-design-editor__control-group-control">
                                            <el-input
                                                v-model="activeItem[`subEntry`][activeShowCubeListWrap].subTitle"
                                                type="text"
                                                class="zent-input"
                                                placeholder="最多支持6个字符"
                                                show-word-limit
                                                maxlength="6"
                                            />
                                            <el-color-picker
                                                v-model="activeItem[`subEntry`][activeShowCubeListWrap].subTitleColor"
                                                size="large"
                                                show-alpha
                                                @change="(color:string) => (activeItem[`subEntry`][activeShowCubeListWrap].subTitleColor = color)"
                                            />
                                        </div>
                                    </div>
                                    <div class="zent-design-editor__control-group-container flex" style="align-items: flex-start">
                                        <div class="zent-design-editor__control-group-label">背景色：</div>
                                        <div class="zent-design-editor__control-group-control" style="display: flex; align-items: flex-start">
                                            <el-input
                                                v-model="activeItem[`subEntry`][activeShowCubeListWrap].background"
                                                type="textarea"
                                                class="zent-input"
                                                placeholder="支持颜色输入"
                                            />
                                            <el-color-picker
                                                v-model="activeItem[`subEntry`][activeShowCubeListWrap].backgroundColor"
                                                size="large"
                                                show-alpha
                                                @change="(color:string) => (activeItem[`subEntry`][activeShowCubeListWrap].background = color)"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </template>
        <!-- 选择素材 e -->
        <selectMaterial
            :dialog-visible="dialogVisible"
            :upload-files="1"
            @select-material-fn="selectMaterialFn"
            @cropped-file-change="croppedFileChange"
            @checked-file-lists="checkedFileLists"
        />
        <!-- 选择素材 d -->
    </div>
</template>

<style lang="scss" scope>
@import '@/assets/css/decoration/cubeBox.scss';
@include b(selectMaterialStyle) {
    width: 60px;
    height: 60px;
    border-radius: 5px;
    overflow: hidden;
    border: 1px dashed #ccc;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    @include e(span) {
        color: #999;
        font-size: 14px;
        line-height: 24px;
    }
}
</style>
