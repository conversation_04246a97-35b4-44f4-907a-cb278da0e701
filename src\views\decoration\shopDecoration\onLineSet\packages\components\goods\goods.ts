/*
 * @description: 商品组件
 * @Author: lexy
 * @Date: 2022-08-08 17:34:02
 * @LastEditors: lexy
 * @LastEditTime: 2023-06-09 14:13:17
 */
export enum ListStyle {
    'goods-style--one' = 'goods-style--one',
    'goods-style--two' = 'goods-style--two',
    'goods-style--three' = 'goods-style--three',
    'goods-style--four' = 'goods-style--four',
    'goods-style--five' = 'goods-style--five',
}
export enum CategoryStyle {
    'pic-text',
    'text',
}
type GoodType = {
    ponentType: number
    goods: any[]
    listStyle: keyof typeof ListStyle
    categoryType: 'all' | 'selected'
    featuredCategory: boolean
    moreGoods: boolean
    sort: 0 | 1
    showPrice: boolean
    categoryStyle: keyof typeof CategoryStyle
    // pagePadding: number
    // titleStyle: number
    // goodPadding: number
    // goodsStyle: string
    // angle: string
    // textStyle: string
    firstCatList: CategoryItem[]
    currentCategoryId: string
    showContent: GoodShowContentType
    goodsItem: GoodEnumItemType
    goodsCount: number
    goodsTemp: GoodTempItemType
    categoryTemp: CategoryItem
}
type GoodShowContentType = {
    // nameShow: boolean
    // priceShow: boolean
    // buttonShow: boolean
    buttonStyle: number
    buttonText: string
    tagShow: boolean
    tagStyle: number
    pointShow: boolean
}
type GoodEnumItemType = {
    id: number
    img: string
    name: string
    price: string
}
export type GoodTempItemType = {
    id: number
    name: string
    img: string
    endTime: string
    price: number
    guide: number
    soldCount: number
    inventory: number
    deliveryTime: string
}
export default {
    /** 1展示分类  2不展示分类 */
    ponentType: 1,

    /** 商品 */
    goods: [] as ApiGoodItemType[],

    /** 列表样式 */
    listStyle: 'goods-style--one',

    /** 类目类型 */
    categoryType: 'all',

    /** 推荐类目 */
    featuredCategory: true,

    /** 更多商品 */
    moreGoods: true,

    /** 是否展示排序 */
    sort: 0,

    /** 分类样式 */
    categoryStyle: 'pic-text',

    /** 是否展示划线价 */
    showPrice: false,

    /** 页面边距 */
    // pagePadding: 17,

    /** 类目样式 1新  2下划线 */
    // titleStyle: 1,

    /** 商品间距 */
    // goodPadding: 12,

    /** 商品样式 */
    // goodsStyle: 'is-none',

    /** 图片倒角 */
    // angle: 'is-straight',

    /** 文本样式 */
    // textStyle: 'is-normal',

    /** 一级类目 */
    firstCatList: [],

    /** 当前分类ID */
    currentCategoryId: '',

    /** 显示内容 */
    showContent: {
        // nameShow: true,
        // priceShow: true,
        // buttonShow: true,
        buttonStyle: 1,
        buttonText: '',
        tagShow: true,
        tagStyle: 1,
        pointShow: true,
    },

    /** 商品对象 */
    goodsItem: {
        id: 1,
        img: 'https://devoss.chongyoulingxi.com/system-front/mobile/def_commodity.png',
        name: '商品名称',
        price: '99.90',
    },

    /** 商品数量 */
    goodsCount: 1,
    /** 分类模版 */
    categoryTemp: {
        productNum: 5,
        name: '分类名',
        id: '1',
        pic: 'https://devoss.chongyoulingxi.com/system-front/mobile/def_category.png',
    },
    goodsTemp: {
        id: 1,
        name: '商品名称',
        img: 'https://devoss.chongyoulingxi.com/system-front/mobile/def_commodity.png',
        endTime: '20:15:14',
        price: 99,
        guide: 219,
        soldCount: 10,
        inventory: 120,
        deliveryTime: '06月24日 14:00',
    },
} as GoodType
export type CategoryItem = {
    productNum: number
    name: string
    id: string
}
// 选择商品时检索出的商品类型
export type ApiGoodItemType = {
    categoryFirstId: string
    categorySecondId: string
    categoryThirdId: string
    createTime: string
    hotScore: string
    id: string
    isCheck?: boolean
    productName: string
    pic: string
    platformCategoryFirstId: string
    platformCategorySecondId: string
    platformCategoryThirdId: string
    salePrices: string[]
    salesVolume: string
    shopId: string
    shopName: string
}

export type SubFormGoodItem = {
    pic: string
    productId: string
    productName: string
    salePrices: string[]
    salesVolume: string
    shopId: string
    shopName: string
    specs: string
    status: string
}
