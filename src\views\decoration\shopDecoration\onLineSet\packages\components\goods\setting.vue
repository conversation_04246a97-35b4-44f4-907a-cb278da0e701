<script setup lang="ts">
import { ref, PropType, watch } from 'vue'
import { useVModel } from '@vueuse/core'
import { ElMessageBox } from 'element-plus'
import defaultGoodData from './goods'
import type { CategoryItem } from './goods'
import type { ApiRetrieveComItemType } from '@/apis/good/model'
/*
 *variable
 */
const $props = defineProps({
    formData: {
        type: Object as PropType<typeof defaultGoodData>,
        default: defaultGoodData,
    },
})
const $emit = defineEmits(['update:formData'])
const subForm = useVModel($props, 'formData', $emit)
watch(
    subForm.value,
    (newval) => {
        if (newval.sort === undefined) {
            subForm.value.sort = 0
        }
        if (newval.categoryStyle === undefined) {
            subForm.value.categoryStyle = 'pic-text'
        }
        if (!newval.firstCatList) {
            subForm.value.firstCatList = []
        }
        if (!newval.currentCategoryId === undefined) {
            subForm.value.currentCategoryId = '-1'
        }
    },
    { immediate: true },
)

const selectGoods = ref()
// 选择商品分类弹窗
const dialogShow = ref(false)
// 类目拖动下角标
const classDragIndex = ref(-1)
// 列表样式
const listStyles = [
    {
        label: '大图模式',
        value: 'goods-style--one',
    },
    {
        label: '一行两个',
        value: 'goods-style--two',
    },
    {
        label: '详细列表',
        value: 'goods-style--three',
    },
    {
        label: '横向滑动',
        value: 'goods-style--four',
    },
    {
        label: '瀑布流',
        value: 'goods-style--five',
    },
]
// 分类样式
const categoryStyles = [
    {
        label: '图文',
        value: 'pic-text',
    },
    {
        label: '纯文本',
        value: 'text',
    },
]

// 购物按钮样式
const buttonStyles = [
    {
        label: '加购一',
        value: '1',
        isGeneral: false,
    },
    {
        label: '加购二',
        value: 'button-style--two',
        isGeneral: false,
    },
    {
        label: '立购一',
        value: 'button-style--three',
        isGeneral: false,
    },
    {
        label: '立购二',
        value: 'button-style--four',
        isGeneral: false,
    },
    {
        label: '图标',
        value: '',
        isGeneral: true,
    },
]
// 商品角标样式
const tagStyles = [
    {
        label: '新品',
        value: 'tag-style--one',
    },
    {
        label: '热卖',
        value: 'tag-style--two',
    },
    {
        label: '抢购',
        value: 'tag-style--three',
    },
]
// 选择商品弹窗
const goodsVisible = ref(false)
// 选择商品数组
const pointGoodsList = ref<ApiRetrieveComItemType[]>([])
const dragStarIndex = ref(0)
/*
 *lifeCircle
 */
/*
 *function
 */
/**
 * @LastEditors: lexy
 * @description: 选择商品分组
 */
const handleChooseCategory = () => {
    dialogShow.value = true
}
/**
 * @LastEditors: lexy
 * @description: 移除商品
 */
const handleRemoveGoods = (index: number) => {
    subForm.value.goods.splice(index, 1)
    subForm.value.goodsCount--
}
/**
 * @LastEditors: lexy
 * @description: 增加商品
 */
const handleAddGoods = () => {
    pointGoodsList.value = []
    subForm.value.goods.forEach((i) => {
        pointGoodsList.value.push(i)
    })
    goodsVisible.value = true
}
/**
 * @LastEditors: lexy
 * @description: 取消操作
 */
const handleCancel = () => {
    ElMessageBox.confirm('确定要退出选择商品页面? 退出后，未保存的信息将不会保留!', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(() => {
        goodsVisible.value = false
        selectGoods.value.search = {
            maxPrice: null,
            minPrice: null,
            name: null,
            current: 1,
            size: 10,
            saleMode: '',
            showCategoryId: null,
        }
        selectGoods.value.goodsList.map((item: { isCheck: boolean }) => {
            return (item.isCheck = false)
        })
    })
}
/**
 * @LastEditors: lexy
 * @description: 确认选择商品
 */
// const handleSure = () => {
//     const list = selectGoods.value.tempGoods as ApiRetrieveComItemType[]
//     console.log('selectGoods.value.tempGoods', list)
//     const temp: SubFormGoodItem[] = []
//     goodsVisible.value = false
//     list.forEach((item) => {
//         const { productId, pic, productName, salePrices, salesVolume, shopId, shopName, specs, status } = item
//         temp.push({
//             productId,
//             pic,
//             productName,
//             salePrices,
//             salesVolume,
//             shopId,
//             shopName,
//             specs,
//             status,
//         })
//     })
//     subForm.value.goods = temp
//     subForm.value.goodsCount = temp.length
// }
/**
 * @LastEditors: lexy
 * @description: 开始拖动，记录拖动的组件下角标
 */
const handleDragstart = (i: number) => {
    dragStarIndex.value = i
}
/**
 * @LastEditors: lexy
 * @description:
 */
const handleDrop = (i: number) => {
    if (dragStarIndex.value === i) {
        return false
    }
    const temp = subForm.value.goods.splice(dragStarIndex.value, 1, subForm.value.goods[i])
    subForm.value.goods.splice(i, 1, ...temp)
}

/**
 * @LastEditors: lexy
 * @description: 被放置的容器触发事件，交换两个组件的位置
 * @param {*} i
 */
const handleDropClass = (i: number) => {
    if (classDragIndex.value === i) {
        return false
    }
    const temp = subForm.value.firstCatList.splice(classDragIndex.value, 1, subForm.value.firstCatList[i])
    subForm.value.firstCatList.splice(i, 1, ...temp)
}
/**
 * @LastEditors: lexy
 * @description:  阻止默认行为，否则drop事件不会触发
 * @returns {*}
 */
const handleDragoverClass = (e: Event) => {
    e.preventDefault()
}
/**
 * @LastEditors: lexy
 * @description: 开始拖动，记录拖动的组件下角标
 * @param {*} i
 */
const handleDragClass = (i: number) => {
    classDragIndex.value = i
}
/**
 * @LastEditors: lexy
 * @description: 选择一级分类
 */
const handleSelectedCategory = (list: CategoryItem[] = []) => {
    const ls = subForm.value.firstCatList || []
    console.log('list', list)
    list.forEach((i) => {
        const temp = ls.find((t) => t.id === i.id)
        if (!temp) {
            ls.push(i)
        }
    })
    subForm.value.firstCatList = ls
    subForm.value.currentCategoryId = list[0].id
}
/**
 * @LastEditors: lexy
 * @description: 删除一级分类
 */
const handleGoodGroupDel = (id: string) => {
    const firstCatList = subForm.value.firstCatList || []
    const index = firstCatList.findIndex((item) => item.id === id)
    firstCatList.splice(index, 1)
}
/**
 * @LastEditors: lexy
 * @description: 切换显示类型
 */
const handleChangePonentType = (ponentType: 1 | 2) => {
    if (ponentType === 1) {
        subForm.value.goods = []
    }
}
/**
 * @LastEditors: lexy
 * @description: 商品数量数据切换
 * @param {*} group
 */
const handleChangeProductNumber = (group: CategoryItem) => {
    if (!/^\d{1,2}$/.test(String(group.productNum))) {
        group.productNum = 5
    }
}
/**
 * 修改分类样式
 * @param val
 */
const changeCategoryStyle = (val) => {
    subForm.value.categoryStyle = val
}
</script>

<template>
    <el-form :model="subForm" label-width="70px" class="goods__page--set">
        <el-form-item label="展示分类">
            <el-radio-group v-model="subForm.ponentType" @change="handleChangePonentType">
                <el-radio :value="1">是</el-radio>
                <el-radio :value="2">否</el-radio>
            </el-radio-group>
        </el-form-item>
        <template v-if="subForm.ponentType === 1">
            <el-form-item label="分类" prop="categoryType">
                <el-radio-group v-model="subForm.categoryType" default-value="all">
                    <el-radio :value="'all'">全部分类</el-radio>
                    <el-radio :value="'selected'">指定分类</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="分类样式">
                <el-radio-group class="radio-wrapper" :model-value="subForm.categoryStyle" @change="changeCategoryStyle">
                    <el-radio v-for="item in categoryStyles" :key="item.value" :value="item.value">
                        {{ item.label }}
                    </el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="全部分组" prop="featuredCategory">
                <el-switch v-model="subForm.featuredCategory" :default-value="true" />
            </el-form-item>
            <el-form-item label="更多商品" prop="moreGoods">
                <el-switch v-model="subForm.moreGoods" :default-value="true" />
            </el-form-item>
        </template>
        <el-form-item label="排序">
            <el-radio-group v-model="subForm.sort">
                <el-radio :value="0">不展示</el-radio>
                <el-radio :value="1">展示</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item label="列表样式">
            <el-radio-group v-model="subForm.listStyle">
                <el-radio v-for="(item, idx) in listStyles" :key="item.value" :class="[idx > 1 ? 'goods-view__addmart' : '']" :value="item.value">
                    {{ item.label }}
                </el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item v-if="['goods-style--one', 'goods-style--three'].includes(subForm.listStyle)" label="购物按钮">
            <el-radio-group v-model="subForm.showContent.buttonStyle" style="margin-left: 24px">
                <el-radio v-for="(item, idx) in buttonStyles" :key="item.value" :value="idx + 1"> {{ item.label }}</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item v-else label="购物按钮" @vnode-before-mount="subForm.showContent.buttonStyle = buttonStyles.length">
            <el-radio-group v-model="subForm.showContent.buttonStyle" style="margin-left: 24px">
                <el-radio v-for="item in buttonStyles.filter((item) => item.isGeneral)" :key="item.value" :value="5">
                    {{ item.label }}
                </el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item label="商品角标">
            <div>
                <el-checkbox v-model="subForm.showContent.tagShow">选用</el-checkbox>
                <el-radio-group v-if="subForm.showContent.tagShow" v-model="subForm.showContent.tagStyle" style="margin-left: 24px">
                    <el-radio v-for="(item, idx) in tagStyles" :key="item.value" :class="[idx > 2 ? 'goods-view__addmart' : '']" :value="idx + 1">
                        {{ item.label }}
                    </el-radio>
                </el-radio-group>
            </div>
        </el-form-item>
        <el-form-item label="划线价" props="showPrice">
            <el-switch v-model="subForm.showPrice" :default-value="false" />
        </el-form-item>
    </el-form>
</template>

<style lang="scss" scoped>
@import '@/assets/css/decoration/goods.scss';

.goods__button-text {
    width: 100px;
    display: inline-block;
    height: 24px;
    line-height: 24px;

    .el-input__inner {
        height: 24px !important;
        line-height: 24px !important;
    }
}

.goods__page--set {
    .select__box-tips {
        font-size: 13px;
        color: #999;
        margin-left: 30px;
    }

    .class__box {
        background-color: #fff;
        border: 1px solid #ccc;
        max-height: 220px;
        overflow-y: scroll;
        border-radius: 4px;
        margin-bottom: 22px;
        padding-bottom: 10px;
    }

    .info__tip-box {
        height: 40px;
        background-color: #f5f5f5;
        margin-bottom: 3px;
        position: relative;
        z-index: 1000;

        span {
            display: inline-block;
            height: 40px;
            line-height: 40px;
        }

        .t_one {
            width: 170px;
            padding-left: 18px;
        }

        .t_two {
            width: 134px;
        }
    }

    .list__item {
        height: 35px;
        line-height: 35px;
        font-size: 14px;
        box-sizing: border-box;
        padding: 5px 10px;

        .show__name {
            display: inline-block;
            width: 135px;
            padding: 0px 10px;
            background-color: #f2f2f2;
            font-size: 12px;
            height: 27px;
            line-height: 27px;
            border-radius: 6px;
            white-space: nowrap;
            text-overflow: ellipsis;
            color: #333;
            border: 1px solid #ddd;
        }

        .click__btn {
            float: right;
            color: #333;
            height: 35px;
            line-height: 35px;
            cursor: pointer;
        }

        .input__pro-num {
            width: 80px;
            float: right;
            margin-right: 70px;
        }
    }
}
</style>
