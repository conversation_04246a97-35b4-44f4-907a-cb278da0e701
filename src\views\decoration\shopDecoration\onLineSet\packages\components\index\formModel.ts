/*
 * @description: 整合组件初始化数据
 * @Author: lexy
 * @Date: 2022-08-17 17:17:21
 * @LastEditors: lexy
 * @LastEditTime: 2024-02-04 10:05:37
 */

import cubeBox from '../cube-box/cubeBox'
import goods from '../goods/goods'
import navigation from '../navigation/navigation'
import search from '../search/search'
import swiper from '../swiper/swiper'
import navBar from '../navBar/nav-bar'
import onlyPromotion from '../onlyPromotion/onlyPromotion'

const defaultData = {
    cubeBox,
    goods,
    navigation,
    search,
    swiper,
    navBar,
    onlyPromotion,
}
const defaultDataArr = Object.values(defaultData)
type FormDataType = typeof defaultDataArr[number]

export interface ComponentItem {
    icon: string
    id?: string
    label: string
    value: keyof typeof defaultData
    formData?: FormDataType
    showType?: boolean
    type?: number
}
export default defaultData
