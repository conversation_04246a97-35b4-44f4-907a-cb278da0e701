/*
 * @description: 所有展示组价
 * @Author: lexy
 * @Date: 2022-08-17 17:26:14
 * @LastEditors: lexy
 * @LastEditTime: 2023-11-08 09:51:36
 */
import { defineAsyncComponent } from 'vue'
export default {
    cubeBox: defineAsyncComponent(() => import('../cube-box/preview.vue')),
    goods: defineAsyncComponent(() => import('../goods/preview.vue')),
    navigation: defineAsyncComponent(() => import('../navigation/preview.vue')),
    search: defineAsyncComponent(() => import('../search/preview.vue')),
    swiper: defineAsyncComponent(() => import('../swiper/preview.vue')),
    navBar: defineAsyncComponent(() => import('../navBar/preview.vue')),
    classification: defineAsyncComponent(() => import('../classification/preview.vue')),
    onlyPromotion: defineAsyncComponent(() => import('../onlyPromotion/preview.vue')),
}
