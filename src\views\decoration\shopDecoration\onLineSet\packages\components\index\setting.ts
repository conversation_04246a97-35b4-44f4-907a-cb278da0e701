/*
 * @description: 所有配置组价
 * @Author: lexy
 * @Date: 2022-08-17 17:26:14
 * @LastEditors: lexy
 * @LastEditTime: 2023-11-08 09:52:31
 */
import { defineAsyncComponent } from 'vue'
export default {
    cubeBox: defineAsyncComponent(() => import('../cube-box/setting.vue')),
    goods: defineAsyncComponent(() => import('../goods/setting.vue')),
    navigation: defineAsyncComponent(() => import('../navigation/setting.vue')),
    search: defineAsyncComponent(() => import('../search/setting.vue')),
    swiper: defineAsyncComponent(() => import('../swiper/setting.vue')),
    navBar: defineAsyncComponent(() => import('../navBar/setting.vue')),
    classification: defineAsyncComponent(() => import('../classification/setting.vue')),
    onlyPromotion: defineAsyncComponent(() => import('../onlyPromotion/setting.vue')),
}
