<template>
    <div class="description">
        <div class="description__content">
            平台对账是指平台端 与 各交易对象之间的资金(不含积分订单、提现业务)往来，订单【已完成】后生成对应的对账数据。
        </div>
        <el-table
            :data="configData"
            border
            :header-cell-style="{
                'background-color': '#F6F8FA',
                'font-weight': 'normal',
                color: '#515151',
            }"
        >
            <el-table-column prop="transactionType" label="交易类型" width="140" />
            <el-table-column prop="flowType" label="收入/支出" />
            <el-table-column prop="transactionObject" label="交易对象" />
            <el-table-column prop="fieldDescription" label="字段说明" width="450" />
        </el-table>
    </div>
</template>

<script lang="ts" setup>
const configData = [
    {
        transactionType: '平台服务费',
        flowType: '收入',
        transactionObject: '店铺',
        fieldDescription: '根据平台商家扣率或订单金额进行提佣，为平台的正常运行提供支撑',
    },
    {
        transactionType: '平台服务费(采购)',
        flowType: '收入',
        transactionObject: '供应商',
        fieldDescription: '线上支付的采购订单根据平台商家扣率或订单金额进行提佣，为平台的正常运行提供支撑',
    },
    {
        transactionType: '用户充值',
        flowType: '收入',
        transactionObject: '用户',
        fieldDescription: '用户自己储值充值而产生的平台收入，不含系统充值、系统扣除',
    },
    {
        transactionType: '购买付费会员',
        flowType: '收入',
        transactionObject: '用户',
        fieldDescription: '用户购买付费会员产生的平台收入',
    },
    {
        transactionType: '续费付费会员',
        flowType: '收入',
        transactionObject: '用户',
        fieldDescription: '用户续费(继续购买)付费会员产生的平台收入',
    },
    {
        transactionType: '积分交易',
        flowType: '收入',
        transactionObject: '用户',
        fieldDescription: '用户购买需要支付现金的积分商品而产生的(含运费)收入',
    },
    {
        transactionType: '会员折扣',
        flowType: '支出',
        transactionObject: '店铺',
        fieldDescription: '因会员享有该权益导致的费用支出，该笔支出将作为店铺的收入',
    },
    {
        transactionType: '会员包邮',
        flowType: '支出',
        transactionObject: '店铺',
        fieldDescription: '因会员享有该权益导致的费用支出，该笔支出将作为店铺的收入',
    },
    {
        transactionType: '平台优惠券',
        flowType: '支出',
        transactionObject: '店铺',
        fieldDescription: '平台给指定用户发放的优惠券其成本由平台承担，该笔支出将作为店铺的收入',
    },
    {
        transactionType: '返利抵扣',
        flowType: '支出',
        transactionObject: '店铺',
        fieldDescription: '用户使用消费返利的余额购买商品，该笔支出将作为店铺的收入',
    },
    {
        transactionType: '充值赠送',
        flowType: '支出',
        transactionObject: '用户',
        fieldDescription: '用户充值时因满足储值规则赠送的金额',
    },
]
</script>

<style lang="scss" scoped>
@include b(description) {
    @include e(content) {
        margin-bottom: 15px;
    }
}
</style>
