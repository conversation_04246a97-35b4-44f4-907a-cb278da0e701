<template>
    <div style="background: #f9f9f9">
        <m-card v-model="cardFlag">
            <el-form ref="ruleForm" :model="searchForm">
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item label="订单号" prop="no" label-width="90px">
                            <el-input v-model="searchForm.no" placeholder="请输入订单号" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="用户昵称" prop="nickName" label-width="90px">
                            <el-input v-model="searchForm.nickName" placeholder="请输入用户昵称" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="购买时间" prop="buyTime" label-width="90px">
                            <el-date-picker
                                v-model="searchForm.buyTime"
                                value-format="YYYY-MM-DD"
                                start-placeholder="开始时间"
                                end-placeholder="结束时间"
                                type="daterange"
                                placeholder="请选择购买时间"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="手机号" prop="userPhone" label-width="90px">
                            <el-input v-model="searchForm.userPhone" placeholder="请输入手机号" maxlength="11" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="会员等级" prop="level" label-width="90px">
                            <el-input v-model="searchForm.level" placeholder="请输入会员等级" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="到期时间" prop="expireDate" label-width="90px">
                            <el-date-picker
                                v-model="searchForm.expireTime"
                                value-format="YYYY-MM-DD"
                                start-placeholder="开始时间"
                                end-placeholder="结束时间"
                                type="daterange"
                                placeholder="请选择到期时间"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row style="margin-left: 30px">
                    <el-col :span="24">
                        <div class="actions">
                            <div class="actions__btns">
                                <el-button type="primary" round @click="handleSearch">搜索</el-button>
                                <el-button round @click="handleReset">重置</el-button>
                                <el-button round type="primary" @click="handleExport">导出</el-button>
                            </div>
                        </div>
                    </el-col>
                </el-row>
            </el-form>
        </m-card>
    </div>
    <div class="handle_container df">
        <div class="export cup">
            <el-icon class="export-icon" @click="showMemberRecordsDialog = true"><QuestionFilled /></el-icon>
        </div>
    </div>
    <el-dialog v-model="showMemberRecordsDialog" title="会员记录说明" :width="800">
        <description-dialog />
    </el-dialog>
</template>

<script lang="ts" setup>
import MCard from '@/components/MCard.vue'
import descriptionDialog from './description-dialog.vue'
import { QuestionFilled } from '@element-plus/icons-vue'
import { cloneDeep } from 'lodash'
const cardFlag = ref(false)
const showMemberRecordsDialog = ref(false)
const searchForm = reactive({
    no: '',
    nickName: '',
    userPhone: '',
    level: '',
    buyTime: '',
    expireTime: '',
})
const $emit = defineEmits(['changeShow', 'search', 'export'])
watch(
    () => cardFlag.value,
    (isShow) => $emit('changeShow', isShow),
)
const handleSearch = () => {
    const cloneSearchForm: any = cloneDeep(searchForm)
    if (Array.isArray(searchForm.buyTime)) {
        cloneSearchForm.buyStartTime = searchForm.buyTime?.[0]
        cloneSearchForm.buyEndTime = searchForm.buyTime?.[1]
    }
    if (Array.isArray(searchForm.expireTime)) {
        cloneSearchForm.expireStartTime = searchForm.expireTime?.[0]
        cloneSearchForm.expireEndTime = searchForm.expireTime?.[1]
    }
    $emit('search', cloneSearchForm)
}
const handleReset = () => {
    // @ts-ignore
    Object.keys(searchForm).forEach((key) => (searchForm[key] = ''))
}
const handleExport = () => {
    $emit('export')
}
</script>

<style lang="scss" scoped>
@include b(export-icon) {
    font-size: 28px;
    margin-left: 8px;
}
@include b(actions) {
    @include flex(space-between);
}
</style>
