<template>
    <div style="display: flex; justify-content: space-between">
        <div>
            <el-date-picker
                v-model="dateRange"
                format="YYYY/MM/DD"
                value-format="YYYY-MM-DD"
                type="daterange"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                style="margin-bottom: 14px"
                @change="handleChangeDate"
            />
        </div>
        <div>
            <el-input v-model="searchParams.no" placeholder="交易流水号" type="text">
                <template #append>
                    <el-button :icon="Search" @click="initWithdrawList" />
                </template>
            </el-input>
        </div>
    </div>
    <el-table
        :data="withdrawList"
        :header-cell-style="{ background: '#F6F8FA' }"
        :row-style="{ height: '68px' }"
        height="510px"
        @selection-change="handleChangeSelection"
    >
        <el-table-column type="selection" fixed="left" />
        <el-table-column label="订单号" prop="reqTraceNum" align="center" width="170" />
        <!-- <el-table-column label="银行名" prop="bankName" align="center" width="120" />
        <el-table-column label="银行账号" prop="bankName" align="center" width="100" :formatter="formatBankAccount" /> -->
        <el-table-column label="提现金额" align="center">
            <template #default="{ row }">
                <div>{{ divTenThousand(row.orderAmount) }}</div>
            </template>
        </el-table-column>
        <el-table-column label="手续费" align="center">
            <template #default="{ row }">
                <div>{{ divTenThousand(row.couponAmount) }}</div>
            </template>
        </el-table-column>
        <el-table-column label="状态" align="center">
            <template #default="{ row }">
                <div>{{ convertStatus(row.status) }}</div>
            </template>
        </el-table-column>
        <el-table-column v-if="status === 'FORBIDDEN'" label="拒绝说明" prop="reason" align="center"></el-table-column>
        <el-table-column v-else label="备注" prop="remark" align="center"></el-table-column>
        <el-table-column label="申请时间" prop="applyTime" align="center"></el-table-column>
    </el-table>
    <page-manage
        :page-num="pageConfig.current"
        :page-size="pageConfig.size"
        :total="pageConfig.total"
        @handle-current-change="handleChangeCurrent"
        @handle-size-change="handleChangeSize"
    />
</template>

<script lang="ts" setup>
import { reactive } from 'vue'
import PageManage from '@/components/PageManage.vue'
import { doGetPlatformWithdrawList } from '@/apis/finance'
import { ElMessage } from 'element-plus'
// import type { TableColumnCtx } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
const $props = withDefaults(defineProps<{ status: string; checkedData: any[] }>(), {
    status: 'APPLYING',
    checkedData: () => [],
})
const $emit = defineEmits(['update:checkedData'])
/*
 *variable
 */
const dateRange = ref('')
const { divTenThousand } = useConvert()
const pageConfig = reactive({
    size: 10,
    current: 1,
    total: 0,
})
const searchParams = reactive({
    status: 'APPLYING',
    startDate: '',
    endDate: '',
    no: '',
    supplier: '',
})
const withdrawList = ref([])
watch(
    () => $props.status,
    () => {
        pageConfig.current = 1
        initWithdrawList()
    },
    { immediate: true },
)
/*
 *lifeCircle
 */
// initWithdrawList()
/*
 *function
 */
// const formatBankAccount = (row: any, column: TableColumnCtx<any>) => {
//     return `***${row.bankAccount}`
// }

const handleChangeCurrent = (e: number) => {
    pageConfig.current = e
    initWithdrawList()
}
const handleChangeSize = (e: number) => {
    pageConfig.current = 1
    pageConfig.size = e
    initWithdrawList()
}
const handleChangeDate = (e: string[] | null) => {
    if (e) {
        searchParams.startDate = e[0]
        searchParams.endDate = e[1]
    } else {
        searchParams.startDate = ''
        searchParams.endDate = ''
    }
    initWithdrawList()
}
async function initWithdrawList() {
    // 商家端 'SHOP'   供应商端  'SUPPLIER'
    const type = 'SHOP'
    const { code, data, msg } = await doGetPlatformWithdrawList({ ...pageConfig, ...searchParams, status: $props.status, type })
    if (code === 200) {
        withdrawList.value = data.records
        pageConfig.total = data.total
    } else {
        ElMessage.error(msg ? msg : '获取提现列表失败')
    }
}
function convertStatus(val: string) {
    const statusType: { [x: string]: string } = {
        APPLYING: '待审核',
        SUCCESS: '已到账',
        CLOSED: '已拒绝',
        FORBIDDEN: '已拒绝',
    }
    return statusType[val]
}
defineExpose({ initWithdrawList })

const computedCheckedData = computed({
    get() {
        return $props.checkedData
    },
    set(newVal) {
        $emit('update:checkedData', newVal)
    },
})
const handleChangeSelection = (selectionData: any[]) => {
    computedCheckedData.value = selectionData
}
</script>
