export enum STORED_FLOW_ENUM {
    SYSTEM_GIFT = '系统赠送',
    USER_RECHARGE = '用户充值',
    SYSTEM_RECHARGE = '系统充值',
    SYSTEM_DEDUCTION = '系统扣除',
    SHOPPING_CONSUMPTION = '购物消费',
    PURCHASE_MEMBERSHIP = '购买会员',
    RENEWAL_MEMBERSHIP = '续费会员',
    UPGRADE_MEMBERSHIP = '升级会员',
    REFUND_SUCCESSFUL = '退款成功',
}

export const storeFlowConfig: { value: keyof typeof STORED_FLOW_ENUM; label: string }[] = [
    { value: 'SYSTEM_GIFT', label: '系统赠送' },
    { value: 'USER_RECHARGE', label: '用户充值' },
    { value: 'SYSTEM_RECHARGE', label: '系统充值' },
    { value: 'SYSTEM_DEDUCTION', label: '系统扣除' },
    { value: 'SHOPPING_CONSUMPTION', label: '购物消费' },
    { value: 'PURCHASE_MEMBERSHIP', label: '购买会员' },
    { value: 'RENEWAL_MEMBERSHIP', label: '续费会员' },
    // { value: 'UPGRADE_MEMBERSHIP', label: '升级会员' },
    { value: 'REFUND_SUCCESSFUL', label: '退款成功' },
]
