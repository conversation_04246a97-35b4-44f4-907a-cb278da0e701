<template>
    <div style="background: #f9f9f9">
        <m-card v-model="cardFlag">
            <el-form ref="ruleForm" :model="searchForm">
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item label="流水编号" prop="no" label-width="90px">
                            <el-input v-model="searchForm.no" placeholder="请输入流水编号" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="用户昵称" prop="userNickName" label-width="90px">
                            <el-input v-model="searchForm.userNickName" placeholder="请输入用户昵称" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="手机号" prop="userPhone" label-width="90px">
                            <el-input v-model="searchForm.userPhone" placeholder="请输入手机号" maxlength="11" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="操作类型" prop="operatorType" label-width="90px">
                            <el-select v-model="searchForm.operatorType" placeholder="请选择操作类型" clearable>
                                <el-option value="" label="全部" />
                                <el-option v-for="(item, index) in storeFlowConfig" :key="index" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="关联订单" prop="orderNo" label-width="90px">
                            <el-input v-model="searchForm.orderNo" placeholder="请输入关联订单" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="操作时间" prop="createTime" label-width="90px">
                            <el-date-picker
                                v-model="searchForm.createTime"
                                type="datetimerange"
                                format="YYYY/MM/DD HH:mm:ss"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                placeholder="请选择操作时间"
                                start-placeholder="开始时间"
                                end-placeholder="结束时间"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row style="margin-left: 30px">
                    <el-col :span="24">
                        <el-form-item>
                            <el-button type="primary" round @click="handleSearch">搜索</el-button>
                            <el-button round @click="handleReset">重置</el-button>
                            <slot></slot>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </m-card>
    </div>
</template>

<script lang="ts" setup>
import MCard from '@/components/MCard.vue'
import { cloneDeep } from 'lodash'
import { storeFlowConfig } from './config'
const cardFlag = ref(false)
const searchForm = reactive({
    orderNo: '',
    userNickName: '',
    userPhone: '',
    operatorType: '',
    no: '',
    createTime: '',
})
const $emit = defineEmits(['changeShow', 'search'])
watch(
    () => cardFlag.value,
    (isShow) => $emit('changeShow', isShow),
)
const handleSearch = () => {
    const cloneSearchForm: any = cloneDeep(searchForm)
    if (Array.isArray(searchForm.createTime)) {
        cloneSearchForm.operatorStartTime = searchForm.createTime?.[0]
        cloneSearchForm.operatorEndTime = searchForm.createTime?.[1]
    }
    $emit('search', cloneSearchForm)
}
const handleReset = () => {
    // @ts-ignore
    Object.keys(searchForm).forEach((key) => (searchForm[key] = ''))
    handleSearch()
}
</script>

<style lang="scss" scoped>
@include b(export-icon) {
    font-size: 28px;
    margin-left: 8px;
}
</style>
