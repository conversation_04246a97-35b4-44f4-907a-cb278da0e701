<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-04-27 11:39:49
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-09 17:25:52
-->
<template>
    <div class="commodity">
        <div class="commodity__left">
            <el-image style="width: 68px; height: 68px" :src="$props.info.pic" fit="fill"
                :preview-src-list="[$props.info.pic]"></el-image>
        </div>
        <div class="commodity__right">
            <div class="commodity__right--name-id" :title="$props.info.name">
                <el-text class="commodity__right--name"> {{ $props.info.name }}</el-text>
                <el-text class="mx-1" size="small" @click="handleCopy($props.info.id)">
                    ({{ $props.info.id }} <el-icon><CopyDocument /></el-icon>)
                </el-text>
            </div>
            <div class="commodity__right--price">售价：￥{{ retailPrice }}</div>
            <div class="commodity__right--price">供货：￥{{ salePrice }}</div>
            <div class="commodity__right--price">佣金：￥{{ commission }}</div>
            <div v-if="$props.info.providerName" class="commodity__right--sup" :title="$props.info.providerName">
                供应商:{{ $props.info.providerName }}
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import type { PropType } from 'vue'
import type { ApiGoodType } from '@/views/good/types'
import useConvert from '@/composables/useConvert'
import { CopyDocument } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
const $props = defineProps({
    info: {
        type: Object as PropType<ApiGoodType>,
        default() {
            return {}
        },
    },
})
const { divTenThousand } = useConvert()
const retailPrice = computed(() => {
    const tempArr =
        $props.info.storageSkus?.map((item) => {
            return +(Number(item.salePrice) + Number(item.commission))
        }) || []
    if (tempArr.length === 0) return 0
    const min = Math.min(...tempArr) / 10000
    const max = Math.max(...tempArr) / 10000
    if (max === min) {
        return max?.toFixed(2)
    } else {
        return `${min?.toFixed(2)}-${max?.toFixed(2)}`
    }
})
const salePrice = computed(() => {
    const tempArr = $props.info.storageSkus.map((item) => {
        return +item.salePrice
    })
    const min = Math.min(...tempArr) / 10000
    const max = Math.max(...tempArr) / 10000
    if (max === min) {
        return max?.toFixed(2)
    } else {
        return `${min?.toFixed(2)}-${max?.toFixed(2)}`
    }
})
const commission = computed(() => {
    const tempArr = $props.info.storageSkus.map((item) => {
        return +item.commission
    })
    const min = Math.min(...tempArr) / 10000
    const max = Math.max(...tempArr) / 10000
    if (max === min) {
        return max?.toFixed(2)
    } else {
        return `${min?.toFixed(2)}-${max?.toFixed(2)}`
    }
})
/**
 * 复制文本
 * @param text 文本
 */
const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text).then((res) => {
        ElMessage.success('复制成功！')
    })
}
</script>
<style lang="scss">
@include b(commodity) {
    @include flex();
    font-size: 12px;
    text-align: left;
    justify-content: flex-start;
    @include e(left) {
        width: 68px;
        height: 68px;
        margin-right: 10px;
    }
    @include e(right) {
        @include m(name) {
            @include utils-ellipsis(2);
            font-weight: bold;
        }
        @include m(price) {
            color: #ff7417;
            margin: 2px 0;
        }
        @include m(sup) {
            width: 120px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    }
}
</style>
