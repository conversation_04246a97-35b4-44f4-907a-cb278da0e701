<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-04-14 13:07:12
 * @LastEditors: lexy
 * @LastEditTime: 2023-07-21 11:09:18
-->
<template>
    <div style="background: #f9f9f9">
        <m-card v-model="cardFlag">
            <el-form ref="ruleForm" :model="searchForm">
                <el-row :gutter="20">
                    <el-col :span="8">
                        <!-- <el-form-item label="店铺名称" prop="name" label-width="90px">
                            <el-input v-model="searchForm.shopName" placeholder="请填写店铺名称" maxlength="20"></el-input>
                        </el-form-item> -->
                        <el-form-item label="店铺名称" prop="shopId" label-width="90px">
                            <el-select
                                v-model="searchForm.shopId"
                                filterable
                                remote
                                reserve-keyword
                                clearable
                                placeholder="请输入店铺名称"
                                :remote-method="shopSearchRemote"
                                :loading="shopSearchLoading"
                            >
                                <el-option v-for="item in shopSearchList" :key="item.id" :label="item.name" :value="item.id" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="商品id" prop="productId" label-width="90px">
                            <el-input v-model="searchForm.productId" placeholder="请填写商品id" maxlength="20" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="商品名称" prop="name" label-width="90px">
                            <el-input v-model="searchForm.name" placeholder="请填写商品名称" maxlength="20" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="商品类型" prop="platformCategoryId" label-width="90px">
                            <el-select v-model="searchForm.productType" placeholder="请选择商品类型" clearable>
                                <el-option v-for="item in goodsTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="平台类目" prop="platformCategoryId" label-width="90px">
                            <el-cascader
                                v-model="cascaderModel"
                                placeholder="请选择平台类目"
                                :options="categoryList"
                                :props="props1"
                                clearable
                                :show-all-levels="false"
                                @change="handleChangeCascader"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="商品来源" prop="sellType" label-width="90px">
                            <el-select v-model="searchForm.sellType" placeholder="请选择" clearable>
                                <template v-for="item in soucerOptions" :key="item.value">
                                    <el-option :label="item.label" :value="item.value" />
                                </template>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row style="margin-left: 30px">
                    <el-col :span="8">
                        <el-form-item>
                            <el-button type="primary" round @click="searchHandle">搜索</el-button>
                            <el-button round @click="handleReset">重置</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </m-card>
    </div>
</template>
<script lang="ts" setup>
import MCard from '@/components/MCard.vue'
import { doGetCategory, doGetShopList } from '@/apis/shops'
import type { searchFormType } from '../../types'
import type { CascaderProps } from 'element-plus'
const $props = defineProps({
    tabsActive: { type: String, default: ' ' },
})
const $emit = defineEmits(['getSearchParams', 'showChange'])
const searchForm = reactive<searchFormType>({
    platformCategoryId: '',
    shopId: '',
    productType: '',
    name: '',
    createBeginTime: '',
    createEndTime: '',
    date: '',
    status: '',
    sellType: '',
    productId: '',
})
const cascaderModel = ref('')

const props1: CascaderProps = {
    value: 'id',
    label: 'name',
    expandTrigger: 'hover',
}

const goodsTypeOptions = [
    {
        value: '',
        label: '全部商品',
    },
    {
        value: 'VIRTUAL_PRODUCT',
        label: '虚拟商品',
    },
    {
        value: 'REAL_PRODUCT',
        label: '实物商品',
    },
]

const shopSearchLoading = ref(false)
const shopSearchList = ref<any[]>([])
const categoryList = ref([])
const ruleForm = ref()
const cardFlag = ref(false)
const searchHandle = () => {
    if (searchForm?.date?.length > 0) {
        searchForm.createBeginTime = searchForm.date[0]
        searchForm.createEndTime = searchForm.date[1]
    } else {
        searchForm.createBeginTime = void 0
        searchForm.createEndTime = void 0
    }
    searchForm.platformCategoryId = searchForm.platformCategoryId === '0' ? '' : searchForm.platformCategoryId
    $emit('getSearchParams', searchForm)
}
const handleReset = () => {
    // @ts-ignore
    Object.keys(searchForm).forEach((key) => (searchForm[key] = ''))
    cascaderModel.value = ''
    searchHandle()
}

// 来源选项
const soucerOptions = [
    {
        value: '',
        label: '全部',
    },
    {
        value: 'OWN',
        label: '自有商品',
    },
    {
        value: 'PURCHASE',
        label: '采购商品',
    },
    {
        value: 'CONSIGNMENT',
        label: '代销商品',
    },
]
const shopSearchRemote = async (query: string) => {
    if (query) {
        const { data } = await doGetShopList({ name: query, current: 1, size: 999 })
        shopSearchList.value = data?.records || []
    } else {
        shopSearchList.value = []
    }
}

watch(
    () => cardFlag.value,
    (val) => {
        $emit('showChange', val)
    },
)
watch(
    () => $props.tabsActive,
    (val) => {
        if (val !== ' ') {
            searchForm.status = ''
        }
    },
)
const pageConfig = reactive({
    pageSize: 20,
    pageNum: 1,
    total: 0,
})
getCategory()
/**
 * @LastEditors: lexy
 * @description: 获取类目列表
 * @param {number} current
 * @param {number} size
 */
async function getCategory() {
    const { data } = await doGetCategory({
        current: pageConfig.pageNum,
        size: 1000,
    })
    pageConfig.total = data.total
    initList(data.records, 'secondCategoryVos')
    data.records.unshift({ categoryId: '0', id: '0', name: '全部类目', parentId: '0', sort: 1 })
    categoryList.value = data.records
}
function initList(list: any[], str: string) {
    list.forEach((item) => {
        if (item[str]) {
            item.children = item[str]
            delete item[str]
            if (item.children.length) {
                initList(item.children, 'categoryThirdlyVos')
            }
        }
    })
}
const handleChangeCascader = (e: any[]) => {
    searchForm.platformCategoryId = e.pop()
}

// 重置搜索参数
const refreshSearchForm = () => {
    let key: keyof searchFormType
    for (key in searchForm) {
        searchForm[key] = ''
    }
}

const goodsType = ref<'店铺商品' | '供应商商品'>('店铺商品')

// 是否是店铺商品
const isShopGoods = computed(() => goodsType.value === '店铺商品')

// 切换店铺商品 | 供应商商品
const changeGoods = (type: '店铺商品' | '供应商商品') => {
    console.log('type', type)
    goodsType.value = type
    // 刷新
    refreshSearchForm()
}
defineExpose({
    changeGoods,
})
</script>
