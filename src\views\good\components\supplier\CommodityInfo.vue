<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-04-27 11:39:49
 * @LastEditors: lexy
 * @LastEditTime: 2023-07-13 17:17:38
-->
<template>
    <div class="commodity">
        <div class="commodity__left">
            <el-image style="width: 68px; height: 68px" :src="$props.info.albumPics?.split(',')?.splice(0, 1)"
                fit="fill" :preview-src-list="$props.info.albumPics?.split(',')"></el-image>
        </div>
        <div class="commodity__right">
            <div class="commodity__right--name" :title="$props.info.productName">{{ $props.info.productName }}</div>
            <div class="commodity__right--price">￥{{ price }}</div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import type { PropType } from 'vue'
import type { SupplierListInterface } from '../../types/supplier'
const $props = defineProps({
    info: {
        type: Object as PropType<SupplierListInterface>,
        default() {
            return {}
        },
    },
})
const { divTenThousand } = useConvert()
const price = computed(() => {
    const salePrices = $props.info?.salePrices || []
    const maxPrice = salePrices.pop()
    const minPrice = salePrices.shift()
    if (maxPrice === minPrice) {
        return divTenThousand(minPrice)
    } else {
        return `${divTenThousand(minPrice)}~${divTenThousand(maxPrice)}`
    }
})
</script>
<style lang="scss">
@include b(commodity) {
    @include flex();
    font-size: 12px;
    text-align: left;
    justify-content: flex-start;
    @include e(left) {
        width: 68px;
        height: 68px;
        margin-right: 10px;
    }
    @include e(right) {
        @include m(name) {
            @include utils-ellipsis(2);
            font-weight: bold;
        }
        @include m(price) {
            color: #ff7417;
            margin: 4px 0;
        }
        @include m(sup) {
            width: 120px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    }
}
</style>
