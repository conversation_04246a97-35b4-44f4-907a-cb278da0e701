<template>
    <div class="details">
        <el-row :gutter="8">
            <el-col :span="3">商品名称：</el-col>
            <el-col :span="21" style="font-weight: 600">{{ dataInfo?.name }}</el-col>
        </el-row>
        <el-row :gutter="8">
            <el-col :span="12">
                <el-row :gutter="8">
                    <el-col :span="6">平台类目：</el-col>
                    <el-col :span="18">{{ platformCategoryName }}</el-col>
                </el-row>
            </el-col>
            <el-col :span="12">
                <el-row :gutter="8">
                    <el-col :span="6">销售方式：</el-col>
                    <el-col :span="18">{{ SellTypeEnum[dataInfo?.sellType as keyof typeof SellTypeEnum] }}</el-col>
                </el-row>
            </el-col>
            <el-col :span="12">
                <el-row :gutter="8">
                    <el-col :span="6">商品卖点：</el-col>
                    <el-col :span="18">{{ dataInfo?.saleDescribe }}</el-col>
                </el-row>
            </el-col>
        </el-row>
        <el-row :gutter="8">
            <el-col :span="24">
                <el-table :data="dataInfo?.storageSkus" max-height="230px" size="small">
                    <el-table-column label="规格">
                        <template #default="{ row }">
                            {{ row?.spec?.length ? row?.spec?.join(';') : '单规格' }}
                        </template>
                    </el-table-column>
                    <el-table-column label="SKU图">
                        <template #default="{ row }">
                            <el-image
                                :preview-src-list="previewLists"
                                e
                                :src="row?.image"
                                :z-index="9999"
                                :preview-teleported="true"
                                style="width: 50px; height: 50px"
                            />
                        </template>
                    </el-table-column>
                    <el-table-column label="划线价(元)">
                        <template #default="{ row }">
                            {{ divTenThousand(row?.price) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="销售价(元)">
                        <template #default="{ row }">
                            {{ divTenThousand(row?.retailPrice) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="供货价(元)">
                        <template #default="{ row }">
                            {{ divTenThousand(row?.salePrice) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="佣金(元)">
                        <template #default="{ row }">
                            {{ divTenThousand(row?.commission) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="是否限购" align="center">
                        <template #default="{ row }">
                            <el-tag :type="LIMIT_TEXT_COLOR[row?.limitType]" effect="dark">{{ LIMIT_TEXT[row?.limitType] }}</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="限购数量" prop="limitNum" />
                    <el-table-column label="库存类型" prop="stockType" align="center">
                        <template #default="{ row }">
                            <el-tag :type="STOCK_TEXT_COLOR[row?.stockType]">{{ STOCK_TEXT[row?.stockType] }}</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="库存" prop="stock" />
                    <el-table-column label="重量" prop="weight">
                        <template #default="{ row }">
                            <span>{{ row.weight }} {{ row.weightType ? `(${row.weightType})` : '(KG)' }}</span>
                        </template>
                    </el-table-column>
                </el-table>
            </el-col>
        </el-row>
        <el-row :gutter="8">
            <el-col :span="24">
                <!-- eslint-disable-next-line vue/no-v-html -->
                <div v-preview class="details-more" v-html="dataInfo?.detail" />
            </el-col>
        </el-row>
    </div>
</template>

<script lang="ts" setup>
import { doGetSupplierCommodityDetails } from '@/apis/good'
import { ElMessage } from 'element-plus'
import { LIMIT_TEXT, ApiSkuType, STOCK_TEXT_COLOR, STOCK_TEXT, LIMIT_TEXT_COLOR } from '../../types'

const { divTenThousand } = useConvert()
const $props = defineProps({
    commodityId: {
        type: String,
        default: '',
    },
    shopId: {
        type: String,
        default: '',
    },
})
enum SellTypeEnum {
    CONSIGNMENT = '代销商品',
    PURCHASE = '采购商品',
    OWN = '自有商品',
}
const dataInfo = ref<any>({})
const platformCategoryName = computed(() => {
    if (!dataInfo.value?.platformCategoryName) {
        return ''
    }
    const { oneName, twoName, threeName } = dataInfo.value?.platformCategoryName
    return [oneName, twoName, threeName].filter(Boolean).join('/')
})
const getCommidityDetails = async () => {
    const { code, msg, data } = await doGetSupplierCommodityDetails($props.commodityId, { shopId: $props.shopId })
    if (code === 200) {
        dataInfo.value = {
            ...data,
            storageSkus: (data.storageSkus as ApiSkuType[]).map((sku) => ({
                ...sku,
                retailPrice: Number(sku?.salePrice) + Number(sku?.commission),
            })),
        }
    } else {
        ElMessage.error({ message: msg })
    }
}
getCommidityDetails()

/**
 * zrb:获取图片列
 *
 *  */
const previewLists = computed(() => {
    if (dataInfo.value?.storageSkus) {
        return dataInfo.value?.storageSkus.map((item: any) => {
            return item.image
        })
    } else {
        return []
    }
})
</script>

<style lang="scss" scoped>
.el-row {
    padding: 5px 0;
    line-height: 1.5;
}

@include b(details-more) {
    max-height: 500px;
    overflow-y: auto;

    :deep(img) {
        max-width: 100%;
    }
}
</style>
