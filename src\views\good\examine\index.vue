<template>
    <el-radio-group v-model="tabRadio" size="large" class="radio">
        <el-radio-button label="shop">店铺商品</el-radio-button>
        <el-radio-button label="supplier">供应商商品</el-radio-button>
    </el-radio-group>
    <component :is="tabPageComponentMap[tabRadio]" />
</template>

<script lang="ts" setup>
const tabRadio = ref<'shop' | 'supplier'>('shop')
const tabPageComponentMap = {
    shop: defineAsyncComponent(() => import('./shop/index.vue')),
    supplier: defineAsyncComponent(() => import('./supplier/index.vue')),
}
</script>

<style scoped>
.radio {
    margin-bottom: 8px;
}
</style>
