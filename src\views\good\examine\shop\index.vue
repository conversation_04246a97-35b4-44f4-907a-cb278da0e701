<template>
    <search @on-search-params="getSearch" @change-show="handleSearchShow" />
    <el-tabs v-model="currentTab" class="tab_container" @tab-change="handleTabClick">
        <el-tab-pane v-for="(item, key) in goodsStatus" :key="item" :label="key" :name="item"></el-tab-pane>
    </el-tabs>
    <div class="handle_container">
        <el-button v-if="currentTab === 'UNDER_REVIEW'" type="primary" @click="handleAuditGoods(selectItems)">批量审核</el-button>
    </div>
    <q-table
        v-model:checked-item="selectItems"
        :data="tableList.goods"
        :selection="currentTab === 'UNDER_REVIEW' || currentTab === ''"
        :style="{ height: tableHeight }"
        class="table"
    >
        <q-table-column label="商品" align="left">
            <template #default="{ row }">
                <div class="commodity-info">
                    <el-image :src="row?.pic" :alt="row?.name" style="width: 68px; height: 68px; flex-shrink: 0" />
                    <div class="commodity-info__main">
                        <span style="font-weight: 600">{{ row?.name }}</span>
                        <span class="commodity-info__main--price">￥{{ salePriceRange(row?.salePrices) }}</span>
                    </div>
                </div>
            </template>
        </q-table-column>
        <q-table-column label="所属店铺" align="center" width="130" prop="shopName" />
        <q-table-column label="状态" align="center" width="60">
            <template #default="{ row }">
                {{ ExamineGoodsEnum[row?.auditStatus as keyof typeof ExamineGoodsEnum] }}
            </template>
        </q-table-column>
        <q-table-column label="提交时间" align="center" prop="submitTime" width="130" />
        <q-table-column label="审核时间" align="center" prop="auditTime" width="130" />
        <q-table-column label="操作" align="center" width="140">
            <template #default="{ row }">
                <el-link type="primary" @click="handlePreviewExamineDetails(row)">查看</el-link>
                <el-link v-if="row?.auditStatus === 'UNDER_REVIEW'" type="primary" @click="handleAuditGoods([row])">审核</el-link>
                <el-link v-if="row?.auditStatus === 'REFUSE'" type="primary" @click="reasonRefusal(row)">拒绝原因</el-link>
            </template>
        </q-table-column>
    </q-table>
    <BetterPageManage
        :page-num="tableList.page.current"
        :page-size="tableList.page.size"
        :total="tableList.total"
        @handle-current-change="handleChangeCurrentChange"
        @handle-size-change="handleChangeSize"
        @reload="initList"
    />
    <el-dialog v-model="previewVisible" title="商品详情" center width="900px" destroy-on-close>
        <CommodityDetails :commodity-id="commodityInfo.id" :shop-id="commodityInfo.shopId" />
    </el-dialog>
    <el-dialog v-model="showAuditDialog" title="商品审核" destroy-on-close @close="handleCloseAuditDialog">
        <audit-goods ref="auditGoodsRefs" />
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="showAuditDialog = false">取消</el-button>
                <el-button type="primary" @click="handleConfirmAudit">确认</el-button>
            </span>
        </template>
    </el-dialog>
    <el-dialog v-model="reasonRefusalDialog" title="拒绝原因" center destroy-on-close @close="reasonRefusal">
        <div>拒绝原因：{{ reason }}</div>
    </el-dialog>
</template>

<script lang="ts" setup>
import QTable from '@/components/qszr-core/packages/q-table/QTable'
import QTableColumn from '@/components/qszr-core/packages/q-table/q-table-column.vue'
import search from './components/search.vue'
import useExamineListHooks from './hooks/useExamineListHooks'
import BetterPageManage from '@/components/BetterPageManage/BetterPageManage.vue'
import CommodityDetails from '../../components/shop/commodityDetail.vue'
import usePreviewExamineDetails from './hooks/usePreviewExamineDetails'
import auditGoods from './components/audit-goods.vue'
import { ExamineGoodsEnum } from '../../types/index'

const {
    getSearch,
    handleSearchShow,
    currentTab,
    goodsStatus,
    handleTabClick,
    tableHeight,
    tableList,
    salePriceRange,
    initList,
    selectItems,
    handleAuditGoods,
    showAuditDialog,
    auditGoodsRefs,
    handleConfirmAudit,
    handleCloseAuditDialog,
    handleChangeCurrentChange,
    handleChangeSize,
    reasonRefusalDialog,
    reasonRefusal,
    reason,
} = useExamineListHooks()

const { commodityInfo, previewVisible, handlePreviewExamineDetails } = usePreviewExamineDetails()
</script>

<style lang="scss" scoped>
@include b(table) {
    overflow: auto;
    transition: height 0.5s;
}
@include b(commodity-info) {
    display: flex;
    @include e(main) {
        display: flex;
        flex-direction: column;
        margin-left: 8px;
        @include m(price) {
            color: #ff7417;
            margin: 10px 0;
        }
    }
}
@include b(el-link) {
    margin-right: 8px;
}
@include b(btns) {
    padding-bottom: 8px;
}
</style>
