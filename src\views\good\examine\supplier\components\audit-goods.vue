<template>
    <el-form ref="formRef" :model="formModel" :rules="formRules">
        <el-form-item prop="status" label="审核状态">
            <el-radio-group v-model="formModel.status" @change="handleChangeStatus">
                <el-radio label="SELL_OFF">通过</el-radio>
                <el-radio label="REFUSE">拒绝</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="explain" :required="formModel.status === 'REFUSE'">
            <el-input
                v-model="formModel.explain"
                :autosize="{ minRows: 2, maxRows: 4 }"
                type="textarea"
                placeholder="100字以内"
                :maxlength="100"
                show-word-limit
            />
        </el-form-item>
    </el-form>
</template>

<script lang="ts" setup>
import { FormInstance, FormRules } from 'element-plus'

const formModel = reactive({
    status: 'SELL_OFF',
    explain: '',
})
const handleChangeStatus = (changedStatus: any) => {
    if (changedStatus === 'SELL_OFF') {
        formModel.explain = ''
    }
}
const formRules: FormRules = {
    explain: {
        required: formModel.status === 'REFUSE',
        message: '请输入原因',
        trigger: 'blur',
    },
    status: {
        required: true,
        message: '请选择审核状态',
        trigger: 'change',
    },
}
const formRef = ref<FormInstance | null>(null)
const validateForm = () => {
    return new Promise((resolve, reject) => {
        if (formRef.value) {
            formRef.value.validate((isValid) => {
                if (isValid) {
                    resolve(formModel)
                } else {
                    reject('valid fail')
                }
            })
        } else {
            reject('none form inst')
        }
    })
}

defineExpose({ validateForm })
</script>
