<template>
    <div class="search">
        <m-card v-model="isShow">
            <el-form ref="form" :model="searchType" label-width="90px">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="商品名称">
                            <el-input v-model="searchType.name" placeholder="请输入商品名称" maxlength="20"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="供应商名称">
                            <el-select v-model="searchType.shopId" filterable remote reserve-keyword
                                placeholder="请输入供应商名称" :remote-method="shopSearchRemote">
                                <el-option v-for="item in shopSearchList" :key="item.id" :label="item.name"
                                    :value="item.id" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="平台类目">
                            <el-cascader v-model="searchType.platformCategoryId" style="width: 224px"
                                :options="categoryList" :props="shopCascaderProps" placeholder="请选择展示分类"
                                :show-all-levels="false" @change="handleChangeCascader" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="商品类型">
                            <el-select v-model="searchType.productType" v-loadMore="{
                                fn: loadMoreHandle,
                            }" placeholder="请选择" style="width: 224px">
                                <el-option v-for="item in typeList" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item style="margin-bottom: 0">
                    <el-button class="from_btn" type="primary" round @click="search">搜索</el-button>
                    <el-button class="from_btn" round @click="handleReset">重置</el-button>
                </el-form-item>
            </el-form>
        </m-card>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import MCard from '@/components/MCard.vue'
import { doGetCategory, doGetShopList } from '@/apis/shops'
import { doGetSeachSupplierSearchList } from '@/apis/good'

const isShow = ref(false)
const categoryList = ref([])

const shopCascaderProps = {
    expandTrigger: 'hover' as 'click' | 'hover',
    label: 'name',
    value: 'id',
}
const typeList = [
    {
        value: '',
        label: '全部',
    },
    {
        value: 'REAL_PRODUCT',
        label: '实物商品',
    },
    {
        value: 'VIRTUAL_PRODUCT',
        label: '虚拟商品',
    },
]
const $emit = defineEmits(['onSearchParams', 'changeShow'])
watch(
    () => isShow.value,
    (val) => {
        $emit('changeShow', val)
    },
)
const shopSearchList = ref<any[]>([])

const shopSearchRemote = async (query: string) => {
    if (query) {
        const { data } = await doGetSeachSupplierSearchList({ supplierName: query })
        shopSearchList.value = data || []
    } else {
        shopSearchList.value = []
    }
}
const searchType = ref({
    name: '',
    platformCategoryId: '',
    secondPlatformCategoryId: '',
    productType: '',
    shopId: '',
})

const handleChangeCascader = (e: any) => {
    searchType.value.secondPlatformCategoryId = ''
    searchType.value.platformCategoryId = ''
    let categoryId = e.pop()
    if (e.length < 2 && categoryId !== '0') {
        searchType.value.secondPlatformCategoryId = categoryId
    }
    searchType.value.platformCategoryId = categoryId
}
async function init() {
    const { data } = await doGetCategory({
        current: 1,
        size: 1000,
    })
    initList(data.records, 'secondCategoryVos')
    data.records.unshift({ categoryId: '0', id: '0', name: '全部类目', parentId: '0', sort: 1 })
    categoryList.value = data.records
}
function initList(list: any[], str: string) {
    list.forEach((item) => {
        if (item[str]) {
            item.children = item[str]
            delete item[str]
            if (item.children.length) {
                initList(item.children, 'categoryThirdlyVos')
            }
        }
    })
}
const loadMoreHandle = (e: any) => {
    $emit('onSearchParams')
}
function search() {
    $emit('onSearchParams', toRaw({
        ...searchType.value,
        platformCategoryId: (searchType.value.secondPlatformCategoryId && searchType.value.platformCategoryId) || searchType.value.platformCategoryId === '0' ? '' : searchType.value.platformCategoryId,
    }))
}
const handleReset = () => {
    // @ts-ignore
    Object.keys(searchType).forEach((key) => (searchType.value[key] = ''))
    search()
}
init()
</script>

<style lang="scss" scoped>
@include b(search) {
    background: #f9f9f9;
}
</style>
