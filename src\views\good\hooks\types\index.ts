export interface SupplierListInterface {
    albumPics: string
    createTime: string
    extra: {
        customDeductionRatio: string
        platformCategory: {
            one: string
            three: string
            two: string
        }
        productAttributes: []
        productParameters: []
    }
    id: string
    productName: string
    productType: 'VIRTUAL_PRODUCT' | 'REAL_PRODUCT'
    salePrices: string[]
    sellType: 'CONSIGNMENT' | 'PURCHASE' | 'OWN'
    shopId: string
    status: 'SELL_ON' | 'SELL_OFF' | 'PLATFORM_SELL_OFF'
    storageSkus: StorageSku[]
    supplierName: string
}

interface StorageSku {
    activityType: string
    id: string
    image: string
    initSalesVolume: string
    limitNum: number
    limitType: 'UNLIMITED' | 'LIMITED'
    minimumPurchase: number
    price: string
    productId: string
    salePrice: string
    salesVolume: string
    shopId: string
    specs: string[]
    stock: string
    stockType: 'LIMITED' | 'UNLIMITED'
}

enum Limit {
    UNLIMITED,
    PRODUCT_LIMITED,
    SKU_LIMITED,
}
