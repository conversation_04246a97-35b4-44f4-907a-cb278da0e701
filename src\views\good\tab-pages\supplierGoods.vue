<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-04-25 15:42:10
 * @LastEditors: lexy 
 * @LastEditTime: 2023-11-25 10:23:10
-->
<template>
    <div>
        <commodity-search ref="commoditySearchRef" :tabs-active="tabsActive" @get-search-params="getSearchParams" @show-change="handleSearchShow" />
        <el-tabs v-model="tabsActive" class="tabs" @tab-change="tabChangeHandle">
            <el-tab-pane label="全部" name=""></el-tab-pane>
            <el-tab-pane label="已上架" name="SELL_ON"></el-tab-pane>
            <el-tab-pane label="已下架" name="SELL_OFF"></el-tab-pane>
            <el-tab-pane label="违规下架" name="PLATFORM_SELL_OFF"></el-tab-pane>
        </el-tabs>
        <q-table
            ref="multipleTableRef"
            v-model:checked-item="tableSelectedArr"
            :data="tableList"
            style="margin-top: 10px"
            class="q-table"
            :style="{ height: tableHeight }"
        >
            <q-table-column label="商品" prop="goodInfo" align="left">
                <template #default="{ row }">
                    <commodity-info :info="row" />
                </template>
            </q-table-column>
            <q-table-column label="所属供应商" align="center" width="250">
                <template #default="{ row }">
                    <span :title="row.supplierName" class="shop-name">{{ row.supplierName }}</span>
                </template>
            </q-table-column>
            <q-table-column label="商品来源" prop="sellType" align="center" width="100px">
                <template #default="{ row }">
                    {{ SellTypeEnum[row?.sellType as keyof typeof SellTypeEnum] }}
                </template>
            </q-table-column>
            <q-table-column label="状态" prop="status" align="center" width="110">
                <template #default="{ row }">
                    <el-tag class="ml-2" :type="row?.status === 'SELL_ON' ? 'success' : 'danger'">{{ usePlatformGoodStatus(row?.status) }}</el-tag>
                </template>
            </q-table-column>
            <q-table-column label="操作" align="center" width="180">
                <template #default="{ row }">
                    <div v-if="row?.status === 'PLATFORM_SELL_OFF'">
                        <el-link :underline="false" type="primary" round @click="showGoodDetailHandle(row)">查看</el-link>
                        <el-link :underline="false" type="primary" round style="margin-left: 10px" @click="showExplain(row?.extra?.productViolation)">
                            违规原因
                        </el-link>
                        <el-link :underline="false" type="danger" round style="margin-left: 10px" @click="recoverHandle(row)">恢复</el-link>
                    </div>
                    <div v-else>
                        <el-link :underline="false" type="primary" round @click="showGoodDetailHandle(row)">查看</el-link>
                        <el-link :underline="false" type="danger" round style="margin-left: 30px" @click="handleBtnChange('PLATFORM_SELL_OFF', row)">
                            违规下架
                        </el-link>
                    </div>
                </template>
            </q-table-column>
        </q-table>
    </div>
    <page-manage
        :page-size="pageConfig.pageSize"
        :page-num="pageConfig.pageNum"
        :total="pageConfig.total"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
    />
    <el-dialog v-model="dialogStatus" title="商品详情" width="60%" top="5vh" destroy-on-close @close="dialogStatus = false">
        <commodity-detail :commodity-id="tableSelectedArr?.[0]?.id" :shop-id="tableSelectedArr?.[0]?.shopId" />
    </el-dialog>

    <el-dialog v-model="dialogSellOff" title="违规下架" width="650px" center destroy-on-close @close="closeSellOffDialog">
        <el-form ref="formSellOffRef" :model="sellOffData" label-width="120px" :rules="rules">
            <el-form-item label="类型（多选）" prop="violationType">
                <el-checkbox-group v-model="sellOffData.violationType">
                    <el-checkbox-button
                        v-for="(violationType, violationTypeKey) in violationTypeMap"
                        :key="violationTypeKey"
                        :label="violationTypeKey"
                    >
                        {{ violationType }}
                    </el-checkbox-button>
                </el-checkbox-group>
            </el-form-item>
            <el-form-item label="违规说明" prop="violationExplain">
                <el-input v-model="sellOffData.violationExplain" style="width: 450px" :maxlength="50"></el-input>
            </el-form-item>
            <el-form-item label="相关证据（最多五张图片）" prop="violationEvidence">
                <div v-for="(item, index) in evidence" :key="index" style="position: relative; margin-right: 20px">
                    <q-upload
                        v-model:src="evidence[index]"
                        :width="80"
                        :height="80"
                        :format="{
                            size: 1,
                            types: ['image/png', 'image/jpg', 'image/jpeg', 'image/gif', 'image/webp', 'image/bmp'],
                            width: 1000,
                            height: 1000,
                            isBeyondLimit: true,
                        }"
                    />
                    <el-icon
                        v-if="item"
                        style="position: absolute; right: -5px; top: -5px; background: #fff; border-radius: 50%"
                        color="#7f7f7f"
                        size="20px"
                        @click="delImgHandle(index)"
                        ><i-ep-circle-close
                    /></el-icon>
                </div>
                <q-upload
                    v-show="evidence.length <= 5"
                    :width="80"
                    :height="80"
                    :format="{ size: 1, types: ['image/png', 'image/jpg'], width: 1000, height: 1000 }"
                    @change="addNewMainSuccess"
                />
            </el-form-item>
        </el-form>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="dialogSellOff = false">取消</el-button>
                <el-button type="primary" @click="handleSellOff"> 确定 </el-button>
            </span>
        </template>
    </el-dialog>
    <el-dialog v-model="dialogExplain" title="违规原因" width="500px" center @close="dialogSellOff = false">
        <div style="line-height: 30px">
            <el-row :gutter="8">
                <el-col :span="12">
                    <div>检查员：{{ explainData.rummager }}</div>
                </el-col>
                <el-col :span="12">
                    <div>检查时间：{{ explainData.examineDateTime }}</div>
                </el-col>
            </el-row>
            <div>类型：{{ explainData.violationType }}</div>
            <div>违规说明：{{ explainData.violationExplain }}</div>
            <div>
                相关证据：
                <img v-for="(pic, picIndex) in explainData.violationEvidence" :key="picIndex" :src="pic" class="violation-evidence" />
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import CommoditySearch from '../components/supplier/CommoditySearch.vue'
import CommodityInfo from '../components/supplier/CommodityInfo.vue'
import QTable from '@/components/qszr-core/packages/q-table/QTable'
import QTableColumn from '@/components/qszr-core/packages/q-table/q-table-column.vue'
import PageManage from '@/components/PageManage.vue'
import commodityDetail from '../components/supplier/commodityDetail.vue'
import { usePlatformGoodStatus } from '@/composables/usePlatformGoodStatus'
import { doUpdateSupplierSellStatus, doGetSupplierList, recoverSupplierGoods } from '@/apis/good'

import { ElMessage, ElMessageBox } from 'element-plus'
import type { ElTable, UploadProps, FormInstance } from 'element-plus'
import type { SupplierListInterface, searchFormType } from '../types/supplier'

const violationTypeMap = {
    PROHIBITED: '违禁品',
    COUNTERFEIT: '假冒伪劣',
    EXCESSIVE_PLATFORM_INTERVENTION: '平台介入率太高',
    TITLE_IRREGULARITY: '标题有问题',
    OTHER: '其他',
}
enum SellTypeEnum {
    CONSIGNMENT = '代销商品',
    PURCHASE = '采购商品',
    OWN = '自有商品',
}
/*
 *variable
 */
const $route = useRoute()
const tabsActive = ref($route.query.name ? String($route.query.name) : '')
const tableHeight = ref('calc(100vh - 330px)')
const tableSelectedArr = ref<SupplierListInterface[]>([])
const tableList = ref<SupplierListInterface[]>([])
const pageConfig = reactive({
    pageSize: 20,
    pageNum: 1,
    total: 0,
})
const handleSearchShow = (e: boolean) => {
    if (e) {
        tableHeight.value = 'calc(100vh - 450px)'
    } else {
        tableHeight.value = 'calc(100vh - 330px)'
    }
}
const searchParams = ref<searchFormType>({
    platformCategoryId: '',
    sellType: '',
    productType: '',
    status: '',
    shopId: '',
    supplierGoodsName: '',
})

// 搜索框组件
const commoditySearchRef = ref<InstanceType<typeof CommoditySearch>>()
const multipleTableRef = ref<InstanceType<typeof ElTable>>()
const dialogStatus = ref(false)
/*
 *lifeCircle
 */
initList()

/**
 * @LastEditors: lexy
 * @description: 操作按钮上下架
 * @param {*} val
 * @returns {*}
 */
const formSellOffRef = ref<FormInstance>()
const rules = {
    violationType: [{ required: true, message: '类型为必选项', trigger: 'change' }],
    violationExplain: [{ required: true, message: '违规说明为必填项', trigger: 'blur' }],
    violationEvidence: [{ required: true, message: '相关证据为必选项', trigger: 'change' }],
}

const dialogSellOff = ref(false)

const platform = import.meta.env.VITE_PLATFORM_NAME

const sellOffData = ref({
    violationType: [],
    violationExplain: '',
    violationEvidence: '',
    rummager: platform,
})
const evidence = ref<string[]>([])
/**
 * @LastEditors: lexy
 * @description: 删除商品主图
 * @param {number} index
 */
const delImgHandle = (index: number) => {
    evidence.value.splice(index, 1)
    sellOffData.value.violationEvidence = evidence.value.join(',')
}

/**
 * @LastEditors: lexy
 * @description: 新增商品主图
当上传图片成功后，将图片的响应信息添加到commodityImgList数组中，并将该数组转换成字符串，赋值给submitForm.value.albumPics。
 * @returns {UploadProps}
 */
const addNewMainSuccess: UploadProps['onSuccess'] = (response, uploadFile) => {
    evidence.value.push(response)
    sellOffData.value.violationEvidence = evidence.value.join(',')
}
// 下架状态
const commandList = reactive([
    {
        label: 'PLATFORM_SELL_OFF',
        name: '下架',
    },
])
// 点击下架商品
let sellOffRow: any = {}
const handleBtnChange = async (val: string, goods: SupplierListInterface) => {
    if (goods['status'] === val) {
        const type = commandList.find((item) => val)?.name
        ElMessage.error(`该商品已${type}`)
        return
    }
    sellOffRow = goods
    dialogSellOff.value = true
}

// 确定下架
const handleSellOff = async () => {
    formSellOffRef.value?.validate(async (valid) => {
        if (valid) {
            const data = {
                productIds: [sellOffRow.id],
                // keys: [{ shopId: sellOffRow.shopId, productId: sellOffRow.id }],
                productViolation: sellOffData.value,
            }
            try {
                const { code, success } = await doUpdateSupplierSellStatus(data, 'PLATFORM_SELL_OFF')
                if (code === 200 && success) {
                    ElMessage.success('更新成功')
                    initList()
                    dialogSellOff.value = false
                } else {
                    ElMessage.error('更新失败')
                }
            } catch (error) {
                return
            }
        }
    })
}
const closeSellOffDialog = () => {
    sellOffData.value = {
        violationType: [],
        violationExplain: '',
        violationEvidence: '',
        rummager: platform,
    }
    evidence.value = []
    dialogSellOff.value = false
}
// 违规弹框
const dialogExplain = ref(false)
// 违规信息
const explainData = ref({ rummager: '', violationType: '', violationExplain: '', violationEvidence: [], examineDateTime: '' })
// 对应数据
const explainMap = {
    rummager: '检查员',
    examineDateTime: '检查时间',
    violationType: '类型',
    violationExplain: '原因',
    violationEvidence: '相关证据',
}
const showExplain = async (productViolation: any = {}) => {
    productViolation.violationEvidence = productViolation?.violationEvidence ? productViolation.violationEvidence.split(',') : []
    const list: string[] = []
    productViolation?.violationType?.forEach((item: keyof typeof violationTypeMap) => {
        list.push(violationTypeMap[item])
    })
    productViolation.violationType = list.join(',')
    explainData.value = productViolation
    dialogExplain.value = true
}

const handleSizeChange = (val: number) => {
    pageConfig.pageNum = 1
    pageConfig.pageSize = val
    initList()
}
const handleCurrentChange = (val: number) => {
    pageConfig.pageNum = val
    initList()
}
const getSearchParams = (params: searchFormType) => {
    searchParams.value = params
    initList()
}
// 获取店铺商品/获取供应商商品
async function initList() {
    let params = {
        ...searchParams.value,
        current: pageConfig.pageNum,
        size: pageConfig.pageSize,
    }
    let code = 200
    let success = true
    let data = {
        records: [],
        total: 0,
    }
    // 供应商商品接口
    const {
        code: goodsCode,
        data: goodsData,
        success: goodsSuccess,
    } = await doGetSupplierList({ ...params, supplierProductStatus: tabsActive.value })
    code = goodsCode
    data = goodsData
    success = goodsSuccess
    if (code === 200 && success) {
        tableList.value = data.records
        pageConfig.total = data.total
    } else {
        ElMessage.error('获取商品列表失败')
    }
}

const tabChangeHandle = () => {
    initList()
}

const showGoodDetailHandle = (goodItem: SupplierListInterface) => {
    tableSelectedArr.value = [goodItem]
    dialogStatus.value = true
}

/**
 * zrb:违规商品恢复
 *
 */
const recoverHandle = async (goodItem: SupplierListInterface) => {
    ElMessageBox.confirm('确定要恢复该商品吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(async () => {
            const data = { productIds: [goodItem.id] }
            try {
                const { code, success } = await recoverSupplierGoods(data)
                if (code === 200 && success) {
                    ElMessage.success('恢复成功')
                    initList()
                } else {
                    ElMessage.error('恢复失败')
                }
            } catch (error) {
                return
            }
        })
        .catch(() => {
            // 处理取消按钮的逻辑
        })
}
</script>
<style scoped lang="scss">
@include b(q-table) {
    overflow: auto;
    transition: height 0.5s;
}

@include b(shop-name) {
    width: 120px;
    text-align: center;
    @include utils-ellipsis(1);
}
.see {
    width: 82px;
    height: 36px;
    background: #eaf5fe;
    border-radius: 21px;
    font-size: 12px;
    color: #309af3;
    line-height: 36px;
    text-align: center;
    cursor: pointer;
}
.violation-evidence {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    object-fit: contain;
    vertical-align: top;
}
.violation-evidence + .violation-evidence {
    margin-left: 5px;
}
</style>
