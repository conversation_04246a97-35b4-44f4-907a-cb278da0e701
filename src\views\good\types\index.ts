/*
 * @description:
 * @Author: lexy
 * @Date: 2022-05-16 11:20:43
 * @LastEditors: lexy
 * @LastEditTime: 2023-07-21 13:30:59
 */
// export type searchFormType = Record<
//     'platformCategoryId' | 'name' | 'createBeginTime' | 'createEndTime' | 'date' | 'shopId' | 'productType' | 'status',
//     string
// >

export type searchFormType = {
    platformCategoryId?: string
    name: string
    createBeginTime?: string
    createEndTime?: string
    date: string
    shopId: string
    productType: string
    status: string
    sellType: string
    productId?: string
}

/**
 * @LastEditors: lexy
 * @description: 平台商品api类型
 * @param {string} albumPics 画册
 * @param {string} name 商品名称
 * @param {string} productStatus 商品状态
 * @param {string} providerName 供货商
 */
export interface ApiGoodType {
    albumPics: string
    createTime: string
    id: string
    name: string
    saleDescribe: string
    shopId: string
    shopName: string
    providerName: string
    pic: string
    storageSkus: ApiGoodSkuType[]
    productStatus?: string
    status?: string
    sellType: keyof typeof SellTypeEnum
    supplierId?: string
    commission?: string
    salePrice?: string
    retailPrice?: string
}

export enum SellTypeEnum {
    CONSIGNMENT = '代销商品',
    PURCHASE = '采购商品',
    OWN = '自有商品',
}
/**
 * @LastEditors: lexy
 * @description: sku类型
 * @param {string}
 * @param {string}
 * @param {string}
 * @param {string}
 */
export interface ApiSkuType {
    id: string
    image: string
    initSalesVolume: number
    limitNum: number
    limitType: 'UNLIMITED'
    price: string
    productId: string
    salePrice: string
    salesVolume: string
    shopId: string
    specs: string[]
    specsIds: string[]
    stock: string
    stockType: 'UNLIMITED' | string
    weight: number
}
/**
 * @LastEditors: lexy
 * @description: 平台商品sku类型
 *  @param {string} stock 库存
 *  @param {string} stockType 库存类型
 *  @param {string}salePrice 单价
 * @returns {*}
 */
export interface ApiGoodSkuType {
    id: string
    image: string
    salePrice: string
    stock: string
    stockType: { name: string }
}
// /**
//  * @LastEditors: lexy
//  * @description: 平台商品sku类型
//  * @param {number} perLimit 限购数量
//  * @param {string[]} productSpecNames 商品规格组
//  * @param {string} specs 商品规格组字符串
//  */
// interface ApiGoodSkuType {
//     id: string
//     perLimit: number
//     price: number
//     productId: string
//     productSpecNames: string[]
//     sale: number
//     specs: string
//     stock: number
//     weight: number
// }

/**
 * 审核状态
 */
export enum ExamineGoodsEnum {
    ALREADY_PASSED = '已通过',
    UNDER_REVIEW = '审核中',
    REFUSE = '已拒绝',
}

/**
 * 限购类型
 */
export enum LIMIT_TYPE {
    UNLIMITED = 'UNLIMITED',
    PRODUCT_LIMITED = 'PRODUCT_LIMITED',
    SKU_LIMITED = 'SKU_LIMITED',
}

/**
 * 限购类型文本
 */
export enum LIMIT_TEXT {
    UNLIMITED = '不限购',
    PRODUCT_LIMITED = '限购',
    SKU_LIMITED = 'SKU限购',
}

/**
 * 类型颜色
 */
export enum LIMIT_TEXT_COLOR {
    UNLIMITED = 'success',
    PRODUCT_LIMITED = 'warning',
    SKU_LIMITED = 'danger',
}

/**
 * 库存类型
 */
export enum STOCK_TYPE {
    UNLIMITED,
    LIMITED,
}

/**
 * 库存类型文本
 */
export enum STOCK_TEXT {
    UNLIMITED = '无限库存',
    LIMITED = '有限库存',
}

/**
 * 库存类型颜色
 */
export enum STOCK_TEXT_COLOR {
    UNLIMITED = 'success',
    LIMITED = 'warning',
}
