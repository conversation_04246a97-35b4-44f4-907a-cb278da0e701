<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-08-23 15:04:37
 * @LastEditors: lexy
 * @LastEditTime: 2022-12-22 13:45:19
-->
<script setup lang="ts">
import { ref, PropType, watch } from 'vue'
import { useVModel } from '@vueuse/core'
import MCard from '@/components/MCard.vue'
import type { EvaluateSearchData } from '@/views/order/appraise/types'
import { JoinMemberType } from '@/apis/decoration/type'
import { doGetMemberType } from '@/apis/decoration'
/*
 *variable
 */
const $props = defineProps({
    searchData: {
        type: Object as PropType<EvaluateSearchData>,
        default: () => ({}),
    },
})
const $emit = defineEmits(['update:searchData', 'click', 'showSearchCardChange'])
const _searchData = useVModel($props, 'searchData', $emit)
const isShowForm = ref(false)
const rateData = ['全部', '一颗星', '二颗星', '三颗星', '四颗星', '五颗星']
const memberTypeData = ref<JoinMemberType[]>([])
/*
 *variable
 */
/*
 *lifeCircle
 */
onMounted(() => {
    getMemberType()
})
watch(
    () => isShowForm.value,
    (val) => {
        $emit('showSearchCardChange', val)
    },
)
/*
 *function
 */
const getMemberType = async () => {
    const res = await doGetMemberType()
    memberTypeData.value = [...res.data]
}
</script>

<template>
    <div class="form">
        <m-card v-model="isShowForm">
            <el-form :model="_searchData">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="商品名称">
                            <el-input v-model="_searchData.name" placeholder="请输入商品名称" maxlength="20" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="买家昵称">
                            <el-input v-model="_searchData.nickname" placeholder="请输入买家名称" maxlength="20" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="成交时间">
                            <el-date-picker
                                v-model="_searchData.clinchTime"
                                format="YYYY/MM/DD"
                                value-format="YYYY-MM-DD"
                                type="daterange"
                                start-placeholder="开始时间"
                                end-placeholder="结束时间"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="评价星级">
                            <el-select v-model="_searchData.rate" placeholder="请选择" style="width: 100%">
                                <el-option v-for="(item, index) in rateData" :key="index" :value="index" :label="item"> </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="会员">
                            <el-select v-model="_searchData.memberType" placeholder="全选" multiple style="width: 100%">
                                <el-option v-for="(item, index) in memberTypeData" :key="index" :value="item.id" :label="item.name"> </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label-width="60px">
                    <el-button class="form__btn" type="primary" round @click="$emit('click')">搜索</el-button>
                </el-form-item>
            </el-form>
        </m-card>
    </div>
</template>

<style scoped lang="scss"></style>
