<script lang="ts" setup>
import { LEXICON_RELATE_TYPE, LEXICON_TYPE } from '@/apis/order/evaluationLexicon'
import { UploadFilled, Download } from '@element-plus/icons-vue'
import request from '@/apis/request'
import { ElMessage, type UploadInstance, type UploadRequestOptions, genFileId } from 'element-plus'
import type { Ref } from 'vue'
import { PropType, ref } from 'vue'

const props = defineProps({
    type: {
        type: String as PropType<keyof typeof LEXICON_TYPE>,
        default: 'USER',
    },
    relateType: {
        type: String as PropType<keyof typeof LEXICON_RELATE_TYPE>,
        default: 'PRODUCT',
    },
})

const $emit = defineEmits(['importSuccess'])
const uploadFileApi = (url: string, formData: FormData) => {
    return request.post(url, formData, {
        headers: {
            'Content-Type': 'multipart/form-data',
        },
        timeout: Number(import.meta.env.VITE_REQUEST_TIME_OUT),
    })
}
const uploadExcelRef: Ref<UploadInstance | null> = ref(null)
const handleUploadRequest = async (options: UploadRequestOptions) => {
    const formData = new FormData()
    formData.append('file', options.file)
    formData.append('type', props.type)
    formData.append('relateType', props.relateType)
    const result = await uploadFileApi('gruul-mall-order/order/evaluate/library/import', formData)
    if (result?.data.code === 200) {
        ElMessage.success({ message: '导入成功' })
        $emit('importSuccess')
    } else {
        ElMessage.error({ message: result?.data.msg || '导入失败' })
        return Promise.reject('导入失败')
    }
}
const handleDownloadTemplate = async () => {
    const eLink = document.createElement('a')
    eLink.href = `${import.meta.env.VITE_CDN_URL}/dev-mall/2025/4/%E8%AE%A2%E5%8D%95%E8%AF%84%E4%BB%B7%E8%AF%8D%E6%A8%A1%E6%9D%BF.xlsx`
    eLink.download = '模版-批量导入词库.xls'
    eLink.click()
}
const onExceed = (files: File[]) => {
    uploadExcelRef.value?.clearFiles()
    const file: any = files[0]
    file.uid = genFileId()
    uploadExcelRef.value?.handleStart(file)
}
defineExpose({ uploadExcelRef })
</script>
<template>
    <el-upload
        ref="uploadExcelRef"
        accept=".xlsx,.xls"
        :on-exceed="onExceed"
        :limit="1"
        :auto-upload="false"
        :http-request="handleUploadRequest"
        drag
        multiple
    >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">拖到这或者 <em>点击此上传</em></div>
        <template #tip>
            <el-link type="primary" style="margin-top: 10px" @click="handleDownloadTemplate">
                <el-icon class="el-icon--upload"><download /></el-icon>
                下载导入模板
            </el-link>
        </template>
    </el-upload>
</template>

<style scoped>
.notice {
    color: #f00;
}
</style>
