<template>
    <el-table :data="tableData" style="padding: 16px 20px" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" fixed="left" />
        <el-table-column v-if="props.relateType === 'PRODUCT'" label="关联商品" prop="productName" :width="240" />
        <el-table-column v-if="props.relateType === 'CATEGORY'" label="关联类目" prop="categoryName" :width="240" />
        <el-table-column label="评价词" prop="content">
            <template #default="{ row }">
                <el-text line-clamp="2">{{ row.content }}</el-text>
            </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="createTime" :width="160" />
        <el-table-column label="操作" :width="160" align="center">
            <template #default="{ row }">
                <el-button link type="primary" @click="handleAction('edit', row.id)">编辑</el-button>
                <el-button link type="danger" @click="handleAction('delete', row.id)">删除</el-button>
            </template>
        </el-table-column>
    </el-table>
    <BetterPageManage
        :page-num="pageConfig.pageNum"
        :page-size="pageConfig.pageSize"
        :total="pageConfig.total"
        :load-init="true"
        @reload="reload(true)"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
    />
</template>

<script setup lang="ts">
import { doDelEvaluateLexicon, doGetEvaluationLexicon, LEXICON_RELATE_TYPE, LEXICON_TYPE } from '@/apis/order/evaluationLexicon'
import BetterPageManage from '@/components/BetterPageManage/BetterPageManage.vue'
import { ElMessageBox } from 'element-plus'
import { ElMessage } from 'element-plus'
import { PropType, ref } from 'vue'
const props = defineProps({
    type: {
        type: String as PropType<keyof typeof LEXICON_TYPE>,
        default: 'USER',
    },
    relateType: {
        type: String as PropType<keyof typeof LEXICON_RELATE_TYPE>,
        default: 'PRODUCT',
    },
})
const emits = defineEmits(['updateLexicon'])
const tableData = ref([])
const selectedRecordsData = ref<any[]>([])
const pageConfig = shallowReactive({
    pageNum: 1,
    pageSize: 20,
    total: 0,
})

const initTableData = () => {
    const { pageNum, pageSize } = pageConfig
    doGetEvaluationLexicon({ current: pageNum, size: pageSize, type: props.type, relateType: props.relateType }).then((res) => {
        if (res.code === 200 && res.data) {
            tableData.value = res?.data?.records || []
            pageConfig.total = res?.data?.total || 0
        }
    })
}

const handleSizeChange = (val: number) => {
    pageConfig.pageNum = 1
    pageConfig.pageSize = val
    reload(true)
}
const handleCurrentChange = (val: number) => {
    pageConfig.pageNum = val
    reload()
}
/**
 * 表格操作方法
 * @param action 操作类型
 * @param id     数据id
 */
const handleAction = (action: 'delete' | 'edit', id: number) => {
    if (action === 'delete') {
        handelDel([id])
    }
    if (action === 'edit') {
        emits('updateLexicon', id)
    }
}

/**
 * 选中的数据变化
 */
const handleSelectionChange = (selectedData: any[]) => {
    selectedRecordsData.value = selectedData
}

/**
 * 处理删除的方法
 * @param ids     需要删除的数据id数组
 */
const handelDel = (ids: number[]) => {
    if (!ids?.length)
        return ElMessage({
            type: 'info',
            message: '请先选择需要批量处理的数组',
        })

    ElMessageBox.confirm(`确定要删除${ids.length > 1 ? '这些' : '该'}词库吗，此操作不可逆?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(async () => {
        doDelEvaluateLexicon(ids).then((res) => {
            if (res.code === 200) {
                reload()
                ElMessage({
                    type: 'success',
                    message: '删除成功',
                })
            } else {
                ElMessage({
                    type: 'info',
                    message: '删除失败',
                })
            }
        })
    })
}

const reload = (reset?: boolean) => {
    if (reset) {
        pageConfig.pageNum = 1
    }
    initTableData()
    selectedRecordsData.value = []
}

const batchDel = () => handelDel(selectedRecordsData.value.map(({ id }) => id))

watch(
    () => [props.type, props.relateType],
    (val) => {
        if (val) {
            reload(true)
        }
    },
    { immediate: true },
)

defineExpose({ batchDel, reload })
</script>
