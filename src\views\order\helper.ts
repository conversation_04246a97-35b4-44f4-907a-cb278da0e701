import { afsStatusHandler } from './orderDetails/OrderStatusCalculate'
import type { ApiOrder, ShopOrderItem } from './types/order'
import { calculate } from './orderDetails/OrderStatusCalculate'

export const getOrderDeliveryStatus = (row: ApiOrder, shopOrderItems: ShopOrderItem[]) => {
    const currentOrderStatus = calculate(row).state.status
    if (['待发货', '待收货', '已完成', '待发货(部分发货)', '待评价'].includes(currentOrderStatus)) {
        let deliveredCount = 0,
            finishCount = 0
        shopOrderItems.forEach((shopOrderItem) => {
            if (shopOrderItem.status === 'OK') {
                let currentStatus = ''
                if (shopOrderItem.packageStatus === 'WAITING_FOR_DELIVER') {
                    deliveredCount++
                } else {
                    finishCount++
                }
            }
        })
        if (deliveredCount === shopOrderItems.length) {
            return '待发货'
        } else if (finishCount === shopOrderItems.length) {
            return '已发货'
        } else {
            return '部分发货'
        }
    }
}
/**
 * @description 获取当前订单是否处于售后状态
 * @param row 订单数据
 * @returns { boolean } 是否处于售后状态
 */
export const getOrderAfterSaleStatus = (row: ApiOrder) => {
    const shopOrderItems = row.shopOrders?.[0]?.shopOrderItems || []
    for (const shopOrderItem of shopOrderItems) {
        const { ing } = afsStatusHandler[shopOrderItem.afsStatus]
        if (ing) return true
    }
    return false
}

/**
 * @description 获取支付方式
 * @param row 当前订单信息
 * @returns 支付方式
 */
export const getPaymentType = (row: ApiOrder) => {
    const payMap = { ALIPAY: '支付宝支付', BALANCE: '余额支付', WECHAT: '微信支付' }
    const orderPaymentType = row?.orderPayment?.type
    return payMap[orderPaymentType] || ''
}
/**
 * @description 获取配送方式
 * @param row 当前订单信息
 * @returns 配送方式
 */
export const getDeliveryMode = (row: ApiOrder) => {
    const distributionModeMap = {
        EXPRESS: '快递配送',
        INTRA_CITY_DISTRIBUTION: '同城配送',
        SHOP_STORE: '到店自提',
        VIRTUAL: '无需物流',
        MERCHANT: '快递配送',
    }
    const distributionMode = row?.extra?.distributionMode
    return distributionModeMap[distributionMode] || ''
}
