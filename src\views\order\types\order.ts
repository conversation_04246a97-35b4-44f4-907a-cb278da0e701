/*
 * @description:
 * @Author: lexy
 * @Date: 2022-06-27 18:38:21
 * @LastEditors: lexy
 * @LastEditTime: 2023-07-12 13:38:08
 */
import { AFSSTATUS } from '@/views/afs/types'
import { SellTypeEnum } from '@/views/good/types'

/**
 * @LastEditors: lexy
 * @description: 订单状态
 * @param  UNPAID 未支付
 * @param  PAID 已支付
 * @param  BUYER_CLOSED 买家关闭订单
 * @param  SYSTEM_CLOSED 系统关闭订单
 * @param  SELLER_CLOSED 卖家关闭订单
 */
export enum ORDERSTATUS {
    UNPAID,
    PAID,
    BUYER_CLOSED,
    SYSTEM_CLOSED,
    SELLER_CLOSED,
    TEAMING,
    TEAM_FAIL,
}

/**
 * @LastEditors: lexy
 * @description: 订单tab状态
 * @param UNPAID 待支付
 * @param UN_DELIVERY 待发货
 * @param UN_RECEIVE 待收货
 * @param COMPLETED 已完成
 * @param CLOSED 已关闭
 */
export enum QUERYORDERSTATUS {
    UNPAID,
    UN_DELIVERY,
    UN_RECEIVE,
    COMPLETED,
    CLOSED,
}

/**
 * @LastEditors: lexy
 * @description: 订单类型
 * @param COMMON 商品订单
 * @param SPIKE 秒杀
 */
export enum ORDERTYPE {
    COMMON,
    SPIKE,
}

export enum ORDERPAYMENT {
    WECHAT,
    ALIPAY,
    BALANCE,
    YST,
}
export enum PAY_MODE {
    SCAN_WEIXIN = 'SCAN_WEIXIN',
    WECHAT_PUBLIC = 'WECHAT_PUBLIC',
    WECHATPAY_MINIPROGRAM = 'WECHATPAY_MINIPROGRAM',
    SCAN_ALIPAY = 'SCAN_ALIPAY',
    ALIPAY_SERVICE = 'ALIPAY_SERVICE',
}

/**
 * @LastEditors: lexy
 * @description: 订单支付状态
 * @param CLOSED 取消支付
 * @param UNPAID 未支付
 * @param PAID 已支付
 */
export enum ORDERPAYMENTSTATUS {
    CLOSED,
    UNPAID,
    PAID,
}

/**
 * @LastEditors: lexy
 * @description: 优惠类型
 * @param PLATFORM_COUPON 平台优惠券
 * @param SHOP_COUPON 店铺优惠券
 * @param FULL_REDUCTION 满减
 */
enum DISCOUNTSOURCETYPE {
    PLATFORM_COUPON,
    SHOP_COUPON,
    FULL_REDUCTION,
}

/**
 * @LastEditors: lexy
 * @description: 优惠状态
 */
enum DISCOUNTSOURCESTATUS {
    NORMAL,
    CLOSED,
}

/**
 * @LastEditors: lexy
 * @description: 商铺订单状态
 * @param UNPAID 未支付
 * @param PAID 支付
 * @param SYSTEM_CLOSED 系统关闭
 * @param BUYER_CLOSED  买家关闭订单
 * @param SELLER_CLOSED  卖家关闭订单
 */
export enum SHOPORDERSTATUS {
    OK,
    SYSTEM_CLOSED,
    BUYER_CLOSED,
    SELLER_CLOSED,
}

/**
 * @LastEditors: lexy
 * @description: 包裹状态
 * @param WAITING_FOR_DELIVER 待发货
 * @param WAITING_FOR_RECEIVE 已发货待收货
 * @param BUYER_WAITING_FOR_COMMENT 买家确认收货 待评价
 * @param SYSTEM_WAITING_FOR_COMMENT 系统确认收货 待评价
 * @param BUYER_COMMENTED_COMPLETED 买家已评论 已完成
 * @param SYSTEM_COMMENTED_COMPLETED 系统自动好评 已完成
 * @param BUYER_REQUEST_REFUND 买家申请退款 仅退款
 * @param  REFUNDED  卖家同意退款
 * @param  BUYER_REQUEST_RETURNS_REFUND   买家申请退货退款
 */
export enum PACKAGESTATUS {
    WAITING_FOR_DELIVER,
    WAITING_FOR_RECEIVE,
    BUYER_WAITING_FOR_COMMENT,
    SYSTEM_WAITING_FOR_COMMENT,
    BUYER_COMMENTED_COMPLETED,
    SYSTEM_COMMENTED_COMPLETED,
}

/**
 * @LastEditors: lexy
 * @description: 包裹配置方式
 * @param EXPRESS 快递
 */
enum PACKAGETYPE {
    EXPRESS,
}

/**
 * @LastEditors: lexy
 * @param: WITHOUT  无需物流发货
 * @param: EXPRESS 普通发货 自己填 物流公司与 单号
 * @param: PRINT_EXPRESS 打印发货
 * @returns {*}
 */
export enum DeliverType {
    WITHOUT,
    EXPRESS,
    PRINT_EXPRESS,
}

/**
 * @LastEditors: lexy
 * @description:
 * @returns {*}
 */
export enum SHOPITEMSTATUS {
    OK,
    CLOSED,
}
/**
 * @LastEditors: lexy
 * @description: 配送方式
 */
export enum DISTRIBUTION {
    MERCHANT,
    EXPRESS, //快递配送
    INTRA_CITY_DISTRIBUTION, //同城配送
    SHOP_STORE, //店铺门店
    VIRTUAL, // 无需物流
}

/**
 * @LastEditors: lexy
 * @description: 订单类型
 * @param {string} buyerId 买家用户id
 * @param {string} no 订单号
 * @param {ORDERSTATUS} status 订单状态
 * @param {ORDERTYPE} type 订单类型
 * @param {OrderPayment} orderPayment 订单支付相关信息
 * @param {OrderDiscount} orderDiscounts 订单优惠相关
 * @param {ShopOrder} shopOrders 店铺订单相关
 */
export interface ApiOrder {
    id: string
    shopId: string
    buyerId: string
    createTime: string
    buyerNickname: string
    updateTime: string
    no: string
    status: keyof typeof ORDERSTATUS
    type: keyof typeof ORDERTYPE
    remark: string
    orderPayment: OrderPayment
    orderDiscounts: OrderDiscount[]
    shopOrders: ShopOrder[]
    orderReceiver: OrderReceiver
    shopOrderPackages: ApiLogistics01[]
    extra: {
        distributionMode: keyof typeof DISTRIBUTION
    }
    checked?: boolean
}

export interface OrderListSearchData {
    no: string
    buyerNickname: string
    productName: string
    receiverName: string
    startTime: string
    endTime: string
}

export interface OrderDataType {
    records: ApiOrder[]
}

/**
 * @LastEditors: lexy
 * @description: 订单接收
 * @returns {*}
 */
export interface OrderReceiver {
    address: string
    areaCode: string[]
    id: string
    mobile: string
    name: string
}

/**
 * @LastEditors: lexy
 * @description: 支付相关信息
 * @param payerId 支付用户id
 * @param type 支付类型
 * @param status 支付状态
 * @param totalAmount 订单总金额
 * @param freightAmount 总运费
 * @param discountAmount 优惠总金额
 * @param payTime 支付时间
 * @param payAmount 支付总金额金额 = 订单总金额 - 优惠总金额
 */
export interface OrderPayment {
    createTime: string
    updateTime: string
    shopId: string
    orderId: string
    payerId: string
    type: keyof typeof ORDERPAYMENT
    status: keyof typeof ORDERPAYMENTSTATUS
    totalAmount: number
    freightAmount: number
    discountAmount: number
    payAmount: number
    payTime: string
    payMode: keyof typeof PAY_MODE
}

/**
 * @LastEditors: lexy
 * @description: 订单优惠
 * @param sourceType 优惠类型
 * @param sourceStatus 优惠状态
 * @param sourceId 优惠源Id
 * @param sourceAmount 优惠金额
 * @param sourceDesc 优惠信息描述
 * @param discountItems 优惠项对应商品
 */
export interface OrderDiscount {
    id: string
    shopId: string
    orderId: string
    sourceType: keyof typeof DISCOUNTSOURCETYPE
    sourceStatus: keyof typeof DISCOUNTSOURCESTATUS
    sourceId: string
    sourceAmount: number
    sourceDesc: string
    discountItems: OrderDiscountItem[]
}

/**
 * @LastEditors: lexy
 * @description: 优惠对应的商品
 * @param packageId 店铺包裹id
 * @param packageItemId 店铺包裹商品id
 * @param discountId 优惠项id
 */
export interface OrderDiscountItem {
    itemId: string
    shopId: string
    packageId: string
    packageItemId: string
    discountId: string
    discountAmount: string
}

export enum SHOP_TYPE_ENUM {
    SELF_OWNED = '自营',
    PREFERRED = '优选',
    ORDINARY = '普通',
}

/**
 * @LastEditors: lexy
 * @description: 店铺订单相关
 * @param no 店铺订单号
 * @param remark 店铺订单备注
 */
export interface ShopOrder {
    id: string
    no: string
    status: keyof typeof SHOPORDERSTATUS
    shopId: string
    orderId: string
    shopName: string
    shopLogo: string
    remark: string
    shopOrderItems: ShopOrderItem[]
    orderReceiver?: OrderReceiver
    extra: {
        deliverTime?: string
        receiveTime?: string
    }
    shopType?: keyof typeof SHOP_TYPE_ENUM
    // shopOrderPackages: ShopOrderPackage[]
}

export interface ShopOrderItem {
    afsNo: string
    afsStatus: keyof typeof AFSSTATUS
    freightPrice: string
    freightTemplateId: string
    status: keyof typeof SHOPITEMSTATUS
    id: string
    image: string
    num: number
    packageStatus: keyof typeof PACKAGESTATUS
    orderId: string
    productId: string
    productName: string
    salePrice: string
    dealPrice: string
    fixPrice: string
    shopId: string
    skuId: string
    specs: string[]
    weight: number
    packageId?: string
    sellType?: keyof typeof SellTypeEnum
    supplierShopType?: keyof typeof SHOP_TYPE_ENUM
    supplierName?: string
}

/**
 * @LastEditors: lexy
 * @description: 商铺订单包裹相关
 * @param no 店铺订单包裹id
 * @param shopOrderId 店铺订单id
 * @param freightTemplateId 运费模板id
 * @param freightPrice 用户支付的运费
 * @param  status 包裹状态
 * @param  type  配送方式
 * @param  receiverName  收货人名称
 * @param  receiverMobile  收货人电话
 * @param  receiverAreaCode  省市区code
 * @param  receiverAddress  收货人详细地址
 * @param  receiveTime  确认收货时间
 * @param  shopOrderPackageItems  确认收货时间
 */
export interface Apipackage {
    confirmTime: string
    createTime: string
    deleted: boolean
    deliveryTime: string
    expressCompanyCode: string
    expressCompanyName: string
    expressNo: string
    id: string
    orderNo: string
    receiverAddress: string
    receiverMobile: string
    receiverName: string
    remark: string
    shopId: string
    status: string
    type: string
    updateTime: string
    deliverShopName?: string
}

export interface ApiLogistics01 {
    createTime: string
    deleted: false
    deliveryTime?: string
    expressCompanyCode: string
    expressCompanyName: string
    expressNo: string
    id: string
    orderNo: string
    receiverAddress: string
    receiverMobile: string
    receiverName: string
    remark: string
    shopId: string
    status: keyof typeof PACKAGESTATUS
    type: keyof typeof DeliverType
    updateTime: string
    version: 0
    // 确认收货时间
    confirmTime?: string
    //  评论时间
    commentTime?: string
    success: true
}
export interface ExtraMap {
    AllDeliveryCount: string
    miniDeliveryCount: string
}
