<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-05-25 00:00:56
 * @LastEditors: lexy
 * @LastEditTime: 2023-03-09 19:19:09
-->
<!--
 * @description: 权限设置
 * @Author: lexy
-->
<template>
    <div class="tab_container">
        <el-tabs v-model="currentTab">
            <el-tab-pane v-for="tab in tabs" :key="tab.name" :label="tab.label" :name="tab.name" />
        </el-tabs>
    </div>
    <component :is="reactiveComponent[currentTab as keyof typeof reactiveComponent]" />
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

const tabs = [
    { label: '角色管理', name: 'Role' },
    { label: '管理员列表', name: 'Admin' },
]
const currentTab = ref('Admin')

const reactiveComponent = reactive({
    Admin: defineAsyncComponent(() => import('./AdminList.vue')),
    Role: defineAsyncComponent(() => import('./RoleList.vue')),
})
</script>

<style scoped lang="scss"></style>
