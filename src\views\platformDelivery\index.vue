<template>
    <div class="tab_container" style="padding-top: 10px">
        <el-tabs v-model="activeName">
            <el-tab-pane label="自营商家发货" name="shopDelivery" />
            <el-tab-pane label="地址管理" name="addressManage" />
            <el-tab-pane label="物流服务" name="logisticsService" />
            <el-tab-pane label="物流设置" name="logisticsSet" />
            <el-tab-pane label="打印设置" name="printSet" />
        </el-tabs>
    </div>
    <component :is="reactiveComponent[activeName as keyof typeof reactiveComponent]" />
</template>

<script lang="ts" setup>
import { defineAsyncComponent, ref } from 'vue'

const activeName = ref('shopDelivery')

const reactiveComponent = {
    shopDelivery: defineAsyncComponent(() => import('./pages/shop-delivery.vue')),
    addressManage: defineAsyncComponent(() => import('./pages/address-manage/index.vue')),
    logisticsService: defineAsyncComponent(() => import('./pages/logistics-service/index.vue')),
    logisticsSet: defineAsyncComponent(() => import('./pages/logistics-set.vue')),
    printSet: defineAsyncComponent(() => import('./pages/print-set.vue')),
}
</script>
