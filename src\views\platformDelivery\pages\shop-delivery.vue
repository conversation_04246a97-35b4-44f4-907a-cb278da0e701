<template>
    <el-form :model="shopDeliveryConfigModel" class="form">
        <el-form-item label="自营店铺用户订单" prop="shopDeliver">
            <el-radio-group v-model="shopDeliveryConfigModel.shopDeliver">
                <el-radio label="PLATFORM">平台发货</el-radio>
                <el-radio label="OWN">店铺自己发货</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item label="自营供应商用户订单" prop="shopDeliver">
            <el-radio-group v-model="shopDeliveryConfigModel.supplierDeliver">
                <el-radio label="PLATFORM">平台发货</el-radio>
                <el-radio label="OWN">供应商自己发货</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item>
            <div class="desc">平台发货是指平台可以发(货)所有自营商家的订单(不含采购订单) ，商家自己发货是指各自营商家自己发货</div>
        </el-form-item>
        <el-form-item>
            <div class="submit">
                <el-button type="primary" @click="handleSave">保存</el-button>
            </div>
        </el-form-item>
    </el-form>
</template>

<script lang="ts" setup>
import { reactive } from 'vue'
import { doGetShopDeliveryConfig, doPutShopDeliveryConfig } from '@/apis/set/platformDelivery'
import { ElMessage } from 'element-plus'

const shopDeliveryConfigModel = reactive({
    id: '',
    shopDeliver: 'OWN',
    supplierDeliver: 'OWN',
})

const initialDeliveryConfig = async () => {
    const { data } = await doGetShopDeliveryConfig()
    if (data?.id) {
        Object.keys(shopDeliveryConfigModel).forEach((key) => {
            const modelKey = key as keyof typeof shopDeliveryConfigModel
            shopDeliveryConfigModel[modelKey] = data?.[modelKey] || shopDeliveryConfigModel[modelKey]
        })
    }
}

const handleSave = async () => {
    const { code, msg } = await doPutShopDeliveryConfig(shopDeliveryConfigModel)
    if (code === 200) {
        ElMessage.success({ message: msg || '更新成功' })
        initialDeliveryConfig()
    } else {
        ElMessage.error({ message: msg || '更新失败' })
    }
}
initialDeliveryConfig()
</script>

<style lang="scss" scoped>
.form {
    padding: 15px;
}
.desc {
    color: #999;
}
.submit {
    padding-left: 120px;
}
</style>
