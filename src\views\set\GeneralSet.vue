<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-05-14 13:19:51
 * @LastEditors: lexy
 * @LastEditTime: 2023-09-12 10:28:19
-->
<script setup lang="ts">
import type { Component } from 'vue'

interface AsyncComponent {
    BasicSet: Component
    FileSet: Component
    MessageSet: Component
    WechatPay: Component
    PayTreasurePay: Component
    PrintSet: Component
    LogisticsExpress: Component
    PrivateAgreement: Component
    RateJobSet: Component
}
type ActiveName =
    | 'BasicSet'
    | 'FileSet'
    | 'MessageSet'
    | 'WechatPay'
    | 'PayTreasurePay'
    | 'LogisticsExpress'
    | 'PrintSet'
    | 'PrivateAgreement'
    | 'RateJobSet'
/*
 *variable
 */

const tabPaneSet = [
    { label: '基础设置', name: 'BasicSet' },
    { label: '微信支付', name: 'WechatPay' },
    { label: '支付宝支付', name: 'PayTreasurePay' },
    { label: '消息配置', name: 'MessageSet' },
    // { label: '物流设置', name: 'PrintSet' },
    { label: '物流快递', name: 'LogisticsExpress' },
    { label: '扣率任务配置', name: 'RateJobSet' },
    { label: 'OSS配置', name: 'FileSet' },
    { label: '协议配置', name: 'PrivateAgreement' },
]
const activeName = ref<ActiveName>('BasicSet')
// 动态组件列表
const reactiveComponent = reactive<AsyncComponent>({
    BasicSet: defineAsyncComponent(() => import('./components/BasicSet.vue')),
    FileSet: defineAsyncComponent(() => import('./components/FileSet.vue')),
    MessageSet: defineAsyncComponent(() => import('./components/MessageSet.vue')),
    WechatPay: defineAsyncComponent(() => import('./components/WechatPay.vue')),
    PrintSet: defineAsyncComponent(() => import('./components/PrintSet.vue')),
    PayTreasurePay: defineAsyncComponent(() => import('./components/PayTreasurePay.vue')),
    LogisticsExpress: defineAsyncComponent(() => import('./components/LogisticsExpress.vue')),
    PrivateAgreement: defineAsyncComponent(() => import('./components/PrivateAgreement.vue')),
    RateJobSet: defineAsyncComponent(() => import('./components/RateJobSet/index.vue')),
})
/**
 * @LastEditors: lexy
 * @description: 通用设置tab栏切换数据
 * @returns {*}
 */
</script>

<template>
    <div class="tab_container" style="padding-top: 10px; padding-bottom: 0">
        <el-tabs v-model="activeName" class="demo-tabs">
            <el-tab-pane v-for="tabItem in tabPaneSet" :key="tabItem.name" :label="tabItem.label" :name="tabItem.name" />
        </el-tabs>
    </div>
    <component :is="reactiveComponent[activeName]" />
</template>
<style lang="scss" scoped></style>
