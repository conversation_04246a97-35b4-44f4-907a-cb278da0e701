<template>
    <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑执行任务' : '新增执行任务'" width="50%" destroy-on-close>
        <el-form ref="rateJobForm" label-width="120px" :model="form" :rules="rules">
            <el-form-item label="执行类目" label-width="120px" prop="categoryIds" required>
                <el-row gutter="24" style="width: 100%">
                    <el-col :span="18">
                        <el-cascader
                            v-model="form.categoryIds"
                            :content-ref="categoryRef"
                            placeholder="请选择平台类目"
                            :options="categoryList"
                            :props="cascaderProps"
                            clearable
                            :show-all-levels="false"
                            style="width: 100%"
                            @change="changeCategoryIds"
                        />
                    </el-col>
                    <el-col :span="2" :offset="1">
                        <el-checkbox v-model="selectAllCategory" label="全部类目" size="small" border @change="changeSelectAll" />
                    </el-col>
                </el-row>
            </el-form-item>
            <el-form-item label="执行范围" required>
                <el-radio-group v-model="form.scope" @change="handleScopeChange">
                    <el-radio :value="'ALL'">{{ SCOPE_KEY_TEXT.ALL }}</el-radio>
                    <el-radio :value="'ALL_SUPPLIER'">{{ SCOPE_KEY_TEXT.ALL_SUPPLIER }}</el-radio>
                    <el-radio :value="'ALL_SHOP'">{{ SCOPE_KEY_TEXT.ALL_SHOP }}</el-radio>
                    <el-radio :value="'SPECIFIED_SUPPLIER'">{{ SCOPE_KEY_TEXT.SPECIFIED_SUPPLIER }}</el-radio>
                    <el-radio :value="'SPECIFIED_SHOP'">{{ SCOPE_KEY_TEXT.SPECIFIED_SHOP }}</el-radio>
                </el-radio-group>
            </el-form-item>
            <template v-if="form.scope && (form.scope === 'SPECIFIED_SHOP' || form.scope === 'SPECIFIED_SUPPLIER')">
                <el-form-item label="执行商户" prop="shopIds" required>
                    <el-select
                        v-model="selectedShopIds"
                        placeholder="请选择执行商户"
                        multiple
                        filterable
                        clearable
                        :filter-method="filterShop"
                        :reserve-keyword="false"
                        style="width: 80%"
                        @input="handleShopSelectionChange"
                        @change="handleShopSelectionChange"
                        @visible-change="handleVisibleChange"
                    >
                        <template #empty>
                            <div v-if="pageConfig.loading" class="loading-text">加载中...</div>
                            <div v-else>暂无数据</div>
                        </template>
                        <div style="max-height: 300px; overflow-y: auto" @scroll="handleScroll">
                            <el-option v-for="item in shopListData" :key="item.value" :label="item.label" :value="item.value" />
                        </div>
                    </el-select>
                </el-form-item>
            </template>
            <template v-if="form.scope === 'ALL' || form.scope?.includes('SHOP')">
                <!-- 商家扣率 -->
                <el-form-item label="执行费率" prop="deductionRatio" required>
                    <el-input-number v-model="form.deductionRatio" :min="0" :max="100" :step="1" placeholder="请输入执行扣率" :controls="false">
                        <template #suffix>
                            <span>%</span>
                        </template>
                    </el-input-number>
                </el-form-item>
            </template>
            <template v-if="form.scope?.includes('SUPPLIER')">
                <!-- 供应商扣率 -->
                <el-form-item label="执行费率" prop="supplierDeductionRatio" required>
                    <el-input-number
                        v-model="form.supplierDeductionRatio"
                        :min="0"
                        :max="100"
                        :step="1"
                        placeholder="请输入执行扣率"
                        :controls="false"
                    >
                        <template #suffix>
                            <span>%</span>
                        </template>
                    </el-input-number>
                </el-form-item>
            </template>

            <el-form-item label="执行时间" required>
                <!-- 禁止今天以前的时间 -->
                <el-date-picker
                    v-model="form.executeTime"
                    type="datetime"
                    placeholder="请选择任务执行时间"
                    format="YYYY-MM-DD HH:mm"
                    value-format="YYYY-MM-DD HH:mm"
                    style="width: 80%"
                    :disabled-date="disabledDate"
                >
                </el-date-picker>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="handleCancel">取消</el-button>
                <el-button type="primary" @click="handleSubmit">确认</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules, CascaderProps } from 'element-plus'
import { RateJobType, SCOPE_KEY_TEXT, CategoryItem } from '.'
import { doGetCategory, doGetShopList } from '@/apis/shops'
import { doAddRateJob, doGetRateJobDetail, doEditRateJob } from '@/apis/set/RateJob/index'

const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    editId: {
        type: String,
        default: '',
    },
})

const emit = defineEmits(['update:show', 'success'])

const selectAllCategory = ref<boolean>(false)

const categoryRef = ref()

// 表单引用
const rateJobForm = ref<FormInstance>()

// 搜索关键字
const keyword = ref('')

// 弹窗显示状态
const dialogVisible = computed({
    get: () => props.show,
    set: (val) => emit('update:show', val),
})

// 是否为编辑模式
const isEdit = computed(() => !!props.editId)

// 表单数据
const form = ref<RateJobType>({
    scope: 'ALL',
    executeTime: '',
    categoryIds: '',
    shopIds: '',
})

// 表单校验规则
const rules = reactive<FormRules>({
    scope: [{ required: true, message: '请选择执行对象', trigger: 'change' }],
    deductionRatio: [{ required: true, message: '请输入执行扣率', trigger: 'blur' }],
    supplierDeductionRatio: [{ required: true, message: '请输入执行扣率', trigger: 'blur' }],
    shopIds: [{ required: true, message: '请选择执行商户', trigger: 'change' }],
    categoryIds: [{ required: true, message: '请选择执行类目', trigger: 'change' }],
    executeTime: [{ required: true, message: '请选择执行时间', trigger: 'change' }],
})

// 类目列表
const categoryList = ref<CategoryItem[]>([])

// 级联选择器配置
const cascaderProps = {
    value: 'id',
    label: 'name',
    children: 'secondCategoryVos',
    expandTrigger: 'hover',
    multiple: true,
} as CascaderProps

// 店铺列表数据
const shopListData = ref<any[]>([])
// 选中的店铺ID数组
const selectedShopIds = ref<string[]>([])

// 分页配置
const pageConfig = reactive({
    current: 1,
    size: 10,
    loading: false,
    total: 0,
})

// 生命周期钩子
onMounted(() => {
    getCategory()
})

// 监听弹窗显示状态变化
watch(
    () => dialogVisible.value,
    async (val) => {
        if (val && isEdit.value) {
            // 编辑模式，获取详情
            await getDetailAndFormat(props.editId)
        } else if (val) {
            // 新增模式，重置表单
            resetForm()
        }
    },
)

// 监听选择选中全部类目的变化
const changeSelectAll = (val) => {
    if (val) {
        let ids: Array<string[]> = []
        categoryList.value.map(({ categoryId, secondCategoryVos }) => {
            if (secondCategoryVos) {
                secondCategoryVos?.forEach(({ categoryId: secCategoryId }) => ids.push([categoryId, secCategoryId]))
            } else {
                ids.push([categoryId])
            }
        })
        form.value.categoryIds = ids
    } else {
        form.value.categoryIds = []
    }
}

/**
 * 获取类目列表
 */
const getCategory = async () => {
    try {
        const { data } = await doGetCategory({
            current: 1,
            size: 1000,
        })

        // data.records.unshift({ categoryId: '0', id: '0', name: '全部类目', parentId: '0', sort: 1 })
        categoryList.value = data.records
    } catch (error) {
        console.error('获取类目列表失败:', error)
    }
}

/**
 * 过滤店铺
 */
const filterShop = (query: string) => {
    if (query) {
        keyword.value = query
        pageConfig.current = 1
        pageConfig.total = 0
        shopListData.value = []
        loadShopData()
    }
}

/**
 * 加载店铺数据
 */
const loadShopData = async () => {
    if (pageConfig.loading) return

    pageConfig.loading = true
    try {
        const params = {
            current: pageConfig.current,
            size: pageConfig.size,
            shopModes: form.value.scope === 'SPECIFIED_SHOP' ? 'COMMON' : 'SUPPLIER',
            name: '',
        }
        if (keyword.value) {
            params.name = keyword.value
        }

        const { data } = await doGetShopList(params)
        pageConfig.total = data.total || 0
        if (data && data.records) {
            // 转换数据格式
            const shopOptions = data.records.map((item) => ({
                value: item.id,
                label: item.name,
            }))

            if (pageConfig.current === 1) {
                shopListData.value = shopOptions
            } else {
                shopListData.value = [...shopListData.value, ...shopOptions]
            }

            // 如果还有更多数据，更新页码
            if (shopListData.value.length < Number(data.total)) {
                pageConfig.current++
            }
        }
    } catch (error) {
        console.error('加载店铺数据失败:', error)
    } finally {
        pageConfig.loading = false
    }
}
/**
 * 格式化详情数据
 */
const formatDetailData = () => {
    // 格式化分类数据
    if (form.value.categoryIds || form.value.categoryList) {
        // 补充选中数据的父级id
        if (typeof form.value.categoryIds === 'string') {
            form.value.categoryIds = form.value.categoryIds.split(',')
        }

        if (form.value.categoryList && form.value.categoryList.length > 0) {
            form.value.categoryList.forEach((cate: any, index: number) => {
                for (let i = 0; i < categoryList.value.length; i++) {
                    if (categoryList.value[i].id === cate.id) {
                        form.value.categoryIds[index] = [categoryList.value[i].id]
                    }
                    if (categoryList.value[i].secondCategoryVos?.length > 0) {
                        categoryList.value[i].secondCategoryVos.forEach((item2: any) => {
                            if (item2.id === cate.id) {
                                form.value.categoryIds[index] = [categoryList.value[i].id, item2.id]
                            }
                        })
                    }
                }
            })
        }
    }

    // 格式化店铺数据
    if (form.value.shopIds || form.value.shopList) {
        // 如果是字符串，转换为数组
        if (typeof form.value.shopIds === 'string' && form.value.shopIds) {
            selectedShopIds.value = form.value.shopIds.split(',').filter(Boolean)
        } else if (Array.isArray(form.value.shopList)) {
            selectedShopIds.value = form.value.shopList.map((item: any) => item.id)
        }

        // 如果店铺列表为空，加载数据
        if (selectedShopIds.value.length > 0 && shopListData.value.length === 0) {
            loadShopData()
        }
    }
}

/**
 * 获取详情后格式化数据
 */
const getDetailAndFormat = async (id: string) => {
    try {
        const { data } = await doGetRateJobDetail(id)
        if (data) {
            Object.assign(form.value, {
                ...data,
                deductionRatio: Number(data.deductionRatio) || 0,
                supplierDeductionRatio: Number(data.supplierDeductionRatio) || 0,
            })

            // 确保分类数据已加载
            if (!categoryList.value.length) {
                await getCategory()
            }

            // 格式化详情数据
            formatDetailData()
        }
    } catch (error) {
        console.error('获取详情失败:', error)
    }
}

/**
 * 处理店铺选择变化
 */
const handleShopSelectionChange = (values: any) => {
    keyword.value = ''
    if (typeof values === 'string') {
        return
    }
    if (Array.isArray(values)) {
        form.value.shopIds = values.join(',')
    } else if (values) {
        form.value.shopIds = String(values)
    } else {
        form.value.shopIds = ''
    }
}

/**
 * 监听下拉框打开事件
 */
const handleVisibleChange = (visible: boolean) => {
    if (visible) {
        pageConfig.current = 1
        pageConfig.total = 0
        shopListData.value = []
        loadShopData()
    }
}

/**
 * 滚动到底部加载更多
 */
const handleScroll = (event: any) => {
    const { scrollTop, scrollHeight, clientHeight } = event.target
    // 当滚动到底部时，加载更多数据
    if (scrollTop + clientHeight >= scrollHeight - 20 && !pageConfig.loading && shopListData.value.length < Number(pageConfig.total)) {
        loadShopData()
    }
}

/**
 * 处理范围选择变化
 */
const handleScopeChange = () => {
    // 清空店铺选择
    pageConfig.current = 1
    pageConfig.total = 0
    shopListData.value = []
    selectedShopIds.value = []
    form.value.shopIds = ''
}

/**
 * 禁止今天以前的时间
 */
const disabledDate = (time: Date) => {
    return time.getTime() < Date.now()
}

/**
 * 重置表单
 */
const resetForm = () => {
    form.value = {
        scope: 'ALL',
        executeTime: '',
        categoryIds: '',
        shopIds: '',
    }
    selectAllCategory.value = false
    selectedShopIds.value = []
    rateJobForm.value?.resetFields()
}

/**
 * 取消操作
 */
const handleCancel = () => {
    dialogVisible.value = false
    resetForm()
}

/**
 * 修改选中类目
 */
const changeCategoryIds = (val: string | string[]) => {
    if (categoryList.value?.length === val?.length) {
        selectAllCategory.value = true
        categoryList.value?.forEach((category: CategoryItem) => {
            const allChild = category.secondCategoryVos?.find((secCategory: CategoryItem) => !val.includes(secCategory.categoryId))

            if (!val.includes(category.categoryId) || allChild) {
                selectAllCategory.value = false
            }
        })
    } else {
        selectAllCategory.value = false
    }
}

/**
 * 提交表单
 */
const handleSubmit = async () => {
    try {
        await rateJobForm.value?.validate()

        // 处理类目ID
        const categoryIds = Array.isArray(form.value.categoryIds)
            ? form.value.categoryIds.flatMap((item: any) => (Array.isArray(item) ? item[item.length - 1] : item)).join(',')
            : form.value.categoryIds

        // 验证费率
        if (form.value.scope.includes('SHOP')) {
            if (!form.value.deductionRatio) {
                ElMessage.error('请输入商家扣率')
                return
            }
        }
        if (form.value.scope.includes('SUPPLIER')) {
            if (!form.value.supplierDeductionRatio) {
                ElMessage.error('请输入供应商扣率')
                return
            }
        }
        if (form.value.scope === 'ALL') {
            if (!form.value.deductionRatio) {
                ElMessage.error('请输入执行扣率')
                return
            }
            form.value.supplierDeductionRatio = form.value.deductionRatio
        }

        const data: RateJobType = { ...form.value, categoryIds }

        if (isEdit.value) {
            // 编辑模式
            data.id = props.editId
            const { code, msg } = await doEditRateJob(data)
            if (code !== 200) {
                ElMessage.error(msg)
                return
            }
            ElMessage.success('编辑执行任务成功')
        } else {
            // 新增模式
            const { code, msg } = await doAddRateJob(data)
            if (code !== 200) {
                ElMessage.error(msg)
                return
            }
            ElMessage.success('添加执行任务成功')
        }

        // 关闭弹窗并通知父组件刷新
        dialogVisible.value = false
        emit('success')
        resetForm()
    } catch (error) {
        console.error('提交表单失败:', error)
    }
}
</script>

<style lang="scss" scoped>
.loading-text {
    text-align: center;
    padding: 10px 0;
    color: #909399;
}
</style>
