<template>
    <el-dialog v-model="show" title="任务详情" width="50%" destroy-on-close>
        <el-descriptions :column="1" border>
            <el-descriptions-item label="执行类目" prop="categoryList">
              <el-space wrap>
                <el-tag v-for="item in jobDetail.categoryList" effect="dark">{{ item.name }}</el-tag>
              </el-space>
            </el-descriptions-item>
            <el-descriptions-item label="执行范围" prop="scope">
                {{ jobDetail.scope }}
            </el-descriptions-item>
            <el-descriptions-item label="执行商户" prop="shopList">
                <el-space wrap>
                    <el-tag v-for="item in jobDetail.shopList" effect="plain">{{ item.name }}</el-tag>
                </el-space>
            </el-descriptions-item>
            <el-descriptions-item label="执行费率" prop="deductionRatio">
                {{ jobDetail.deductionRatio || jobDetail.supplierDeductionRatio || 0}}%
            </el-descriptions-item>
            <el-descriptions-item label="执行时间" prop="executeTime">
                {{ jobDetail.executeTime }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间" prop="createTime">
                {{ jobDetail.createTime }}
            </el-descriptions-item>
        </el-descriptions>
    </el-dialog>
</template>
<script setup lang="ts">
import { useVModel } from '@vueuse/core'
import { RateJobType } from './index'
import { PropType } from 'vue'

const props = defineProps({
    detail: {
        type: Object as PropType<RateJobType>,
        default: () => {},
    },
    show: {
        type: Boolean,
        default: false,
    },
})
const emit = defineEmits(['update:show'])
const show = computed({
    get: () => props.show,
    set: (val) => {
        emit('update:show', val)
    },
})
const jobDetail = useVModel(props, 'detail')
</script>
