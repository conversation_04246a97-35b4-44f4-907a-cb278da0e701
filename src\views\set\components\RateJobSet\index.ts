import { ShopType } from '@/apis/decoration/type'
import { CategoryType } from '@/apis/goods/type'
export type RateJobType = {
    id?: string
    executeTime: string
    categoryIds: string[] | string
    deductionRatio?: string
    supplierDeductionRatio?: string
    scope: SCOPE_KEY
    shopIds?: string
    createTime?: string
    shopList?: ShopType[]
    categoryList?: CategoryType[]
}

export type SCOPE_KEY = 'ALL' | 'ALL_SUPPLIER' | 'ALL_SHOP' | 'SPECIFIED_SUPPLIER' | 'SPECIFIED_SHOP'

export enum SCOPE_KEY_VAL {
    ALL = 0,
    ALL_SUPPLIER = 1,
    ALL_SHOP = 2,
    SPECIFIED_SUPPLIER = 3,
    SPECIFIED_SHOP = 4,
}

export enum SCOPE_KEY_TEXT {
    ALL = '全部',
    ALL_SUPPLIER = '全部供应商',
    ALL_SHOP = '全部商家',
    SPECIFIED_SUPPLIER = '部分供应商',
    SPECIFIED_SHOP = '部分商家',
}

export type CategoryItem = {
    id: string
    categoryId: string
    name: string
    secondCategoryVos?: CategoryItem[]
}
