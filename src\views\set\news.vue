<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-05-06 10:40:39
 * @LastEditors: lexy
 * @LastEditTime: 2022-12-21 14:56:16
-->
<script setup lang="ts">
import PageManage from '@/components/PageManage.vue'
import { Search } from '@element-plus/icons-vue'
import { doGetNewsList, doPostNews, doPutNews, doPatchNews, doDelNews } from '@/apis/setting'
import { ElMessage, ElMessageBox } from 'element-plus'
import QEdit from '@/components/q-editor/q-edit.vue'
import type { FormRules } from 'element-plus'
/*
 *variable
 */
const searchValue = ref('')
const tableData = ref([])
const dialogFlag = ref(false)
const isEdit = ref(false)
const ruleFormRef = ref()
const formRule = reactive<FormRules>({
    title: [
        {
            required: true,
            message: '请输入标题',
            trigger: 'blur',
        },
        { min: 2, max: 64, message: '标题请输入2~64之间', trigger: 'blur' },
    ],
    content: [
        {
            required: true,
            message: '请输入内容',
            trigger: 'blur',
        },
    ],
})
const submitForm = ref({
    id: '',
    title: '',
    content: '',
})
const pageConfig = reactive({
    pageSize: 10,
    pageNum: 1,
    total: 0,
})
/*
 *lifeCircle
 */
onMounted(() => {
    GetNewsList()
})

/*
 *function
 */
/**
 * @LastEditors: lexy
 * @description: 初始化列表方法
 */
const GetNewsList = async () => {
    const params = {
        keywords: searchValue.value,
        type: 'ANNOUNCEMENT',
        current: pageConfig.pageNum,
        size: pageConfig.pageSize,
    }
    const { data } = await doGetNewsList(params)
    tableData.value = data.records
    pageConfig.total = data.total
}
/**
 * @LastEditors: lexy
 * @description: 搜索
 */
const Searchlist = () => {
    GetNewsList()
}
/**
 * @LastEditors: lexy
 * @description: 关闭弹窗
 */
const dialogCloseHandle = () => {
    isEdit.value = false
    dialogFlag.value = false
    submitForm.value = {
        id: '',
        title: '',
        content: '',
    }
}
/**
 * @LastEditors: lexy
 * @description: 提交表单
 *
 * */
const submitHandle = async () => {
    const flag = await ruleFormRef.value.validate()
    console.log('submitForm.value.content.trim()', submitForm.value.content.trim())
    if (!submitForm.value.content.trim() || submitForm.value.content === '<p><br></p>') {
        ElMessage.error('请输入内容')
        return
    }
    if (flag) {
        const { code, success } = isEdit.value
            ? await doPutNews(submitForm.value.id, { ...submitForm.value })
            : await doPostNews({ ...submitForm.value })
        if (code === 200 && success) {
            ElMessage.success(`${isEdit.value ? '更新成功' : '添加成功'}`)
            GetNewsList()
            dialogFlag.value = false
        }
    }
}
/**
 *  @LastEditors: lexy
 * @description: 编辑表单
 */
const editHandle = (val: any) => {
    isEdit.value = true
    submitForm.value = val
    dialogFlag.value = true
}
/**
 *  @LastEditors: lexy
 * @description: 推送消息
 */
const pushMessage = async (id: any) => {
    const { code, success } = await doPatchNews(id)
    if (code === 200 && success) {
        ElMessage.success(`推送成功`)
        GetNewsList()
    }
}
/**
 *  @LastEditors: lexy
 * @description: 删除消息
 */
const removalMessage = (id: any) => {
    ElMessageBox.confirm('确认删除当前公告？', '提示').then(async () => {
        const { code, success } = await doDelNews(id)
        if (code === 200 && success) {
            ElMessage.success(`删除成功`)
            GetNewsList()
        }
    })
}
const handleSizeChange = (val: number) => {
    pageConfig.pageNum = 1
    pageConfig.pageSize = val
    GetNewsList()
}
const handleCurrentChange = (val: number) => {
    pageConfig.pageNum = val
    GetNewsList()
}
</script>

<template>
    <div>
        <div class="news__head">
            <el-button type="primary" round @click="dialogFlag = true">新增消息</el-button>
            <div style="display: flex">
                <el-input v-model="searchValue" placeholder="请输入内容" maxlength="150" @keypress.enter="Searchlist">
                    <template #append>
                        <el-button :icon="Search" @click="Searchlist"></el-button>
                    </template>
                </el-input>
            </div>
        </div>
        <el-table :data="tableData" style="width: 100%" :header-row-style="{ background: '#f6f8fa' }" :row-style="{ height: '60px' }">
            <el-table-column type="index" label="序号" :index="1" width="150" />
            <el-table-column prop="title" label="标题" width="260" />
            <el-table-column prop="createTime" label="创建时间" width="300" />
            <el-table-column label="操作">
                <template #default="scope">
                    <el-link :underline="false" class="news__head--tablecell" @click="editHandle(scope.row)"> 编辑</el-link>
                    <el-link v-if="!scope.row.pushed" :underline="false" class="news__head--tablecell" @click="pushMessage(scope.row.id)">
                        推送消息</el-link
                    >
                    <el-link :underline="false" class="news__head--tablecell" @click="removalMessage(scope.row.id)"> 删除</el-link>
                </template>
            </el-table-column>
        </el-table>
        <page-manage
            :page-size="pageConfig.pageSize"
            :page-num="pageConfig.pageNum"
            :total="pageConfig.total"
            @handle-size-change="handleSizeChange"
            @handle-current-change="handleCurrentChange"
            @reload="GetNewsList"
        />
        <el-dialog v-model="dialogFlag" :title="isEdit ? '编辑消息' : '新增消息'" destroy-on-close @close="dialogCloseHandle">
            <el-form ref="ruleFormRef" :model="submitForm" :rules="formRule" label-width="100px">
                <el-form-item label="标题" prop="title">
                    <el-input v-model="submitForm.title" placeholder="请填写通知标题" maxlength="20"></el-input>
                </el-form-item>
                <el-form-item label="通知内容" prop="content">
                    <div style="border: 1px solid #ccc">
                        <q-edit v-model:content="submitForm.content" />
                    </div>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogFlag = false">取消</el-button>
                    <el-button type="primary" @click="submitHandle">确认</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<style lang="scss" scoped>
@include b(news) {
    @include e(head) {
        @include flex(space-between);
        margin-bottom: 13px;
        @include m(tablecell) {
            color: #2e99f3;
            margin-right: 22px;
        }
    }
}
:deep(.el-dialog__headerbtn) {
    z-index: -100;
}
</style>
