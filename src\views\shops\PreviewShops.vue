<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-03-26 14:55:22
 * @LastEditors: lexy
 * @LastEditTime: 2023-07-18 14:31:07
-->
<template>
    <div ref="AddShops" class="shopForm">
        <el-steps :active="stepIndex" simple>
            <el-step title="1.基本信息" icon="none"></el-step>
            <el-step title="2.信息登记" icon="none"></el-step>
            <el-step title="3.收款账户" icon="none"></el-step>
            <el-step title="4.认证&签署" icon="none"></el-step>
        </el-steps>
        <keep-alive>
            <component
                :is="reactiveComponent[currentStep]"
                ref="componentRef"
                :supplier-view-model="supplierViewModel"
                @map-change="handleMapChange"
            ></component>
        </keep-alive>
        <div class="shopForm__tool">
            <el-button v-if="prevStep !== ''" type="default" round @click="preHandle">上一步</el-button>
            <el-button v-if="nextStep !== ''" type="primary" round @click="nextHandle">下一步</el-button>
            <el-button v-else type="info" round @click="$router.back()"> 返回 </el-button>
        </div>
    </div>
</template>

<script lang="ts" setup>
import type { Ref, Component } from 'vue'
import type { FormInstance } from 'element-plus'
import { useRoute } from 'vue-router'
import storage from '@/utils/Storage'
import { ShopFormType, OPERATOR_STATUS, BankAccountType } from './types'
import { doGetShopSigningCategoryList } from '@/apis/shops'

interface AsyncComponent {
    NewShopBase: Component
    NewShopInfo: Component
    NewShopFinance: Component
    NewShopAuthSign: Component
}
type EnumStep = 'NewShopBase' | 'NewShopInfo' | 'NewShopFinance' | 'NewShopAuthSign'
const componentRef = ref()
const $route = useRoute()
const firstAssignment = ref(true)
const currentStep = ref<EnumStep>('NewShopBase')

// 供应商视图模式
const supplierViewModel = computed(() => ($route.path.includes('supplier') ? '供应商' : '店铺'))
const navBackUrl = computed(() => ($route.path.includes('supplier') ? 'shopList' : 'supplierPage'))
// 当前步骤
// 动态组件列表
const reactiveComponent: AsyncComponent = {
    NewShopBase: defineAsyncComponent(() => import('./components/NewShopBase.vue')),
    NewShopInfo: defineAsyncComponent(() => import('./components/NewShopInfo.vue')),
    NewShopFinance: defineAsyncComponent(() => import('./components/NewShopFinance.vue')),
    NewShopAuthSign: defineAsyncComponent(() => import('./components/NewShopAuthSign.vue')),
}

// 当前添加步骤
const currentStepIndicator = computed(() => {
    return stepIndicator[currentStep.value]
})

const stepIndex = computed(() => {
    return currentStepIndicator.value.stepIndex
})

const prevStep = computed(() => {
    return currentStepIndicator.value.prev
})

const nextStep = computed(() => {
    return currentStepIndicator.value.next
})

// 商铺添加步骤
const stepIndicator = {
    NewShopBase: {
        prev: '',
        next: 'NewShopInfo',
        stepIndex: 0,
    },
    NewShopInfo: {
        prev: 'NewShopBase',
        next: 'NewShopFinance',
        stepIndex: 1,
    },
    NewShopFinance: {
        prev: 'NewShopInfo',
        next: 'NewShopAuthSign',
        stepIndex: 2,
    },
    NewShopAuthSign: {
        prev: 'NewShopFinance',
        next: '',
        stepIndex: 3,
    },
}

onMounted(() => {
    initShopForm()
    if ($route.query.step) {
        currentStep.value = $route.query.step as EnumStep
    }
})
// 初始数据
const submitForm = ref<ShopFormType>({
    // companyName: '',
    address: '',
    bankAcc: '',
    bankAccount: {
        payee: '',
        bankName: '',
        openAccountBank: '',
        bankAccount: '',
        acctAttr: '1',
        bankReservePhone: '',
        openBankNo: '',
        payBankNumber: '',
        openBankProvince: '',
        openBankCity: '',
    },
    briefing: '',
    contractNumber: '',
    location: { type: 'Point', coordinates: ['121.583336', '29.990282'] },
    logo: '',
    name: '',
    registerInfo: {
        license: '',
        handheldPhoto: '',
        legalPersonIdFront: '',
        legalPersonIdBack: '',
        enterpriseAdress: '',
        startDate: '',
        endDate: '',
        groupName: '',
        creditCode: '',
        validType: '',
        validityPeriod: [],
        legalPersonName: '',
        legalPersonNo: '',
        operatorStatus: OPERATOR_STATUS.NONE,
    },
    // shopTitelPhoto: '',
    // shopDetailsPhoto: [],
    otherQualifications: [],
    registerMobile: '',
    subjectType: 'COMPANY',
    shopType: 'ORDINARY',
    signingCategory: [],
    extractionType: 'CATEGORY_EXTRACTION',
    drawPercentage: '',
    mode: 'B2B2C',
    shopMode: 'COMMON',
    businessType: 'TRUSTEESHIP',
})
const handleMapChange = (e: { address: string; position: string[] }) => {
    // 编辑状态下初始化不对详细地址进行重写
    if ($route.query.shopId && firstAssignment.value) {
        firstAssignment.value = false
        return
    }
    submitForm.value.address = e.address
    submitForm.value.location.coordinates = e.position
}
const tempArr: Ref<FormInstance>[] = []
/**
 * @LastEditors: lexy
 * @description: 存储组件实例
 * @param {*} component
 */
const handleSaveComponent = (component: Ref<FormInstance>) => {
    if (!tempArr[stepIndex.value]) {
        tempArr.push(componentRef.value)
    }
}

const handleGetComponent = () => {
    if (tempArr[stepIndex.value]) {
        return tempArr[stepIndex.value]
    } else {
        return componentRef.value
    }
}

// 上一步
const preHandle = () => {
    handleSaveComponent(componentRef.value)
    currentStep.value = prevStep.value as EnumStep
}
// 下一步
const nextHandle = () => {
    handleGetComponent().currentFormRef.validate((valid: any) => {
        if (valid || $route.path.includes('preview')) {
            handleSaveComponent(componentRef.value)
            currentStep.value = nextStep.value as EnumStep
        }
    })
}
async function initShopForm() {
    if ($route.query.shopId) {
        const SHOPITEM = new storage().getItem('SHOPITEM')
        const { registerInfo, bankAccount, address, otherQualifications } = SHOPITEM
        submitForm.value = {
            ...SHOPITEM,
            // shopDetailsPhoto: shopDetailsPhoto?.indexOf(',') > -1 ? shopDetailsPhoto.split(',') : [],
            otherQualifications: otherQualifications?.indexOf(',') > -1 ? otherQualifications.split(',') : [],
        }
        if (registerInfo) {
            const { startDate, endDate, addressCode, operatorStatus } = registerInfo
            if (startDate || endDate) {
                await nextTick(() => {
                    submitForm.value.registerInfo.validityPeriod = [startDate, endDate]
                    // submitForm.value.registerInfo.handheldPhoto = handheldPhoto
                })
            }
            if (addressCode) {
                console.log(addressCode)
                if (addressCode === '710000') {
                    submitForm.value.registerInfo.addressCodes = [addressCode]
                } else {
                    const proviceCode = addressCode.substring(0, 2)
                    if (['11', '12', '31', '50', '81', '82'].includes(proviceCode)) {
                        submitForm.value.registerInfo.addressCodes = [proviceCode + '0000', addressCode]
                    } else {
                        const cityCode = addressCode.substring(0, 4)
                        submitForm.value.registerInfo.addressCodes = [proviceCode + '0000', cityCode + '00', addressCode]
                    }
                }
            }
        }
        if (bankAccount) {
            for (let item in bankAccount) {
                if (item !== 'bankAccount') {
                    submitForm.value[item as keyof BankAccountType] = bankAccount[item]
                }
            }
            submitForm.value.bankAccount.openBankPC = [bankAccount.openBankProvince, bankAccount.openBankCity]
            submitForm.value['address'] = address
            // 结局bankAccount重名问题 重新赋值
            submitForm.value['bankAcc'] = bankAccount.bankAccount
        }

        if (!submitForm.value['subjectType']) {
            submitForm.value['subjectType'] = 'COMPANY'
            submitForm.value['bankAccount']['acctAttr'] = '1'
        }
        // if (handheldPhoto) {
        //     await nextTick(() => {
        //         submitForm.value.registerInfo.handheldPhoto = handheldPhoto
        //     })
        // }
        const { code, success, data } = await doGetShopSigningCategoryList({ shopId: submitForm.value.id })
        if (success) {
            const signingCategoryList = data?.map((item) => ({
                id: item.id,
                firstName: item.parentName,
                parentId: item.parentId,
                customDeductionRatio: item.customDeductionRatio,
                name: item.currentCategoryName,
                currentCategoryId: item.currentCategoryId,
                deductionRatio: item?.deductionRatio,
                supplierDeductionRatio: item.supplierDeductionRatio,
            }))
            submitForm.value.signingCategory = signingCategoryList
            if (!submitForm.value.registerMobile) {
                submitForm.value.registerMobile = submitForm.value.userMobile
            }
        }
    }
}
// 暴露属性
provide('addShops', {
    submitForm,
})
</script>
<style lang="scss">
@include b(shopForm) {
    overflow: scroll;
    // min-height: 800px;

    .shopForm_steps {
        position: sticky;
        top: 0;
        z-index: 1000;
    }
}
@include b(shopForm__tool) {
    width: 100%;
    padding: 15px 0px;
    display: flex;
    justify-content: center;
    box-shadow: 0 0px 10px 0px #d5d5d5;
    background-color: white;
    margin-top: auto;
}
</style>
