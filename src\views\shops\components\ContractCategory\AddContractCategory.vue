<script lang="ts" setup>
import type { Ref } from 'vue'
import type { SelectOptionsProps } from './type'
import { doGetCategoryLevelByParentId, doGetCategoryTableList } from '@/apis/shops'
import { ElMessage } from 'element-plus'
import DecimalInput from '@/components/decimal-input/decimal-input.vue'

const $route = useRoute()
const typeName = computed(() => ($route.path.includes('supplier') ? '供应商' : '商家'))
const props = defineProps({
    checkedList: {
        type: Array<string>,
        default: () => [],
    },
})
const emitFn = defineEmits(['selectionChange'])
const filterForm = reactive({
    firstCategory: undefined,
})
const firstCategoryList: Ref<SelectOptionsProps[]> = ref([])
const secondCategoryList: Ref<any[]> = ref([])

const handleSelectionChange = (selectionList: any[]) => {
    emitFn('selectionChange', selectionList)
}
const getFirstCategoryList = async () => {
    const { data, success } = await doGetCategoryLevelByParentId('LEVEL_1', 0, 1000, 1)
    if (success) {
        firstCategoryList.value = data.records?.map((item: any) => ({ value: item.id, label: item.name }))
    } else {
        ElMessage.error('获取一级类目失败')
    }
}
const getSecondCategoryList = async (val: string[]) => {
    secondCategoryList.value = []
    doGetCategoryTableList(val.join(',')).then((res) => {
        secondCategoryList.value = res.data.flatMap((item) => ({ ...item, firstName: item.parentName }))
    })
}
onMounted(() => {
    getFirstCategoryList()
})
</script>
<template>
    <div class="contract">
        <el-form :model="filterForm">
            <el-form-item label="一级类目" prop="firstCategory">
                <el-select
                    v-model="filterForm.firstCategory"
                    placeholder="请选择一级类目"
                    multiple
                    collapse-tags
                    value-key="value"
                    :max-collapse-tags="3"
                    @change="getSecondCategoryList"
                >
                    <el-option
                        v-for="firstCategory in firstCategoryList"
                        :key="firstCategory.value"
                        :label="firstCategory.label"
                        :value="firstCategory.value"
                    />
                </el-select>
            </el-form-item>
        </el-form>
        <el-form ref="tableFormRef">
            <el-table :data="secondCategoryList" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" :selectable="(row) => !props.checkedList.includes(row.id)" />
                <el-table-column label="一级类目" prop="parentName" />
                <el-table-column label="二级类目" prop="name" />
                <el-table-column v-if="$route.path.includes('supplier')" label="供应商扣率" prop="deductionRatio">
                    <template #default="{ row }"> {{  row?.supplierDeductionRatio }}% </template>
                </el-table-column>
                <el-table-column v-else :label="`${typeName}扣率`" prop="deductionRatio">
                    <template #default="{ row }"> {{  row?.deductionRatio }}% </template>
                </el-table-column>
                <el-table-column :label="`自定义${typeName}扣率`">
                    <template #default="{ row }">
                        <div class="flex-box">
                            <decimal-input
                                v-model="row.customDeductionRatio"
                                class="input-number"
                                :min="0"
                                :max="100"
                                :decimal-places="2"
                                :controls="false"
                            >
                                <template #suffix>%</template>
                            </decimal-input>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </el-form>
    </div>
</template>

<style lang="scss" scoped>
@include b(flex-box) {
    @include flex();
    @include b(input-number) {
        flex: 1;
    }
    @include b(el-tag) {
        flex-shrink: 0;
    }
}
</style>
