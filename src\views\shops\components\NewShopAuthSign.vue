<template>
    <el-space fill wrap :fill-ratio="80" :direction="'vertical'" style="width: 100%; margin: 30px auto 80px; min-height: -webkit-fill-available">
        <el-descriptions column="1" label-width="120">
            <template #title>
                <div class="des-title">认证</div>
            </template>
            <el-descriptions-item label="实名认证" label-class-name="item-name" :class-name="['item-info', `color_${personAuthColor}`]">
                {{ REAL_NAME_STATUS[infos.realNameStatus] }}
            </el-descriptions-item>
            <el-descriptions-item label="开户认证" label-class-name="item-name" :class-name="['item-info', `color_${accountAuthColor}`]">
                {{ OPEN_ACC_STATUS[infos.openAccStatus] }}
            </el-descriptions-item>
            <el-descriptions-item label="二维码" label-class-name="item-name" class-name="item-img">
                <el-space :direction="'vertical'">
                    <el-image :src="miniProgramQrCode" :preview-src-list="[miniProgramQrCode]" :style="{ height: '100px', width: '100px' }" />
                    <el-button v-if="miniProgramQrCode" link type="primary" @click="downloadbase64(miniProgramQrCode, '认证二维码')">
                        下载
                    </el-button>
                </el-space>
            </el-descriptions-item>
        </el-descriptions>
        <el-descriptions column="1" label-width="160">
            <template #title>
                <div class="des-title">合同</div>
            </template>
            <el-descriptions-item>
                <el-table
                    row-key="contractName"
                    :data="infos.contractInfoVOS"
                    :border="false"
                    style="width: 100%; margin-top: -10px"
                    :header-row-style="{ background: 'rgb(245, 247, 250)' }"
                    default-expand-all
                    :tree-props="{ children: 'childVos' }"
                >
                    <el-table-column prop="createDate" label="创建日期" width="180" />
                    <el-table-column prop="contractName" label="合同名称" />
                    <el-table-column prop="signDate" label="签署日期" width="160" />
                    <el-table-column prop="contractStatus" label="签署状态" width="80">
                        <template #default="{ row }">
                            <el-text :type="CONTRACT_STATUS_COLOR[row.contractStatus]">
                                {{ CONTRACT_DETAIL_STATUS[row.contractStatus] || '' }}
                            </el-text>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="100">
                        <template #default="{ row }">
                            <el-button-group v-if="row.contractStatus === CONTRACT_DETAIL_STATUS.已签署">
                                <el-button v-if="row.signUrl" link @click="toNewPage(row.signUrl)"> 查看 </el-button>
                                <el-button v-if="row.ifDownload" link type="primary" @click="downloadContract(row.fileUrl, row.contractName)">
                                    下载
                                </el-button>
                            </el-button-group>
                        </template>
                    </el-table-column>
                </el-table>
            </el-descriptions-item>
        </el-descriptions>
    </el-space>
</template>

<script lang="ts" setup>
import { doGetShopAuthSignContractInfos } from '@/apis/contract'
import { ref } from 'vue'
import {
    AUTH_SIGN_INFO_TYPE,
    AUTH_STATUS_COLOR,
    CONTRACT_DETAIL_STATUS,
    CONTRACT_STATUS_COLOR,
    OPEN_ACC_STATUS,
    OPEN_ACC_STATUS_COLOR,
    REAL_NAME_STATUS,
} from '../types'
import { doGetWxChat } from '@/apis/shops'

const $route = useRoute()
const infos = ref<AUTH_SIGN_INFO_TYPE>({
    realNameStatus: 0,
    openAccStatus: 0,
    contractNumber: '',
    legalPersonPhone: '',
    bankReservePhone: '',
    shopId: '',
    userId: '',
    serialNo: '',
    contractInfoVOS: [],
    authType: 'COMPANY',
})
const miniProgramQrCode = ref<string>()

const personAuthColor = computed(() => AUTH_STATUS_COLOR[infos.value.realNameStatus])
const accountAuthColor = computed(() => OPEN_ACC_STATUS_COLOR[infos.value.openAccStatus])

onMounted(() => {
    getAuthSignInfo($route.query?.shopId as string)
    getQrCodeImge($route.query?.shopId as string)
})
const getQrCodeImge = (shopId: string) => {
    const ENV_MODE = import.meta.env.MODE as keyof typeof MODE_MAP
    const MODE_MAP = {
        dev: 'develop',
        test: 'trial',
        prod: 'release',
    }
    doGetWxChat({
        path: decodeURIComponent('/basePackage/pages/contract/list?signShopID=' + shopId),
        envVersion: MODE_MAP[ENV_MODE],
        width: '100',
    }).then((res) => {
        if (res.data) {
            miniProgramQrCode.value = res.data
        }
    })
}
/** 获取当前页面信息 */
const getAuthSignInfo = (shopId: string) => {
    doGetShopAuthSignContractInfos(shopId).then((res) => {
        if (res.data) infos.value = res.data
    })
}
/** 跳转合同页面 */
const toNewPage = (url: string) => {
    window.open(url, '_blank')
}
/** 下载合同 */
const downloadContract = (url: string, contractName?: string) => {
    if (!url) return
    const link = document.createElement('a')
    link.href = url
    link.target = '_blank'
    link.click()
    link.remove()
}
const downloadbase64 = (data: string, name?: string) => {
    var base64 = data.toString() // imgSrc 就是base64哈
    var byteCharacters = window.atob(base64.replace(/^data:image\/(png|jpeg|jpg);base64,/, ''))
    var byteNumbers = new Array(byteCharacters.length)
    for (var i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i)
    }
    var byteArray = new Uint8Array(byteNumbers)
    var blob = new Blob([byteArray], {
        type: undefined,
    })
    var aLink = document.createElement('a')
    aLink.download = name + '.jpg' //这里写保存时的图片名称
    aLink.href = URL.createObjectURL(blob)
    aLink.target = '_blank'
    aLink.click()
    aLink.remove()
}
</script>
<style scoped lang="scss">
.des-title {
    color: rgb(153, 169, 191);
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
}

.item-name {
    color: #5e6d82;
    font-size: 14px;
    height: 35px;
    line-height: 35px;
}

.item-info {
    font-size: 14px;
    font-weight: 400;
    height: 35px;
    line-height: 35px;
}

.item-img {
    height: 132px;
}
</style>
<style>
.color_success {
    color: #00b42a !important;
}

.color_waiting {
    color: #ff7d00 !important;
}

.color_danger {
    color: #f53f3f !important;
}
</style>
