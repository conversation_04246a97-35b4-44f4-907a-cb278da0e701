<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-04-18 14:44:47
 * @LastEditors: lexy
 * @LastEditTime: 2024-04-25 17:54:00
-->
<template>
    <div class="shopBaseForm">
        <el-form ref="currentFormRef" :model="submitForm" :rules="baseFormRules" :disabled="$route.meta.disabled">
            <!-- <el-form-item label="商户名称" prop="companyName">
                <el-input v-model="submitForm.companyName" placeholder="请输入商户名称" maxlength="20" show-word-limit></el-input>
            </el-form-item> -->
            <el-form-item :label="`${supplierViewModel}名称`" prop="name">
                <el-input v-model="submitForm.name" :placeholder="`请输入${supplierViewModel}名称`" maxlength="20"></el-input>
            </el-form-item>
            <el-form-item :label="`${supplierViewModel}logo`" prop="logo">
                <div v-if="!submitForm.logo" class="selectMaterialStyle" @click="buttonlFn('logo')">
                    <span class="selectMaterialStyle__span">+</span>
                </div>
                <img v-else class="selectMaterialStyle" :src="submitForm.logo" alt="" @click="buttonlFn('logo')" />
                <div class="group-cus-logo">
                    <random-avatar
                        :name="cusAvatarLabel || submitForm.name"
                        :max-length="3"
                        :size="60"
                        @handle-avatar-url="(url:string) => cusAvatarUrl = url"
                    />
                    <el-input
                        v-model="cusAvatarLabel"
                        :placeholder="`自定义头像展示字符`"
                        :size="'small'"
                        maxlength="3"
                        clearable
                        :validate-event="false"
                        style="width: 160px"
                    />
                    <el-button-group class="ml-4">
                        <el-button text size="small">刷新</el-button>
                        <el-button text size="small" type="primary" @click="handleApplyLogo">应用</el-button>
                        <el-button text size="small" type="danger" @click="handleDelLogo">删除</el-button>
                    </el-button-group>
                </div>
            </el-form-item>
            <el-form-item label="用户账号" prop="registerMobile">
                <el-select
                    v-model="submitForm.registerMobile"
                    allow-create
                    filterable
                    remote
                    reserve-keyword
                    remote-show-suffix
                    :default-first-option="true"
                    :remote-method="loadUserInfo"
                    :loading="userLoading"
                    placeholder="请输入用户账号（联系电话）"
                    @change="changeRegister"
                >
                    <el-option v-for="item in userOptions" :key="item.mobile" :label="item.mobile" :value="item.mobile">
                        <span style="float: left">{{ item.mobile }}</span>
                        <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">
                            {{ item.nickname }}
                        </span>
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="联系方式" prop="contractNumber">
                <el-input v-model="submitForm.contractNumber" placeholder="请输联系方式" maxlength="11"></el-input>
            </el-form-item>
            <el-form-item label="详情地址" prop="address">
                <el-input v-model="submitForm.address" placeholder="请选择经纬度" maxlength="60"></el-input>
            </el-form-item>
            <el-form-item>
                <q-map ref="mapRef" :coordinates="submitForm.location.coordinates" @change="changeMapHandle" />
            </el-form-item>
            <el-form-item :label="`${supplierViewModel}介绍`" prop="briefing">
                <el-input v-model="submitForm.briefing" :rows="4" type="textarea" maxlength="200" show-word-limit />
            </el-form-item>
            <!-- <el-form-item :label="`${supplierViewModel}门头照`" prop="shopTitelPhoto">
                <q-upload v-model:src="submitForm.shopTitelPhoto" :width="120" :height="120" :is-cropper="true" />
            </el-form-item>
            <el-form-item :label="`店内照`" prop="shopDetailsPhoto">
                <el-row style="width: 100%">
                    <VueDraggableNext
                        v-if="submitForm.shopDetailsPhoto?.length"
                        v-model="submitForm.shopDetailsPhoto"
                        style="display: flex; flex-wrap: wrap"
                    >
                        <div v-for="(item, index) in submitForm.shopDetailsPhoto" :key="index" style="position: relative; margin-right: 20px">
                            <el-image
                                :initial-index="index"
                                :preview-src-list="submitForm.shopDetailsPhoto"
                                :src="submitForm.shopDetailsPhoto[index]"
                                alt=""
                                class="selectMaterialStyle"
                                style="width: 120px; height: 120px"
                            />
                            <el-icon
                                v-if="item"
                                color="#7f7f7f"
                                size="20px"
                                style="position: absolute; right: -5px; top: -5px; background: #fff; border-radius: 50%; cursor: pointer"
                                @click="delImgHandle(index)"
                            >
                                <i-ep-circle-close />
                            </el-icon>
                        </div>
                    </VueDraggableNext>
                    <div
                        v-if="!submitForm.shopDetailsPhoto?.length || submitForm.shopDetailsPhoto?.length < 6"
                        class="selectMaterialStyle"
                        style="width: 120px; height: 120px"
                        @click="buttonlFn('shopDetailsPhoto')"
                    >
                        <el-icon class="avatar-uploader-icon">
                            <i-ep-plus />
                        </el-icon>
                    </div>
                </el-row>
                <div style="color: rgba(69, 64, 60, 0.6); font-size: 12px">
                    尺寸建议750x750（正方形模式）像素以上，大小1M以下，最多5张 (可拖拽图片调整顺序 )
                </div>
            </el-form-item> -->
            <template v-if="supplierViewModel === '店铺'">
                <el-form-item label="经营方式" props="mode">
                    <el-radio-group v-model="submitForm.businessType">
                        <el-radio value="SELF" size="large">自主经营</el-radio>
                        <el-radio value="TRUSTEESHIP" size="large">托管经营</el-radio>
                        <el-radio value="AGENT" size="large">代运营</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="经营模式" props="mode">
                    <el-radio-group v-model="submitForm.mode" :disabled="submitForm.status && submitForm.status !== 'UNDER_REVIEW'">
                        <el-radio value="B2B2C" size="large">线上模式</el-radio>
                        <el-radio value="O2O" size="large">O2O模式</el-radio>
                    </el-radio-group>
                </el-form-item>
            </template>
            <el-form-item :label="`${supplierViewModel}类型`" props="shopType">
                <el-radio-group v-model="submitForm.shopType">
                    <el-radio value="ORDINARY" size="large">普通{{ supplierViewModel }}</el-radio>
                    <el-radio value="PREFERRED" size="large">优选{{ supplierViewModel }}</el-radio>
                    <el-radio value="SELF_OWNED" size="large">自营{{ supplierViewModel }}</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="平台服务费" props="extractionType">
                <el-radio-group v-model="submitForm.extractionType">
                    <el-radio value="CATEGORY_EXTRACTION" size="large">类目提佣</el-radio>
                    <el-radio value="ORDER_SALES_EXTRACTION" size="large">
                        <span class="mr-8">订单金额提佣，按订单实付金额的</span>
                        <el-form-item prop="drawPercentage" style="display: inline-flex">
                            <el-input v-model="submitForm.drawPercentage" style="width: 120px" class="mr-8" />
                        </el-form-item>
                        <span>% ，进行提佣结算</span>
                    </el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="签约类目">
                <ContractCategory
                    :list="submitForm.signingCategory"
                    @update:list="
                        (val) => {
                            submitForm.signingCategory = val
                        }
                    "
                />
            </el-form-item>
        </el-form>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import qMap from '@/components/q-map/q-map.vue'
import { FormRules } from 'element-plus'
import { REGEX_MOBILE } from '@/libs/validate'
import { doGetUserList } from '@/apis/shops'
import ContractCategory from './ContractCategory/index.vue'
import type { Ref, PropType } from 'vue'
import { UserType, ShopFormType } from '../types'
import QUpload from '@/components/q-upload/q-upload.vue'
import RandomAvatar from '@/components/RandomAvatar/RandomAvatar.vue'
import { uploadFileApi } from '@/apis/upload'
import { dataURLtoFile } from '@/utils/util'

//父组件
const $parent = inject('addShops')
const { submitForm, dialogVisible, parameterId } = $parent as { submitForm: Ref<ShopFormType>; dialogVisible: Ref<boolean>; parameterId: Ref<string> }

const buttonlFn = (val: string) => {
    dialogVisible.value = true
    parameterId.value = val
}

/**
 * @LastEditors: lexy
 * @description: 删除店内照片
 * @param {number} index
 */
const delImgHandle = (index: number) => {
    submitForm.value.shopDetailsPhoto?.splice(index, 1)
}
// 选择素材 d

const $route = useRoute()
const mapRef = ref<InstanceType<typeof qMap>>()
const $emit = defineEmits(['mapChange'])
const currentFormRef = ref<FormInstance>()
const componentFlag = ref('base')
const $props = defineProps({
    supplierViewModel: {
        type: String as PropType<'供应商' | '店铺'>,
        default: '店铺',
    },
})
defineExpose({
    currentFormRef,
    componentFlag,
})

const baseFormRules = reactive<FormRules>({
    name: [
        {
            required: true,
            message: `请填写${$props.supplierViewModel}名称`,
            trigger: 'blur',
        },
    ],
    // companyName: [
    //     {
    //         required: true,
    //         message: `请填写商户名称`,
    //         trigger: 'blur',
    //     },
    // ],
    address: [
        {
            required: true,
            message: '请选择地址',
            trigger: 'blur',
        },
        { min: 2, max: 200, message: '输入长度必须在2~200以内', trigger: 'blur' },
    ],
    logo: [
        {
            required: true,
            message: `请上传${$props.supplierViewModel}logo`,
            trigger: 'blur',
        },
    ],
    contractNumber: [
        {
            required: true,
            validator: checkPhone,
            trigger: 'blur',
        },
    ],
    // briefing: [
    //     {
    //         required: true,
    //         message: `请填写${$props.supplierViewModel}介绍`,
    //         trigger: 'blur',
    //     },
    //     { min: 2, max: 200, message: '输入长度必须在2~200以内', trigger: 'blur' },
    // ],
    registerMobile: [
        {
            required: true,
            validator: checkUserAccount,
            trigger: 'blur',
        },
    ],
    drawPercentage: [
        {
            validator: (_, value) => {
                if (submitForm.value.extractionType === 'ORDER_SALES_EXTRACTION') {
                    if (!value) {
                        return new Error('请输入订单金额提佣百分比')
                    } else if (!/^(?:[1-9]?\d|100)$/.test(value)) {
                        return new Error('请填写0-100的数字')
                    }
                }
                return true
            },
            trigger: 'blur',
        },
    ],
})

// 校验用户账号
function checkUserAccount(rule: any, value: any, callback: any) {
    if (value === '') {
        callback(new Error('请输入用户账号注册电话'))
    } else if (!REGEX_MOBILE(value)) {
        callback(new Error('请填写正确的手机号'))
    } else {
        callback()
    }
}

// 校验手机号
function checkPhone(rule: any, value: any, callback: any) {
    if (value === '') {
        callback(new Error('请填写联系方式'))
    } else if (!REGEX_MOBILE(value)) {
        callback(new Error('请填写正确的手机号'))
    } else {
        callback()
    }
}
const cusAvatarLabel = ref<string>()
const cusAvatarUrl = ref<string>()
// 获取自动生成的头像
const handleApplyLogo = () => {
    if (cusAvatarUrl.value) {
        const formData = new FormData()
        const file = dataURLtoFile(cusAvatarUrl.value, cusAvatarLabel.value + '-logo')
        formData.append('file', file)
        uploadFileApi(import.meta.env.VITE_BASE_URL + 'gruul-mall-carrier-pigeon/oss/upload', formData).then((res) => {
            if (res.data.code === 200) {
                submitForm.value.logo = res.data.data
            }
        })
    }
}
// 删除自动生成的头像应用
const handleDelLogo = () => {
    submitForm.value.logo = ''
}

/**
 * 修改注册用的用户账号
 */
const changeRegister = (phone: string) => {
    submitForm.value.registerInfo.legalPersonPhone = phone
    submitForm.value.bankAccount.bankReservePhone = phone
}

const changeMapHandle = (e: { address: string; position: string[] }) => {
    // 编辑回显时 通过坐标回显详情地址有时存在偏差
    // 将change提升到父组件 有父组件决定是否进行change事件的赋值操作
    $emit('mapChange', e)
    // submitForm.value.address = e.address
    // submitForm.value.location.coordinates = e.position
}
// 用户选择下拉框的loading
const userLoading = ref(false)
// 用户选择下拉框数据
const userOptions = ref<UserType[]>([])
const loadUserInfo = (keyword: string) => {
    if (!keyword && !!submitForm.value.registerMobile) {
        return
    }
    userLoading.value = true
    const MAX_SEARCH_USER = 10000
    doGetUserList(1, MAX_SEARCH_USER, keyword).then(({ code, success, data }) => {
        userLoading.value = false
        if (code === 200 && success) {
            userOptions.value = data.records
        }
    })
}
</script>

<style lang="scss">
@include b(shopBaseForm) {
    padding: 20px 25px 70px 46px;
}

@include b(info) {
    @include flex(flex-start);

    @include e(img) {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        margin-right: 10px;
    }

    @include e(name) {
        font-size: 12px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }
}
</style>
<style scoped lang="scss">
.avatar {
    width: 178px;
    height: 178px;
    display: block;
}
.mr-8 {
    margin-right: 8px;
}
.group-cus-logo {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    justify-content: flex-start;
    align-content: flex-start;
    height: 60px;
    gap: 8px 16px;
    width: 360px;
    margin-left: 20px;
}
@include b(selectMaterialStyle) {
    width: 60px;
    height: 60px;
    border-radius: 5px;
    overflow: hidden;
    border: 1px dashed #ccc;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    @include e(span) {
        color: #999;
        font-size: 20px;
    }
}
</style>
