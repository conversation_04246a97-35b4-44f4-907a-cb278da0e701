<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-04-14 13:07:12
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-05 11:34:50
-->
<template>
    <div style="background: #f9f9f9">
        <el-form :model="searchForm">
            <m-card v-model="cardShow">
                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="店铺ID">
                            <el-input v-model="searchForm.no" placeholder="请填写店铺ID" maxlength="20" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="店铺名称">
                            <el-input v-model="searchForm.name" placeholder="请填写店铺名称" maxlength="20" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="状态">
                            <el-select v-model="searchForm.status" placeholder="请选择" clearable>
                                <el-option label="全部" value=" "></el-option>
                                <!-- <el-option label="审核" value="UNDER_REVIEW"></el-option> -->
                                <el-option label="正常" value="NORMAL"></el-option>
                                <!-- <el-option label="拒绝" value="REJECT"></el-option> -->
                                <el-option label="禁用" value="FORBIDDEN"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <!-- <el-col :span="8">
                        <el-form-item label="提佣类型">
                            <el-select v-model="searchForm.extractionType" placeholder="请选择">
                                <el-option label="全部" value="" />
                                <el-option label="类目提佣" value="CATEGORY_EXTRACTION" />
                                <el-option label="订单金额提佣" value="ORDER_SALES_EXTRACTION" />
                            </el-select>
                        </el-form-item>
                    </el-col> -->
                    <el-col :span="8">
                        <el-form-item label="店铺类型">
                            <el-select v-model="searchForm.shopType" placeholder="请选择" clearable>
                                <el-option label="全部" value="" />
                                <el-option label="自营" value="SELF_OWNED" />
                                <el-option label="优选" value="PREFERRED" />
                                <el-option label="普通" value="ORDINARY" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <!-- <el-col :span="8">
                        <el-form-item label="经营模式">
                            <el-select v-model="searchForm.shopModes" placeholder="请选择">
                                <el-option label="全部" value="COMMON,O2O" />
                                <el-option label="线上模式" value="COMMON" />
                                <el-option label="O2O模式" value="O2O" />
                            </el-select>
                        </el-form-item>
                    </el-col> -->
                    <el-col :span="8">
                        <el-form-item label="主体类型">
                            <el-select v-model="searchForm.subjectType" placeholder="请选择" clearable>
                                <el-option label="全部" value="" />
                                <el-option label="个人" value="PERSON" />
                                <el-option label="公司" value="COMPANY" />
                                <el-option label="个体工商户" value="INDIVIDUAL" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="实名认证">
                            <el-select v-model="searchForm.realNameStatus" placeholder="请选择" clearable>
                                <el-option label="全部" value="" />
                                <el-option label="未实名" value="0" />
                                <el-option label="已实名" value="1" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="开户认证">
                            <el-select v-model="searchForm.openAccStatus" placeholder="请选择" clearable>
                                <el-option label="全部" value="" />
                                <el-option label="未开户认证" value="0" />
                                <el-option label="已开户认证" value="1" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="合同签署">
                            <el-select v-model="searchForm.contractStatus" placeholder="请选择" clearable>
                                <el-option label="全部" value="" />
                                <el-option label="未签署" value="0" />
                                <el-option label="部分签署" value="2" />
                                <el-option label="已签署" value="1" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="OCR校验结果">
                            <el-select v-model="searchForm.ocrStatus" placeholder="请选择" clearable>
                                <el-option label="全部" value="" />
                                <el-option label="通过" value="0" />
                                <el-option label="不通过" value="1" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="店铺主体">
                            <el-input v-model="searchForm.legalPersonName" maxlength="20" placeholder="请填写店铺主体" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="申请时间">
                            <el-date-picker
                                clearable
                                v-model="searchForm.timeRange"
                                type="datetimerange"
                                :shortcuts="shortcuts"
                                start-placeholder="起始时间"
                                end-placeholder="结束时间"
                                range-separator="至"
                                :format="'YYYY-MM-DD HH:mm:ss'"
                                :value-format="'YYYY-MM-DD HH:mm:ss'"
                                :default-value="defaultTime"
                                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-button type="primary" round @click="searchHandle">搜索</el-button>
                        <el-button round @click="resetHandle">重置</el-button>
                    </el-col>
                </el-row>
            </m-card>
        </el-form>
    </div>
</template>

<script lang="ts" setup>
import { computed, reactive, ref, watch } from 'vue'
import DateUtil from '@/utils/date'
import MCard from '@/components/MCard.vue'

const $emit = defineEmits(['searchParams', 'showChange'])
const searchForm = reactive<{
    no: string
    name: string
    status: string
    shopModes: string
    shopType: string
    extractionType: string
    subjectType: string
    realNameStatus: string
    openAccStatus: string
    contractStatus: string
    ocrStatus?: string
    legalPersonName?: string
    timeRange?: [Date, Date]
}>({
    no: '',
    name: '',
    status: '',
    shopType: '',
    extractionType: '',
    shopModes: 'COMMON,O2O',
    subjectType: '',
    realNameStatus: '',
    openAccStatus: '',
    contractStatus: '',
    ocrStatus: '',
    legalPersonName: '',
})
const defaultTime = computed(() => {
    // 日期取当天，开始时间为00:00:00，结束时间为当前时刻
    const current = new Date()
    const start = new Date(current.getFullYear(), current.getMonth(), current.getDate(), 0, 0, 0)
    const end = new Date(current.getFullYear(), current.getMonth(), current.getDate(), current.getHours(), current.getMinutes(), current.getSeconds())

    return [start, end]
})

const shortcuts = reactive([
    {
        text: '近一周',
        value: () => {
            const end = new Date()
            const start = new Date()
            start.setDate(start.getDate() - 7)
            start.setHours(0, 0, 0, 0)
            end.setHours(23, 59, 59, 999)
            return [start, end]
        },
    },
    {
        text: '近一个月',
        value: () => {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 1)
            start.setHours(0, 0, 0, 0)
            end.setHours(23, 59, 59, 999)
            return [start, end]
        },
    },
    {
        text: '近三个月',
        value: () => {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 3)
            start.setHours(0, 0, 0, 0)
            end.setHours(23, 59, 59, 999)
            return [start, end]
        },
    },
])

const cardShow = ref(false)
const dateTool = new DateUtil()
const searchHandle = () => {
    let params: any = { ...searchForm }
    if (params.timeRange?.length) {
        params.beginTime = dateTool.getYMDHMSs(params.timeRange[0])
        params.endTime = dateTool.getYMDHMSs(params.timeRange[1])
        delete params.timeRange
    }
    $emit('searchParams', params)
}
const resetHandle = () => {
    Object.keys(searchForm).forEach((key) => {
        if (key === 'shopModes') {
            searchForm.shopModes = 'COMMON,O2O'
            searchForm.timeRange = undefined
        } else {
            // @ts-ignore
            searchForm[key] = ''
        }
    })
    searchHandle()
}
watch(
    () => cardShow.value,
    (val) => {
        $emit('showChange', val)
    },
)
</script>
<style scoped lang="scss">
.shop__search-visible {
    padding: 20px 20px 0 20px;
}
</style>
