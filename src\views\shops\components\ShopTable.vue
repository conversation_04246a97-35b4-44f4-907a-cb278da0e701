<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-04-16 09:47:51
 * @LastEditors: lexy
 * @LastEditTime: 2023-03-30 17:02:03
-->
<template>
    <q-table
        v-model:checked-item="multipleSelection"
        :data="tableData"
        :selection="true"
        class="q-table"
        :class="{ 'up-table': !parentShowChangeStatus }"
    >
        <template #header="{ row }">
            <div class="header">
                <div class="header__content">
                    <span class="mr-20">店铺ID：{{ row.no }}</span>
                    <span class="mr-20">店铺评分：{{ row.score }}</span>
                    <span class="mr-20">申请时间：{{ row.createTime }}</span>
                    <span class="mr-20">管理员：{{ row.userMobile }}</span>
                    <el-text class="mr-20" :type="AUTH_STATUS_COLOR[row.registerInfo.realNameStatus]" size="small" @click="toSignPage(row)">
                        {{ REAL_NAME_STATUS[row.registerInfo.realNameStatus] || '' }}
                    </el-text>
                    <el-text class="mr-20" :type="OPEN_ACC_STATUS_COLOR[row.registerInfo.openAccStatus]" size="small" @click="toSignPage(row)">
                        {{ OPEN_ACC_STATUS[row.registerInfo.openAccStatus] || '' }}
                    </el-text>
                    <el-text class="mr-20" size="small" :type="CONTRACT_TOTAL_STATUS_COLOR[row.registerInfo.contractStatus]" @click="toSignPage(row)">
                        {{ CONTRACT_STATUS[row.registerInfo.contractStatus] || '' }}
                    </el-text>
                    <el-text
                        v-if="row.registerInfo.ocrStatus !== undefined"
                        size="small"
                        :type="OCR_SCAN_STATU_COLOR[row.registerInfo.ocrStatus]"
                        style="cursor: default"
                    >
                        OCR{{ OCR_SCAN_STATUS[row.registerInfo.ocrStatus] || '' }}
                    </el-text>
                    <!-- <el-tooltip
                        v-if="row.registerInfo.ocrStatus !== undefined"
                        :disabled="!row.registerInfo.ocrErrorMsg"
                        effect="dark"
                        :content="row.registerInfo.ocrErrorMsg"
                        placement="top-start"
                    >
                        <el-text size="small" :type="OCR_SCAN_STATU_COLOR[row.registerInfo.ocrStatus]">
                            OCR{{ OCR_SCAN_STATUS[row.registerInfo.ocrStatus] || '' }}
                            <el-icon v-if="row.registerInfo.ocrErrorMsg"><Warning /></el-icon>
                        </el-text>
                    </el-tooltip> -->
                </div>
                <div style="display: flex; align-items: center">
                    <el-popover
                        :width="360"
                        popper-style="box-shadow: rgb(14 18 22 / 35%) 0px 10px 38px -10px, rgb(14 18 22 / 20%) 0px 10px 20px -15px; padding: 5px; height: 240px;"
                    >
                        <template #reference>
                            <el-text type="primary" size="small" class="mr-20" style="cursor: default">
                                <QIcon prefix-font-class="cylx" name="cylx-erweima1" :size="'12px'" style="margin-right: 4px" />店铺二维码
                            </el-text>
                        </template>
                        <template #default>
                            <div
                                style="
                                    display: inline-flex;
                                    align-items: center;
                                    justify-content: flex-start;
                                    flex-direction: column;
                                    flex-wrap: wrap;
                                    height: 100%;
                                    width: 320px;
                                    gap: 20px 0;
                                "
                            >
                                <vue-qr
                                    ref="qrCodeRef"
                                    :logo-src="row.logo"
                                    :text="formateQrCodeUrl(row.id)"
                                    :size="230"
                                    :logo-scale="0.22"
                                    :logo-margin="4"
                                    :logo-background-color="'#fff'"
                                    :background-color="'#fff'"
                                    :callback="(url:string) => saveQrCodeUrl(row.id, url)"
                                />
                                <el-text
                                    type="primary"
                                    class="mr-20"
                                    style="cursor: default; text-align: center; width: 120px; margin-top: 20px"
                                    @click="() => downloadImage(row.id, row.name)"
                                >
                                    下载二维码<el-icon><Download /></el-icon>
                                </el-text>
                                <el-text class="mr-20" style="cursor: default; text-align: center; width: 120px">
                                    <el-icon><Shop /></el-icon>小程序短链：<br />
                                    <el-text size="small" type="info" @click="() => copyShortLink(row.shortLink)">
                                        {{ row.shortLink }} <el-icon> <DocumentCopy /></el-icon
                                    ></el-text>
                                </el-text>
                            </div>
                        </template>
                    </el-popover>
                    <el-link class="mr-20" :underline="false" style="font-size: 12px" type="primary" @click="previewShop(row)">查看</el-link>
                </div>
            </div>
        </template>
        <q-table-column label="序号" :width="90">
            <template #default="{ row }">{{ row.index }}</template>
        </q-table-column>
        <q-table-column label="店铺信息" :width="320">
            <template #default="{ row }">
                <div class="shop">
                    <el-avatar
                        shape="square"
                        style="vertical-align: middle; margin: 0 10px 0 0; flex-shrink: 0"
                        :size="60"
                        :src="row.logo"
                        :icon="Shop"
                    />
                    <div class="shop__content">
                        <div class="shop__content--name">
                            <span v-if="row.shopType === 'SELF_OWNED'" class="tag__autarky">自营</span>
                            <span v-else-if="row.shopType === 'PREFERRED'" class="tag__optimize">优选 </span>
                            <QTooltip :content="`${row.name || ''}`" width="140px"></QTooltip>
                        </div>
                        <div class="shop__content--phone">{{ row.contractNumber }}</div>
                        <div class="shop__content--address">
                            <QTooltip :content="row.address" style="margin-left: 0" width="100%" />
                        </div>
                    </div>
                </div>
            </template>
        </q-table-column>
        <q-table-column label="主体名称" width="100">
            <template #default="{ row }">
                <el-tooltip placement="top" :content="row.registerInfo?.legalPersonName || ''">
                    <span class="legal-person-name">{{ row.registerInfo?.legalPersonName || '' }}</span>
                </el-tooltip>
            </template>
        </q-table-column>
        <q-table-column label="店铺余额" width="150">
            <template #default="{ row }">
                <el-row class="row_balance" @click="handleNavToWithdraw">
                    <div class="col_row_balance">待提现:{{ divTenThousand(row.shopBalance.undrawn) }}</div>
                    <!-- <div class="col_row_balance">待结算:{{ divTenThousand(row.shopBalance.uncompleted) }}</div> -->
                </el-row>
            </template>
        </q-table-column>
        <!-- <q-table-column label="地址" width="130">
            <template #default="{ row }">
                <q-tooltip :content="row.address"></q-tooltip>
            </template>
        </q-table-column>
        <q-table-column label="店铺名称" width="200">
            <template #default="{ row }">
                <div class="shop">
                    <el-avatar style="vertical-align: middle; margin: 0 10px 0 0" :size="40" :src="row.logo" :icon="Shop" />
                    <q-tooltip :content="row.name" width="100px"></q-tooltip>
                </div>
            </template>
        </q-table-column>
        <q-table-column prop="contractNumber" label="联系方式" width="130" />
        <q-table-column label="营业状态" width="130">营业中</q-table-column>
        <q-table-column label="经营模式" width="100">
            <template #default="{ row }">
                {{ row.mode === 'B2B2C' ? '线上模式' : 'O2O模式' }}
            </template>
        </q-table-column>
        <q-table-column label="提佣类型" width="100">
            <template #default="{ row }">
                {{ EXTRACTION_TYPE[row.extractionType] || '' }}
            </template>
        </q-table-column> -->
        <q-table-column label="主体类型" width="100">
            <template #default="{ row }"> {{ SUBJECT_TYPE[row.subjectType] || '' }} </template>
        </q-table-column>
        <q-table-column prop="status" label="状态" width="80">
            <template #default="{ row }">
                <el-switch
                    v-if="row.status === 'FORBIDDEN' || row.status === 'NORMAL'"
                    v-model="row.status"
                    active-value="NORMAL"
                    inactive-value="FORBIDDEN"
                    @change="changeStatus(row.id, row.status)"
                ></el-switch>
                <div v-show="row.status === 'REJECT'">
                    <span>已拒绝</span>
                </div>
            </template>
        </q-table-column>
        <q-table-column label="操作" width="200">
            <template #default="{ row }">
                <template v-if="row.status === 'FORBIDDEN' || row.status === 'NORMAL'">
                    <el-link :underline="false" class="mr-20" style="font-size: 12px" type="primary" @click="navToEdit(row, 'EDIT')"> 编辑 </el-link>
                    <el-link :underline="false" class="mr-20" style="font-size: 12px" type="danger" @click="deleteShop(row)">删除</el-link>
                    <el-link
                        v-if="row.registerInfo.ocrStatus === 1 && row.subjectType !== 'PERSON'"
                        :underline="false"
                        style="font-size: 12px"
                        type="warning"
                        @click="toOCRCheck(row)"
                    >
                        OCR校验
                    </el-link>
                </template>
                <template v-if="row.status === 'UNDER_REVIEW'">
                    <el-link
                        v-show="
                            row.registerInfo.contractStatus === 1 && row.registerInfo.realNameStatus === 1 && row.registerInfo.openAccStatus === 1
                        "
                        :underline="false"
                        style="font-size: 12px"
                        type="success"
                        class="mr-20"
                        @click="shopAudit(row.id, true)"
                    >
                        通过
                    </el-link>
                    <el-link :underline="false" class="mr-20" style="font-size: 12px" type="primary" @click="navToEdit(row, 'EDIT')"> 编辑 </el-link>
                    <el-link :underline="false" style="font-size: 12px" type="danger" @click="shopAudit(row.id, false)">拒绝</el-link>
                </template>
                <template v-if="row.status === 'REJECT'">
                    <el-link :underline="false" class="mr-20" style="font-size: 12px" type="primary" @click="reAudit(row.id)">重新审核</el-link>
                    <el-link :underline="false" class="mr-20" style="font-size: 12px" type="danger" @click="deleteShop(row, 'REJECT')">删除</el-link>
                    <el-link
                        v-if="row.refuseReason"
                        :underline="false"
                        style="font-size: 12px"
                        type="info"
                        @click="handleShowReason(row.refuseReason)"
                        >拒绝原因</el-link
                    >
                </template>
            </template>
        </q-table-column>
    </q-table>
    <el-dialog v-model="showReasonDialog" title="拒绝原因" center destroy-on-close>
        <span>原因：{{ refuseReason }}</span>
    </el-dialog>
</template>
<script lang="ts" setup>
import { Shop, Download, DocumentCopy } from '@element-plus/icons-vue'
import vueQr from 'vue-qr/src/packages/vue-qr.vue'
import { Ref } from 'vue'
import QTable from '@/components/qszr-core/packages/q-table/QTable'
import QTableColumn from '@/components/qszr-core/packages/q-table/q-table-column.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import storage from '@/utils/Storage'
import { doChangeStatus, doCheckOCR, doDelShop, doShopAudit, doShopReAudit } from '@/apis/shops'
import QTooltip from '@/components/q-tooltip/q-tooltip.vue'
import QIcon from '@/components/q-icon/q-icon.vue'
import useConvert from '@/composables/useConvert'
import useClipboard from 'vue-clipboard3'
import {
    AUTH_STATUS_COLOR,
    CONTRACT_STATUS,
    CONTRACT_TOTAL_STATUS_COLOR,
    EXTRACTION_TYPE,
    OCR_SCAN_STATU_COLOR,
    OCR_SCAN_STATUS,
    OPEN_ACC_STATUS,
    OPEN_ACC_STATUS_COLOR,
    REAL_NAME_STATUS,
    SUBJECT_TYPE,
} from '../types/index'

const parentTabChangeHandle = inject('parentTabChangeHandle') as (v: string) => void
const parentTabChoose = inject('parentTabChoose') as Ref
const parentShowChangeStatus = inject('parentShowChangeStatus') as Ref
const $router = useRouter()
const { divTenThousand } = useConvert()
const $props = defineProps({
    tableList: {
        type: Array,
        default() {
            return []
        },
    },
})
const { toClipboard } = useClipboard()
const tableData = ref<any[]>([])
watch(
    $props,
    (val) => {
        tableData.value = val.tableList.map((shop, index) => ({
            index: index + 1,
            ...shop,
        }))
    },
    { immediate: true, deep: true },
)
const multipleSelection = ref<any[]>([])
// 复制短链文本内容
const copyShortLink = async (shortLink: string) => {
    // 复制短链
    try {
        await toClipboard(shortLink) //实现复制
        alert('复制成功')
    } catch (e) {
        console.error(e)
    }
}

// 格式化店铺二维码url
const formateQrCodeUrl = (id: string) => `${import.meta.env.VITE_BASE_URL.replace('/api/', '/h5/')}#/pages/platform/Index?bindShopId=${id}`
/**
 * 下载店铺二维码
 * @param id 店铺id
 * @param name 店铺名称
 */
const downloadImage = (id: string, name: string) => {
    const aLink = document.createElement('a')
    aLink.download = name + '.png' //这里写保存时的图片名称
    aLink.href = qrCodeMap.value[id]
    aLink.click()
    aLink.remove()
}
const qrCodeMap = ref<Record<string, string>>({})
/**
 * 保存店铺二维码
 * @param id 店铺id
 * @param url 二维码url
 */
const saveQrCodeUrl = (id: string, url: string) => {
    qrCodeMap.value[id] = url
}
/**
 * 去合同签署页
 * @param row 行数据
 */
const toSignPage = (row) => {
    new storage().setItem('SHOPITEM', row, 60 * 60 * 2)
    $router.push({
        path: '/shopList/previewShop',
        query: { shopId: row.id, step: 'NewShopAuthSign' },
    })
}
/**
 * 校验OCR结果
 * @param row 行数据
 */
const toOCRCheck = (row) => {
    if (row.registerInfo.ocrStatus === 1) {
        doCheckOCR(row.id).then(({ code, msg }) => {
            if (code === 200) {
                ElMessage.success({ message: '会员信息补录校验成功！' })
            } else {
                ElMessage.error({ message: msg || 'OCR信息校验失败' })
            }
        })
    }
}
/**
 * 店铺审核
 */
/**
 * @LastEditors: lexy
 * @description:待审核列表的批量拒绝
 * @param {*} shopId
 * @param {*} isPass
 * @returns {*}
 */
const shopAudit = (shopId: string, isPass: boolean) => {
    ElMessageBox.prompt(`确认${isPass ? '通过' : '拒绝'}当前店铺审核?`, '请确认', {
        confirmButtonText: '提交',
        cancelButtonText: '取消',
        inputPlaceholder: `请输入${isPass ? '通过' : '拒绝'}理由`,
        inputType: 'textarea',
        inputValidator: (value: string) => value.length < 64,
        inputErrorMessage: '理由内容不可超过64个字符！',
    })
        .then(({ value }) => {
            if (value.length < 64) {
                doShopAudit(shopId, isPass, value).then((res) => {
                    if (res.code !== 200) {
                        ElMessage.error('更新')
                        return
                    }
                    ElMessage.success('已更新')
                    parentTabChangeHandle('UNDER_REVIEW')
                })
            }
        })
        .catch((e) => {})
}
/**
 * 店铺重新审核
 */
/**
 * @LastEditors: lexy
 * @description: 重新审核店铺
 * @param {*} shopId
 * @returns {*}
 */
const reAudit = (shopId: string) => {
    ElMessageBox.confirm(`确认重新审核当前店铺?`)
        .then(() => {
            doShopReAudit(shopId).then((res) => {
                if (res.code !== 200) {
                    ElMessage.error('更新')
                    return
                }
                ElMessage.success('已更新')
                parentTabChangeHandle('UNDER_REVIEW')
            })
        })
        .catch((e) => {})
}
/**
 * @LastEditors: lexy
 * @description: 单一状态切换
 * @param {string} shopsId
 * @param {string} status
 */
const changeStatus = (shopsId: string, status: string) => {
    const toBoolean = status === 'NORMAL'
    doChangeStatus([shopsId], toBoolean)
}
/**
 * @LastEditors: lexy
 * @description: 店铺列表单个删除
 * @param {string} shopsId
 */
const deleteShop = async (
    row: {
        shopBalance: {
            total: string
            uncompleted: string
            undrawn: string
        }
        id: string
    },
    type?: string,
) => {
    ElMessageBox.confirm('确定要删除该店铺吗，此操作不可逆?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(async () => {
            const uncompleted = divTenThousand(row.shopBalance.uncompleted).toNumber()
            const undrawn = divTenThousand(row.shopBalance.undrawn).toNumber()
            if (uncompleted || undrawn) {
                ElMessage.warning({
                    message: '该店铺存在未（提现/结算）金额，禁止删除',
                    duration: 4000,
                })
                return
            }
            const { code, msg } = await doDelShop([row.id])
            if (code === 200) {
                ElMessage({
                    type: 'success',
                    message: '删除成功',
                })
                type ? parentTabChangeHandle(type) : parentTabChangeHandle('')
                return
            }
            ElMessage({
                type: 'error',
                message: msg,
            })
        })
        .catch(() => {
            ElMessage({
                type: 'info',
                message: '删除失败',
            })
        })
}
/**
 * @LastEditors: lexy
 * @description: （店铺列表||已关闭）批量删除
 * @returns {*}
 */
const batchDeleteShop = () => {
    if (!multipleSelection.value.length) {
        return ElMessage.error('请勾选列表')
    }
    ElMessageBox.confirm('确定要删除该店铺吗，此操作不可逆?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(async () => {
            const tempArr = multipleSelection.value.map((item) => item.id)
            const { code, success } = await doDelShop(tempArr)
            if (code === 200 && success) {
                parentTabChoose.value !== 'REJECT' ? parentTabChangeHandle(' ') : parentTabChangeHandle('REJECT')
                ElMessage({
                    type: 'success',
                    message: '删除成功',
                })
            }
        })
        .catch(() => {
            ElMessage({
                type: 'info',
                message: '删除失败',
            })
        })
}
/**
 * @LastEditors: lexy
 * @description: 批量启用禁用方法
 * @param {boolean} status NORMAL启用 FORBIDDEN 禁用
 */

const batchChangeStatus = async (status: string) => {
    const toBoolean = status === 'NORMAL'
    if (!checkVaild(status)) {
        ElMessage.error('请勾选列表')
        return false
    }
    const tempArr = multipleSelection.value.map((item) => {
        return item.id
    })
    if (status === 'refusedTo') {
        // 批量拒绝
        return shopAudit(tempArr.join(','), false)
    }
    const { code, success } = await doChangeStatus(tempArr, toBoolean)
    if (code === 200 && success === true) {
        ElMessage.success('操作成功')
    }
    return true
}
/**
 * @LastEditors: lexy
 * @description: 跳转至对账单
 */
const handleNavToWithdraw = () => {}
/**
 * @LastEditors: lexy
 * @description: 判断选中row是否符合批量操作
 * @param {boolean} status NORMAL启用 FORBIDDEN 禁用
 * @returns {boolean}
 */
function checkVaild(status: string): boolean {
    let flag = true
    if (!multipleSelection.value.length) {
        flag = false
    }
    return flag
}
/**
 * @LastEditors: lexy
 * @description: 待审核列表的通过
 * @param {*} rows
 * @param {*} type
 * @returns {*}
 */
const navToEdit = (row: any, type: 'EDIT' | 'through') => {
    new storage().setItem('SHOPITEM', row, 60 * 60 * 2)
    $router.push({
        path: '/shopList/editShop',
        query: {
            shopId: row.id,
            type,
        },
    })
}

const previewShop = (rows: any) => {
    new storage().setItem('SHOPITEM', rows, 60 * 60 * 2)
    $router.push({
        path: '/shopList/previewShop',
        query: { shopId: rows.id },
    })
}

//
defineExpose({
    changeStatus,
    deleteShop,
    batchChangeStatus,
    batchDeleteShop,
})

/**
 * zrb:弹出查看原因
 * */
const showReasonDialog = ref(false)
const refuseReason = ref<string>('')
const handleShowReason = (reason: string) => {
    refuseReason.value = reason
    showReasonDialog.value = true
}
</script>
<style lang="scss">
.row_balance {
    display: flex;
    width: 100%;
    flex-direction: column;
    align-items: center;
    .col_row_balance + .col_row_balance {
        margin-top: 5px;
    }
}
@include b(header) {
    font-size: 12px;
    color: #838383;
    height: 46px;
    line-height: 46px;
    border-radius: 10px 10px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}
.el-text {
    cursor: pointer;
}
@include b(mr-20) {
    margin-right: 20px;
}
@include b(shop) {
    height: 68px;
    // display: flex;
    // align-items: center;
    // justify-content: flex-start;
    font-size: 12px;
    width: 100%;
    padding-left: 10px;
    display: flex;
    align-items: center;
    @include e(title) {
        width: 130px;
        margin-left: 10px;
        @include utils-ellipsis;
    }
    @include e(content) {
        height: 60px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        flex: 1;
        overflow: hidden;
        @include m(name) {
            display: flex;
            align-items: center;
            font-weight: bold;
            font-size: 1.3em;
            line-height: 20px;
        }
        @include m(phone) {
            font-size: 1.2em;
        }
    }
    @include b(tag) {
        @include e(autarky) {
            flex-shrink: 0;
            margin-right: 5px;
            padding: 0 5px;
            background-color: #fd0505;
            color: #fff;
            border-radius: 4px;
            font-size: 0.7em;
        }
        @include e(optimize) {
            flex-shrink: 0;
            margin-right: 5px;
            padding: 0 5px;
            background-color: #7728f5;
            color: #fff;
            border-radius: 4px;
            font-size: 0.8em;
        }
    }
}
@include b(q-table) {
    height: calc(100vh - 430px);
    overflow: auto;
    transition: height 0.5s;
}
@include b(up-table) {
    height: calc(100vh - 330px);
    overflow: auto;
}
.msg-bell {
    width: 20px;
}
.download {
    content: '';
    position: absolute;
    right: 5px;
    top: 5px;
}
:deep(.el-badge__content),
:deep(.is-fixed) {
    background-color: transparent;
    border: transparent;
    color: #f72020;
    font-weight: bold;
    font-size: 14px;
    padding-left: 5px;
    border: transparent;
}

.legal-person-name {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3; /* 限制显示的行数为3 */
    overflow: hidden;
}
</style>
