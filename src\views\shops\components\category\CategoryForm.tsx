import { ElForm, type FormRules, ElFormItem, ElInput, ElInputNumber, ElUpload, ElIcon, FormInstance, ElRow, ElCol, ElMessage } from 'element-plus'
import { type Ref } from 'vue'
import 'element-plus/dist/index.css'
import './category-form.scss'
import IEpPlus from '~icons/ep/plus'
import { SelectItemType } from './type'
import SelectMaterial from '@/views/material/selectMaterial.vue'
import { ElText } from 'element-plus'
import { CircleCloseFilled } from '@element-plus/icons-vue'

const CategoryForm = defineComponent({
    props: {
        submitFormModel: {
            type: Object,
            default: () => ({}),
        },
        radioCategory: {
            type: Number,
            default: 0,
        },
        isEdit: {
            type: Boolean,
            default: false,
        },
        sLevelInputArray: {
            type: Array,
            default: () => [],
        },
        selectLevel: {
            type: Object,
            default: () => ({ firstArr: [], secondArr: [] }),
        },
        currentUploadIdx: {
            type: Number,
            default: 0,
        },
        firstArrList: {
            type: Array<SelectItemType>,
            default: () => [],
        },
    },
    emits: ['update:submitFormModel', 'update:formRef', 'update:sLevelInputArray', 'update:currentUploadIdx'],
    setup(props, ctx) {
        const dialogVisible = ref(false)
        const parameterId = ref(null)
        const parameterIds = ref(null)
        const formRef: Ref<FormInstance | null> = ref(null)

        const submitForm = computed({
            get() {
                return props.submitFormModel
            },
            set(value) {
                ctx.emit('update:submitFormModel', value)
            },
        })
        const sLevelInputArr = computed({
            get() {
                return props?.sLevelInputArray
            },
            set(value) {
                ctx.emit('update:sLevelInputArray', value)
            },
        })
        const currentUploadIndex = computed({
            get() {
                return props?.currentUploadIdx
            },
            set(value) {
                ctx.emit('update:currentUploadIdx', value)
            },
        })
        onMounted(() => {
            ctx.emit('update:formRef', formRef.value)
        })
        return {
            submitForm,
            sLevelInputArr,
            currentUploadIndex,
            formRef,
            dialogVisible,
            parameterId,
            parameterIds,
        }
    },
    render() {
        const rules: FormRules = {
            fLevelInput: [{ required: true, message: '请输入类目', trigger: 'blur' }],
            sLevelInput: [
                { required: true, message: '请输入二级类目', trigger: ['blur', 'change'] },
                { min: 1, max: 100, message: '类目文字在1~100之间', trigger: 'blur' },
            ],
            sLevelSelect: [{ required: true, message: '请选择类目', trigger: 'change' }],
        }
        const handleDelSecond = (index: number) => {
            if (this.sLevelInputArr?.length === 1) return ElMessage.error('请至少保留一项')
            this.sLevelInputArr.splice(index, 1)
        }
        const handleDeleteThird = (index: number) => {
            if (this.submitForm.tLevelTable?.length === 1) return ElMessage.error('请至少保留一项')
            this.submitForm.tLevelTable.splice(index, 1)
        }
        const delCategoryImg = (e: MouseEvent) => {
            e.stopPropagation()
            if (this.radioCategory === 0) {
                this.submitForm.categoryImg = ''
            }
            if (this.radioCategory === 1 && this.sLevelInputArr[0]) {
                this.sLevelInputArr[0].categoryImg = ''
            }
            if (this.radioCategory === 2 && this.submitForm.tLevelTable[0]) {
                this.submitForm.tLevelTable[0].categoryImg = ''
            }
        }
        return (
            <>
                <ElForm ref="formRef" model={this.submitForm} rules={rules} labelWidth={this.radioCategory === 1 ? 94 : 0}>
                    {this.radioCategory >= 0 && (
                        <>
                            {this.radioCategory === 0 ? (
                                <ElFormItem label="一级类目" prop={'fLevelInput'}>
                                    <ElInput
                                        modelValue={this.submitForm.fLevelInput}
                                        onUpdate:modelValue={(value: string) => (this.submitForm.fLevelInput = value)}
                                        placeholder="请输入一级类目"
                                        showWordLimit={true}
                                        maxlength={4}
                                    />
                                </ElFormItem>
                            ) : (
                                <ElFormItem label="一级类目">
                                    <ElText type="info">
                                        {this.firstArrList.find((item: SelectItemType) => item.id === this.submitForm.fLevelSelect)?.name}
                                    </ElText>
                                </ElFormItem>
                            )}
                            {this.radioCategory === 0 ? (
                                <ElFormItem label="图片(84*84)">
                                    <div class="selectMaterialStyle" onClick={() => (this.dialogVisible = true)}>
                                        {!this.submitForm.categoryImg ? (
                                            <ElIcon class="avatar avatar-uploader-icon">
                                                <IEpPlus />
                                            </ElIcon>
                                        ) : (
                                            <>
                                                <img class="avatar" src={this.submitForm.categoryImg} alt="" />
                                                <div class="del-icon" onClick={delCategoryImg}>
                                                    <ElIcon size="20">
                                                        <CircleCloseFilled />
                                                    </ElIcon>
                                                </div>
                                            </>
                                        )}
                                    </div>
                                </ElFormItem>
                            ) : null}
                        </>
                    )}
                    {this.radioCategory === 1 && this.isEdit ? (
                        this.sLevelInputArr.map((item: any, index: number) => (
                            <>
                                <ElRow gutter={8}>
                                    <ElCol span={24}>
                                        <ElFormItem label="二级类目">
                                            <ElInput
                                                modelValue={item.sLevelInput}
                                                onUpdate:modelValue={(value: string) => (item.sLevelInput = value)}
                                                maxlength={4}
                                                showWordLimit={true}
                                                placeholder="类目名称"
                                                style="width: 100%"
                                            />
                                        </ElFormItem>
                                    </ElCol>
                                    <ElCol span={10}>
                                        <ElFormItem label="商家扣率(%)">
                                            <ElInputNumber
                                                modelValue={item.deductionRatio}
                                                onUpdate:modelValue={(value) => (this.sLevelInputArr[index].deductionRatio = value)}
                                                max={100}
                                                min={0}
                                                stepStrictly
                                                step={1}
                                                disabled={this.isEdit}
                                                placeholder="0-100"
                                                controls-position="right"
                                                style="width: 100px"
                                            />
                                        </ElFormItem>
                                    </ElCol>
                                    <ElCol span={10}>
                                        <ElFormItem label="供应商扣率(%)" labelWidth="160px">
                                            <ElInputNumber
                                                modelValue={item.supplierDeductionRatio}
                                                onUpdate:modelValue={(value) => (this.sLevelInputArr[index].supplierDeductionRatio = value)}
                                                max={100}
                                                min={0}
                                                stepStrictly
                                                step={1}
                                                disabled={this.isEdit}
                                                placeholder="0-100"
                                                controls-position="right"
                                                style="width: 100px"
                                            />
                                        </ElFormItem>
                                    </ElCol>
                                </ElRow>
                                <ElFormItem label="图片(84*84)">
                                    <div
                                        class="selectMaterialStyle"
                                        onClick={() => {
                                            this.dialogVisible = true
                                            this.parameterId = index
                                        }}
                                    >
                                        {!item.categoryImg ? (
                                            <ElIcon class="avatar avatar-uploader-icon">
                                                <IEpPlus />
                                            </ElIcon>
                                        ) : (
                                            <>
                                                <img class="avatar" src={item.categoryImg} alt="" />
                                                <div class="del-icon" onClick={delCategoryImg}>
                                                    <ElIcon size="20">
                                                        <CircleCloseFilled />
                                                    </ElIcon>
                                                </div>
                                            </>
                                        )}
                                    </div>
                                </ElFormItem>
                            </>
                        ))
                    ) : this.radioCategory === 1 ? (
                        <ElFormItem label="二级类目" prop={this.radioCategory === 1 ? '' : 'sLevelSelect'} required={this.radioCategory === 1}>
                            <div class="dialog">
                                <div class="dialog__header">
                                    <span class="dialog__header--category">类目名称</span>
                                    <span class="dialog__header--percent">商家扣率(%)</span>
                                    <span class="dialog__header--rate">供应商扣率(%)</span>
                                    <span class="dialog__header--img">图片(84*84)</span>
                                    <label>操作</label>
                                </div>
                                {this.sLevelInputArr.map((item: any, index: number) => (
                                    <div class="dialog__content">
                                        <ElInput
                                            modelValue={item.sLevelInput}
                                            onUpdate:modelValue={(value) => (item.sLevelInput = value)}
                                            maxlength={4}
                                            showWordLimit={true}
                                            placeholder="类目名称"
                                            style="width: 140px"
                                        />
                                        <ElInputNumber
                                            modelValue={item.deductionRatio}
                                            onUpdate:modelValue={(value) => (item.deductionRatio = value)}
                                            max={100}
                                            min={0}
                                            stepStrictly
                                            step={1}
                                            placeholder="0-100"
                                            controls-position="right"
                                            style="width: 100px;"
                                        />
                                        <ElInputNumber
                                            modelValue={item.supplierDeductionRatio}
                                            onUpdate:modelValue={(value) => (item.supplierDeductionRatio = value)}
                                            max={100}
                                            min={0}
                                            stepStrictly
                                            step={1}
                                            placeholder="0-100"
                                            controls-position="right"
                                            style="width: 120px;"
                                        />

                                        <div
                                            class="selectMaterialStyle"
                                            onClick={() => {
                                                this.dialogVisible = true
                                                this.parameterId = index
                                            }}
                                        >
                                            {!item.categoryImg ? (
                                                <ElIcon class="avatar avatar-uploader-icon">
                                                    <IEpPlus />
                                                </ElIcon>
                                            ) : (
                                                <>
                                                    <img class="avatar" src={item.categoryImg} alt="" />
                                                    <div class="del-icon" onClick={delCategoryImg}>
                                                        <ElIcon size="20">
                                                            <CircleCloseFilled />
                                                        </ElIcon>
                                                    </div>
                                                </>
                                            )}
                                        </div>

                                        {!this.isEdit && (
                                            <div class="dialog__content--tool">
                                                <div
                                                    class="add-btn"
                                                    onClick={() =>
                                                        this.sLevelInputArr.push({ sLevelInput: '', deductionRatio: 0, supplierDeductionRatio: 0 })
                                                    }
                                                >
                                                    新增
                                                </div>
                                                <div class="del-btn" onClick={() => handleDelSecond(index)}>
                                                    删除
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </ElFormItem>
                    ) : this.radioCategory === 2 ? (
                        <ElRow gutter={8}>
                            <ElCol span={10}>
                                <ElFormItem label="二级类目">
                                    <ElText type="info">
                                        {this.selectLevel.secondArr?.find((item: any) => item.id === this.submitForm.sLevelSelect)?.name}
                                    </ElText>
                                </ElFormItem>
                            </ElCol>
                            <ElCol span={10}>
                                <ElFormItem label="商家扣率(%)">
                                    <ElInput modelValue={this.submitForm.deductionRatio} disabled />
                                </ElFormItem>
                            </ElCol>
                            <ElCol span={10}>
                                <ElFormItem label="供应商扣率(%)">
                                    <ElInput modelValue={this.submitForm.supplierDeductionRatio} disabled />
                                </ElFormItem>
                            </ElCol>
                        </ElRow>
                    ) : null}
                    {this.radioCategory === 2 && (
                        <ElFormItem label="三级类目" prop="tLevelTable">
                            <div class="dialog" style={this.isEdit ? 'width:300px' : ''}>
                                <div class="dialog__header">
                                    <span class="dialog__header--name">下级类目名称</span>
                                    <span class="dialog__header--img">图片(132*132)</span>
                                    {!this.isEdit ? <label>操作</label> : null}
                                </div>
                                {this.submitForm.tLevelTable?.map((item: any, index: number) => (
                                    <div class="dialog__content">
                                        <ElInput
                                            modelValue={item.name}
                                            onUpdate:modelValue={(value) => (item.name = value.trim())}
                                            maxlength={4}
                                            showWordLimit={true}
                                            placeholder="类目名称"
                                            style="width: 140px"
                                        />

                                        <div
                                            class="selectMaterialStyle"
                                            onClick={() => {
                                                this.dialogVisible = true
                                                this.parameterIds = index
                                            }}
                                        >
                                            {!item.categoryImg ? (
                                                <ElIcon class="avatar avatar-uploader-icon">
                                                    <IEpPlus />
                                                </ElIcon>
                                            ) : (
                                                <>
                                                    <img class="avatar" src={item.categoryImg} alt="" />
                                                    <div class="del-icon" onClick={delCategoryImg}>
                                                        <ElIcon size="20">
                                                            <CircleCloseFilled />
                                                        </ElIcon>
                                                    </div>
                                                </>
                                            )}
                                        </div>

                                        {!this.isEdit && (
                                            <div class="dialog__content--tool">
                                                <div class="add-btn" onClick={() => this.submitForm.tLevelTable.push({ name: '', categoryImg: '' })}>
                                                    新增
                                                </div>
                                                <div class="del-btn" onClick={() => handleDeleteThird(index)}>
                                                    删除
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </ElFormItem>
                    )}
                </ElForm>
                <SelectMaterial
                    uploadFiles={1}
                    dialogVisible={this.dialogVisible}
                    onSelectMaterialFn={(val: boolean) => {
                        this.dialogVisible = val
                        this.parameterId = null
                        this.parameterIds = null
                    }}
                    onCroppedFileChange={(val: string) => {
                        if (this.parameterId !== null) this.sLevelInputArr[this.parameterId].categoryImg = val[0]
                        if (this.parameterIds !== null) this.submitForm.tLevelTable[this.parameterIds].categoryImg = val[0]
                        if (this.parameterId === null && this.parameterIds === null) {
                            this.submitForm.categoryImg = val[0]
                        }
                    }}
                    onCheckedFileLists={(val: string) => {
                        if (this.parameterId !== null) this.sLevelInputArr[this.parameterId].categoryImg = val[0]
                        if (this.parameterIds !== null) this.submitForm.tLevelTable[this.parameterIds].categoryImg = val[0]
                        if (this.parameterId === null && this.parameterIds === null) {
                            this.submitForm.categoryImg = val[0]
                        }
                    }}
                />
            </>
        )
    },
})

export default CategoryForm
