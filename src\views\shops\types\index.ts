/*
 * @description: 商铺类型
 * @Author: lexy
 * @Date: 2022-05-25 09:56:38
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-05 10:50:55
 */
export type UserType = Record<'userId' | 'username' | 'mobile' | 'email' | 'nickname' | 'avatar' | 'gender', string>
export enum ShopMode {
    COMMON = 'COMMON',
    SUPPLIER = 'SUPPLIER',
    O2O = 'O2O',
}
export enum OPERATOR_STATUS {
    NORMAL = 'NORMAL',
    NONE = 'NONE',
}
// 商铺模式处理程序
export const SHOP_MODE_HANDLER: Record<ShopMode, any> = {
    COMMON: {},
    O2O: {},
    SUPPLIER: {},
}
export interface ShopFormType {
    [key: string]: any
    // companyName: string
    address: string
    bankAcc: string
    bankAccount: BankAccountType
    bankName: string
    briefing: string
    contractNumber: string
    legalPersonIdBack: string
    legalPersonIdFront: string
    license: string
    location: LocationType
    logo: string
    name: string
    openAccountBank: string
    subjectType: 'COMPANY' | 'PERSON' | 'INDIVIDUAL'
    payee: string
    registerInfo: RegisterInfo
    registerMobile: string
    shopType: string
    signingCategory: any[]
    mode: string
    shopMode: keyof typeof ShopMode
    // shopTitelPhoto?: string
    // shopDetailsPhoto?: string[]
    otherQualifications?: string[]
    businessType: 'TRUSTEESHIP' | 'SELF' | 'AGENT'
}
export type BankAccountType = {
    [key: string]: any
    payee: string
    bankName: string
    openAccountBank: string
    bankAccount: string
    // 账户类型  (1--对公,0--对私 ,公司类型店铺 不能选择0)
    acctAttr: '0' | '1'
    // 银行预留手机号(账户类型==0时 必填)
    bankReservePhone: string
    // 银行代码(账户类型==1时 必填)
    openBankNo: string
    // 支付行号，12位数字(账户类型==1时 必填)
    payBankNumber: string
    // 开户行所在省(入参中文)
    openBankProvince?: string
    // 开户行所在市(入参中文)
    openBankCity?: string
}
export type LocationType = {
    type: string
    coordinates: string[]
}
export type RegisterInfo = {
    [key: string]: any
    license: string
    legalPersonIdFront: string
    legalPersonIdBack: string
    // handheldPhoto: string
    startDate: string
    endDate: string
    legalPersonName: string
    legalPersonNo: string
    groupName: string
    creditCode: string
    validType: 'ALWAYS' | 'FIXED'
    validityPeriod: [string, string]
    realNameStatus: 0 | 1
    openAccStatus: 0 | 1 | 2
    enterpriseAdress?: string
    legalPersonPhone: string
    addressCode: string
    operatorStatus: keyof typeof OPERATOR_STATUS
}

export type SelectItemType = Record<'id' | 'name', string>
export type SelectLevelType = Record<'firstArr' | 'secondArr', SelectItemType[]>
export type CateType = {
    id: string
    name: string
    parentId: string
    sort: number
    categoryId: string
    categoryImg?: string | null
}
export interface CateListType extends CateType {
    categoryThirdlyVos?: CateType[]
    secondCategoryVos?: CateType[]
}
export enum CateLevelEnum {
    LEVEL_1,
    LEVEL_2,
    LEVEL_3,
}
export enum REAL_NAME_STATUS {
    已实名 = 1,
    未实名 = 0,
}

export enum AUTH_STATUS_COLOR {
    danger = 0,
    success = 1,
}

export enum OPEN_ACC_STATUS {
    未开户 = 0,
    已开户 = 1,
}

export enum OPEN_ACC_STATUS_COLOR {
    danger = 0,
    success = 1,
}

export enum SUBJECT_TYPE {
    PERSON = '个人',
    COMPANY = '公司',
    INDIVIDUAL = '个体工商户',
}

export enum CONTRACT_STATUS {
    未签署 = 0,
    全部签署 = 1,
    部分签署 = 2,
}

export enum CONTRACT_TOTAL_STATUS_COLOR {
    danger = 0,
    success = 1,
    warning = 2,
}

export enum CONTRACT_DETAIL_STATUS {
    未签署 = 1,
    已签署 = 2,
    过期 = 3,
    签署失败 = 4,
    签约中 = 6,
}

export enum CONTRACT_STATUS_COLOR {
    info = 3,
    primary = 6,
    danger = 4 | 1,
    success = 2,
}

export enum OCR_SCAN_STATUS {
    不通过 = 1,
    通过 = 0,
}

export enum OCR_SCAN_STATU_COLOR {
    danger = 1,
    success = 0,
}

export enum EXTRACTION_TYPE {
    CATEGORY_EXTRACTION = '类目抽佣',
    ORDER_SALES_EXTRACTION = '订单金额提佣',
}

export type CONTRACT_INFO_TYPE = {
    contractName: string
    createDate: string
    signDate: string
    templateNo: string
    signUrl: string
    contractStatus: CONTRACT_DETAIL_STATUS
    ifDownload: boolean
}

export type AUTH_STATUS_VALUE_TYPE = 0 | 1

export type OPEN_ACC_STATUS_VALUE_TYPE = 0 | 1 | 2

export type AUTH_SIGN_INFO_TYPE = {
    realNameStatus: AUTH_STATUS_VALUE_TYPE
    openAccStatus: OPEN_ACC_STATUS_VALUE_TYPE
    legalPersonPhone: string
    contractNumber: string
    bankReservePhone: string
    shopId: string
    userId?: string
    serialNo?: string
    contractInfoVOS: CONTRACT_INFO_TYPE[]
    authType: 'USER' | 'COMPANY'
}

// *---------------
// test
export interface Tree {
    categoryId: string
    id: string
    name: string
    parentId: string
    secondCategoryVos?: TreeSecondary[]
    sort: number
    categoryImg?: string
    deductionRatio?: number | null
    supplierDeductionRatio: number | null
    children?: Tree[]
}
export interface TreeSecondary {
    categoryId: string
    categoryThirdlyVos?: TreeThree[]
    id: string
    name: string
    parentId: string
    sort: number
}
export interface TreeThree {
    categoryImg: string
    id: string
    name: string
    parentId: string
    productNumber: number
    sort: number
}
