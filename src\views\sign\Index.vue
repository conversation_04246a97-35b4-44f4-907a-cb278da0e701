<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-03-21 15:46:43
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-25 15:13:17
-->
<template>
    <div class="container">
        <div class="container__form">
            <img src="@/assets/image/sign/login_pic.png" class="container__form--cover" />
            <div class="container__form--main">
                <div class="logo">
                    <img src="@/assets/image/sign/title_icon_horizontal.png" />
                    <span>平台端</span>
                </div>
                <SignInByPhone />
            </div>
        </div>
        <footer class="container__footer">
            <p class="medusa__rights">Copyright © 2024. All Rights Reserved 杭州宠有灵犀有限公司版权所有</p>
            <p>
                <img style="width: 14px" src="https://devoss.chongyoulingxi.com/system-front/mobile/gongan.jpg" />
                <span class="pointer">浙ICP备2024108466号-2X</span>
            </p>
        </footer>
    </div>
</template>
<script lang="ts" setup>
import SignInByPhone from './components/SignInByPhone.vue'
</script>
<style lang="scss" scoped>
@include b(container) {
    display: flex;
    height: 100%;
    flex-direction: column;
    justify-content: space-between;
    @include e(form) {
        flex: 1;
        padding: 0 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        @include m(cover) {
            width: 35%;
        }
        @include m(main) {
            box-shadow: 0px 0px 16px rgba(7, 21, 70, 0.15);
            border-radius: 20px;
            margin-left: 100px;
            padding: 36px 80px 60px;
            background-color: #fff;
            @include b(logo) {
                display: flex;
                justify-content: center;
                align-items: center;
                img {
                    width: 180px;
                }
                span {
                    margin-left: 100px;
                    color: #7f3386;
                    font-size: 24px;
                    flex-shrink: 0;
                }
            }
        }
    }
    &__footer {
        font-size: 12px;
        line-height: 21px;
        text-align: center;
        color: #999;
        padding-bottom: 20px;
    }
}
</style>
