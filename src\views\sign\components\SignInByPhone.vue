<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-03-21 15:54:07
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-25 17:22:00
-->
<template>
    <div v-loading="loading" class="SignFlow">
        <el-form
            ref="signFormRef"
            :model="signForm"
            class="SignInForm"
            hide-required-asterisk
            label-position="left"
            label-width="85px"
            size="large"
            @submit="submitForm"
        >
            <div class="SignInForm__tabs">
                <div :class="{ 'SignInForm__tab--active': signHandler.password }" class="SignInForm__tab" @click="changeSignType(LoginType.PASSWORD)">
                    密码登录
                </div>
                <div :class="{ 'SignInForm__tab--active': signHandler.mobile }" class="SignInForm__tab" @click="changeSignType(LoginType.SMS_CODE)">
                    验证码登录
                </div>
            </div>

            <el-form-item prop="account" style="margin-left: 0" :rules="accountRules">
                <el-input v-model="signForm.account" :placeholder="`请输入手机号${signHandler.password ? ' 或 账号' : ''}`" :maxlength="11">
                    <template #prepend>
                        <QIcon class="icon-wode-wode" size="20px" color="#999" />
                    </template>
                </el-input>
            </el-form-item>

            <el-form-item v-if="signForm.loginType === LoginType.PASSWORD" prop="password" style="margin-left: 0" :rules="passwordRules">
                <el-input v-model="signForm.password" :type="passIsShow ? 'text' : 'password'" placeholder="请输入登录密码" :maxlength="20">
                    <template #prepend>
                        <QIcon class="icon-mima" size="20px" color="#999" />
                    </template>
                    <template #append>
                        <QIcon
                            :class="passIsShow ? 'icon-yanjing_xianshi' : 'icon-yanjing_yincang'"
                            size="20px"
                            color="#999"
                            @click="passIsShow = !passIsShow"
                        />
                    </template>
                </el-input>
            </el-form-item>
            <el-form-item v-else-if="signForm.loginType === LoginType.SMS_CODE" prop="password" style="margin-left: 0" :rules="smsCodeRules">
                <el-input v-if="signHandler.mobile" v-model="signForm.password" placeholder="输入短信验证码" maxlength="4">
                    <template #prepend>
                        <QIcon class="icon-mima" size="20px" color="#999" />
                    </template>
                    <template #suffix>
                        <el-button v-if="signForm.time === intervalTime" link class="sms-btn" @click="sendSmsCode"> 发送验证码 </el-button>
                        <el-button v-else disabled link> {{ signForm.time }}秒</el-button>
                    </template>
                </el-input>
            </el-form-item>

            <el-button
                class="SignInForm__button"
                :color="canLogin ? '#7f3386' : '#B0B0B0'"
                :disabled="!canLogin"
                native-type="submit"
                @click="submitForm"
            >
                登录
            </el-button>
            <div class="SignInForm__option">
                <el-link :underline="false" @click="handleResetPassword"> 忘记密码 ?</el-link>
            </div>
            <slider-captcha
                v-model="signForm.showSliderCaptcha"
                :do-submit="doPostSmsCode"
                :get-form="() => signForm.account"
                :scale="1"
                @success="slideCaptchaSuccess"
            />
        </el-form>
    </div>
    <!-- 忘记密码 -->
    <change-password v-model:is-show="toggleBindingDialog" :load-user-data="false" />
</template>

<script lang="ts" setup>
import { useAdminInfo } from '@/store/modules/admin'
import { ElMessage } from 'element-plus'
import { reactive, ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { signByUser, doPostSmsCode, doGetUserMenu } from '@/apis/sign'
import { REGEX } from '@/constant'
import ChangePassword from '@/views/business/components/changePassword.vue'
import type { FormInstance, FormRules } from 'element-plus'
import QIcon from '@/components/q-icon/q-icon.vue'
import SliderCaptcha from '@/components/slide-captcha/SliderCaptcha.vue'
import encrypt from '@/utils/encrypted'
import { useMenuList } from '@/store/modules/menu'

enum LoginType {
    PASSWORD = 'password',
    SMS_CODE = 'sms_code',
}

interface SignHandle {
    password: boolean
    mobile: boolean
    formData: () => any
}
const passIsShow = ref(false)
const canLogin = computed(() => signForm.account && signForm.password)

/**
 * reactive variable
 */
const toggleBindingDialog = ref(false)
const intervalTime = 60
const loading = ref(false)
const signFormRef = ref()
const router = useRouter()
const route = useRoute()

const loginTypeHandler: Record<LoginType, SignHandle> = {
    password: {
        password: true,
        mobile: false,
        formData: () => {
            return { grant_type: LoginType.PASSWORD, username: signForm.account, password: signForm.password }
        },
    },
    sms_code: {
        password: false,
        mobile: true,
        formData: () => {
            return { grant_type: LoginType.SMS_CODE, mobile: signForm.account, code: signForm.password }
        },
    },
}
const signHandler = computed<SignHandle>(() => loginTypeHandler[signForm.loginType])

const signForm = reactive({
    readonly: true,
    showSliderCaptcha: false,
    loginType: LoginType.PASSWORD,
    shop: null,
    visible: false,
    shops: [],
    account: null,
    password: null as null | string,
    disabled: false,
    time: intervalTime,
    interval: null as any,
})

watch(
    () => signForm.loginType,
    () => (signForm.password = null),
)

const accountRules = computed(() => {
    let nullMessage = '请输入手机号'
    let validateFunc = (account: string, callback: (arg?: Error) => void) => {
        if (!REGEX.MOBILE.test(account)) {
            callback(new Error('请输入正确的手机号'))
        }
        callback()
    }
    if (signForm.loginType === LoginType.PASSWORD) {
        nullMessage += ' 或 账号'
        validateFunc = (account, callback) => {
            if (!account) {
                callback(new Error('请输入手机号 或 账号'))
            }
            callback()
        }
    }
    return [
        { required: true, trigger: 'blur', message: nullMessage },
        {
            validator: (rule: any, value: string, callback: (arg?: Error) => void) => validateFunc(value, callback),
            trigger: 'blur',
        },
    ]
})

const passwordRules = reactive([
    { required: true, trigger: 'blur', message: `请输入密码` },
    {
        validator: (rule: any, value: string, callback: (arg?: Error) => void) => {
            if (!value) {
                callback(new Error(`请输入请输入密码`))
            }
            callback()
        },
        trigger: 'blur',
    },
])

const smsCodeRules = reactive([
    { required: true, trigger: 'blur', message: `请输入验证码` },
    {
        validator: (rule: any, value: string, callback: (arg?: Error) => void) => {
            if (!/\d{4}$/.test(value)) {
                callback(new Error('请输入正确的验证码'))
            }
            callback()
        },
        trigger: 'blur',
    },
])

/**
 * function
 */
/**
 * 切换登录方式
 * @param type 登录类型
 */
const changeSignType = (type: LoginType) => (signForm.loginType = type)
/**
 * @LastEditors: lexy
 * @description: 忘记密码
 * @returns {*}
 */
const handleResetPassword = () => {
    toggleBindingDialog.value = true
}
const sendSmsCode = () => {
    const formRef = signFormRef.value
    if (!formRef) return
    formRef.validateField('account', (valid: boolean) => {
        if (!valid) return
        formRef.validateField('shop', (shopValid: boolean) => {
            if (!shopValid) return
            signForm.showSliderCaptcha = true
        })
    })
}
const slideCaptchaSuccess = (response: { code: number; data: string }) => {
    signForm.password = response.data
    ElMessage.success('验证码已发送')
    signForm.time -= 1
    signForm.showSliderCaptcha = false
    signForm.interval = setInterval(() => {
        signForm.time -= 1
        if (signForm.time <= 0) {
            signForm.time = intervalTime
            if (signForm.interval) {
                clearInterval(signForm.interval)
                signForm.interval = null
            }
            return
        }
    }, 999)
}
function submitForm(e: Event) {
    e.preventDefault()
    e.stopPropagation()
    const formRef = signFormRef.value
    if (!formRef) return
    formRef.validate((valid: boolean) => {
        if (!valid) return
        let formData = signHandler.value.formData()
        if (signHandler.value.password && signForm.password) {
            formData.password = encrypt(formData.password)
        }
        signByUser(formData).then(async ({ code, data }: any) => {
            if (code !== 200) {
                ElMessage.error('登录失败')
                return
            }
            useAdminInfo().SET_ADMIN_INFO({ ...data })

            const redirect = route.query.redirect
            router.replace(redirect ? redirect.toString() : '/')
        })
    })
}

/**
 * lifeCircle
 */
</script>

<style lang="scss" scoped>
// 覆盖样式
:deep(.el-input__inner) {
    border-color: transparent;
}

:deep(.el-form-item) {
    border-bottom: 1px solid #f8f5f9;
}

:deep(.el-checkbox__inner) {
    width: 12px;
    height: 12px;
}

:deep(.el-checkbox__inner::after) {
    height: 6px;
    left: 3px;
    top: 0;
}

:deep(.el-checkbox__label) {
    color: #909399;
    padding-left: 6px;
    font-size: 12px;
}
.SignInForm__option {
    margin-top: 50px;
}
:deep(.SignInForm__option .el-button--text) {
    color: #909399;
}
.SignFlow {
    margin-top: 50px;
}
.SignInForm__tabs {
    margin-bottom: 30px;
    display: flex;
    justify-content: center;
    .SignInForm__tab {
        margin: 0 10px;
        width: 120px;
        height: 40px;
        box-sizing: border-box;
        line-height: 40px;
        display: inline-block;
        list-style: none;
        font-size: 24px;
        font-weight: 500;
        color: #999;
        position: relative;
        cursor: pointer;
    }
    .SignInForm__tab--active {
        color: #7f3386;
    }
}
:deep(.el-form-item__content) {
    margin-left: 0px !important;
}
:deep(.el-input-group__prepend) {
    box-shadow: none;
    background-color: #fff;
    padding: 0;
}
:deep(.el-input-group__append) {
    box-shadow: none;
    background-color: #fff;
    padding: 0;
}
:deep(.el-input__wrapper) {
    box-shadow: none;
}
:deep(.el-form-item.is-error .el-input__wrapper) {
    box-shadow: none;
}
:deep(.el-form-item.is-error) {
    border-bottom: 1px solid var(--el-color-danger);
}
.SignInForm__button {
    width: 100%;
    color: #fff;
    height: 48px;
    line-height: 48px;
}
.sms-btn {
    color: #5790ff;
}
.el-form-item + .el-form-item {
    margin-top: 30px;
}
</style>
