<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-10-12 09:27:40
 * @LastEditors: lexy
 * @LastEditTime: 2023-05-07 15:27:53
-->
<script setup lang="ts">
import PageManage from '@/components/PageManage.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import QTable from '@/components/qszr-core/packages/q-table/QTable'
import qTableColumn from '@/components/qszr-core/packages/q-table/q-table-column.vue'
import { doGetBlackList, doPutLimitPermission } from '@/apis/vip'
import useBlackReasonHooks from './hooks/useBlackReasonHooks'
enum ROLE {
    FORBIDDEN_COMMENT,
    FORBIDDEN_ORDER,
}
/*
 *variable
 */
const { blockReasonDialogFormModel, showReasonDialog } = useBlackReasonHooks()
const { divTenThousand } = useConvert()
const searchParams = reactive<{ userNickname: string; roles: keyof typeof ROLE | string | null }>({
    userNickname: '',
    roles: null,
})
const pageConfig = reactive({
    current: 1,
    size: 10,
    total: 0,
})
const editDialog = ref(false)
const tableSelectedArr = ref([])
const tableList = ref([])
const checkedArr = ref<string[]>([])
const currentUserId = ref('')
/*
 *lifeCircle
 */
initBlackList()
/*
 *function
 */
const handleChangeSize = (e: number) => {
    pageConfig.size = e
    pageConfig.current = 1
    initBlackList()
}
const handleChangeCurrent = (e: number) => {
    pageConfig.current = e
    initBlackList()
}
const handleSearch = () => {
    initBlackList()
}
const handleRemove = (userId: string) => {
    ElMessageBox.confirm('确定需要移除所有权限吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(async () => {
        const { code, msg } = await doPutLimitPermission([userId], ['USER'])
        if (code === 200) {
            ElMessage.success('移除成功')
            initBlackList()
        } else {
            ElMessage.error(msg ? msg : '操作失败')
        }
    })
}
const handleEdit = (userId: string, userAuth: string[]) => {
    editDialog.value = true
    checkedArr.value = userAuth
    currentUserId.value = userId
}
const handleConfirm = async () => {
    if (!checkedArr.value.length) {
        ElMessage.warning('至少选择一项')
        return
    }
    const { code, msg } = await doPutLimitPermission([currentUserId.value], checkedArr.value)
    if (code === 200) {
        ElMessage.success('操作成功')
        editDialog.value = false
        checkedArr.value = []
        initBlackList()
    } else {
        ElMessage.error(msg ? msg : '操作失败')
    }
}
const handleBatchRemove = () => {
    if (!tableSelectedArr.value.length) {
        ElMessage.warning('请勾选后操作')
        return
    }
    ElMessageBox.confirm('确定需要移除所有权限吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(async () => {
        const userIds = tableSelectedArr.value.map((item: { userId: string }) => item.userId)
        const { code, msg } = await doPutLimitPermission(userIds, ['USER'])
        if (code === 200) {
            ElMessage.success('操作成功')
            initBlackList()
        } else {
            ElMessage.error(msg ? msg : '操作失败')
        }
    })
}
const handleChangeSelect = (e: string) => {
    searchParams.roles = e
    initBlackList()
}
async function initBlackList() {
    const { code, data, msg } = await doGetBlackList({ ...pageConfig, ...searchParams })
    if (code === 200) {
        tableList.value = data.records
        pageConfig.total = data.total
    } else {
        ElMessage.error(msg ? msg : '获取黑名单失败')
    }
}
</script>

<template>
    <div class="handle_container">
        <el-row :gutter="24" justify="space-between" style="margin-bottom: 15px">
            <el-col :span="10">
                <el-button round plain @click="handleBatchRemove">批量移除</el-button>
            </el-col>
            <el-col :span="12" :push="1">
                <el-select v-model="searchParams.roles" @change="handleChangeSelect">
                    <el-option label="全部权限" :value="null" />
                    <el-option label="禁止评论" value="FORBIDDEN_COMMENT" />
                    <el-option label="禁止下单" value="FORBIDDEN_ORDER" />
                </el-select>
                <el-input
                    v-model="searchParams.userNickname"
                    placeholder="请输入会员名称"
                    style="width: 200px; margin-left: 20px"
                    @keypress.enter="handleSearch"
                >
                    <template #append>
                        <el-button :icon="Search" @click="handleSearch" />
                    </template>
                </el-input>
            </el-col>
        </el-row>
    </div>
    <q-table v-model:checked-item="tableSelectedArr" :data="tableList" class="base-vip-table" :selection="true">
        <template #header="{ row }">
            <div class="base-vip-table-top">
                <div class="base-vip-table-top--time">注册时间:{{ row.createTime }}</div>
            </div>
        </template>
        <q-table-column label="客户信息" width="220">
            <template #default="{ row }">
                <div class="customer-Infor">
                    <el-image
                        class="customer-Infor__img"
                        fit="cover"
                        style="width: 40px; height: 40px; margin-right: 10px; flex-shrink: 0"
                        :src="row.userHeadPortrait"
                    />
                    <div class="customer-nick-Infor">
                        <div>
                            <div class="ellipsis" style="width: 120px">{{ row.userNickname }}</div>
                            <div v-show="row.userPhone">{{ row.userPhone }}</div>
                        </div>
                    </div>
                </div>
            </template>
        </q-table-column>
        <q-table-column label="会员类型">
            <template #default="{ row }">
                <span>{{ row.member?.memberType === 'PAID_MEMBER' ? '付费会员' : '免费会员' }}</span>
            </template>
        </q-table-column>
        <q-table-column label="会员等级">
            <template #default="{ row }">
                <div>LV{{ row.member?.rankCode }}</div>
            </template>
        </q-table-column>
        <q-table-column prop="sex" label="成长值" class="rate_size">
            <template #default="{ row }">
                <div class="avatar_text money_text">{{ row.growthValue }}</div>
            </template>
        </q-table-column>
        <q-table-column prop="sex" label="交易总额">
            <template #default="{ row }">
                <el-row justify="space-between" align="middle">
                    <div>{{ divTenThousand(row.dealTotalMoney) }}</div>
                </el-row>
            </template>
        </q-table-column>
        <q-table-column prop="sex" label="储值余额">
            <template #default="{ row }">
                <el-row justify="space-between" align="middle">
                    <div>{{ divTenThousand(row.balance) }}</div>
                </el-row>
            </template>
        </q-table-column>
        <q-table-column prop="sex" label="限制权限">
            <template #default="{ row }">
                <div>
                    <div v-for="item in row.userAuthority" :key="item">
                        {{ item === 'FORBIDDEN_COMMENT' ? '禁止评论' : '禁止下单' }}
                    </div>
                </div>
            </template>
        </q-table-column>
        <q-table-column prop="sex" label="操作" width="260">
            <template #default="{ row }">
                <el-link :underline="false" type="primary" @click="showReasonDialog(row?.explain)">原因</el-link>
                <el-link :underline="false" type="primary" style="margin: 0 8px" @click="handleEdit(row.userId, row.userAuthority)">编辑</el-link>
                <el-link :underline="false" type="danger" @click="handleRemove(row.userId)">恢复</el-link>
            </template>
        </q-table-column>
    </q-table>
    <page-manage
        :page-size="pageConfig.size"
        :page-num="pageConfig.current"
        :total="pageConfig.total"
        @handle-size-change="handleChangeSize"
        @handle-current-change="handleChangeCurrent"
    />
    <el-dialog v-model="editDialog" title="编辑权限">
        <el-form>
            <el-form-item label="权限">
                <el-checkbox-group v-model="checkedArr">
                    <el-checkbox label="FORBIDDEN_COMMENT">禁止评论</el-checkbox>
                    <el-checkbox label="FORBIDDEN_ORDER">禁止下单</el-checkbox>
                </el-checkbox-group>
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button @click="editDialog = false">取消</el-button>
            <el-button @click="handleConfirm">确定</el-button>
        </template>
    </el-dialog>
    <el-dialog v-model="blockReasonDialogFormModel.showDialog" title="原因">
        <span>拉黑原因：{{ blockReasonDialogFormModel.reason }}</span>
    </el-dialog>
</template>

<style scoped lang="scss">
@include b(base-vip-table) {
    // height: calc(100vh - 250px);
    height: calc(100vh - 210px);
    transition: height 0.5s;
    overflow-y: auto;
}
@include b(base-vip-table-top) {
    @include flex(flex-start);
    width: 100%;
    @include m(no) {
    }
    @include m(time) {
        padding: 0 20px;
    }
}
@include b(customer-Infor) {
    width: 300px;
    height: 80px;
    @include flex(flex-start);
    @include e(img) {
        width: 40px;
        height: 40px;
        border-radius: 50%;
    }
}
@include b(customer-nick-Infor) {
    height: 80px;
    @include flex(space-around, flex-start);
    flex-direction: column;
    @include m(label) {
        cursor: pointer;
        &::after {
            content: '';
            display: inline-block;
            margin: 0 0 2px 2px;
            width: 0;
            height: 0;
            vertical-align: middle;
            border-top: 5px solid #000;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
        }
    }
}
</style>
