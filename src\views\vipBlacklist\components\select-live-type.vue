<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-11-15 11:48:50
 * @LastEditors: lexy
 * @LastEditTime: 2022-11-23 09:50:02
-->
<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-11-02 14:58:59
 * @LastEditors: lexy
 * @LastEditTime: 2022-11-04 18:47:19
-->
<script setup lang="ts">
import { useVModel } from '@vueuse/core'
import { disablePermissions } from '@/views/vipBlacklist'
/*
 *variable
 */
const props = defineProps({
    modelValue: {
        type: String,
        default: '',
    },
    placeholder: {
        type: String,
        default: '',
    },
    list: {
        type: Object,
        default: () => ({}),
    },
})
const emit = defineEmits(['update:modelValue', 'change'])
const modelValue = useVModel(props, 'modelValue', emit)

/*
 *variable
 */
/*
 *lifeCircle
 */
/*
 *function
 */
</script>

<template>
    <el-select v-model="modelValue" :placeholder="props.placeholder" style="width: 150px" @change="emit('change', $event)">
        <slot></slot>
        <el-option v-for="(item, key) in Object.values(props.list).length ? props.list : disablePermissions" :key="key" :label="item" :value="key" />
    </el-select>
</template>

<style scoped lang="scss"></style>
