const useBlackReasonHooks = () => {
    const blockReasonDialogFormModel = reactive({
        showDialog: false,
        reason: '',
    })
    const showReasonDialog = (reason = '') => {
        blockReasonDialogFormModel.reason = reason
        blockReasonDialogFormModel.showDialog = true
    }
    return {
        blockReasonDialogFormModel,
        showReasonDialog,
    }
}

export default useBlackReasonHooks
