<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-10-12 09:28:09
 * @LastEditors: lexy
 * @LastEditTime: 2023-06-21 13:05:19
-->
<script setup lang="ts">
import uuid from '@/utils/uuid'
import { ElMessage } from 'element-plus'
import { doGetSavingManage, doGPutSavingManage, doPostSavingManage } from '@/apis/saving'
import type { FormInstance, FormRules } from 'element-plus'
import type { BalancePayInfoItem } from '@/views/vipMoney/types'
import { SavingManageItem } from '@/views/vipMoney/types'
/*
 *variable
 */
const { mulTenThousand, divTenThousand } = useConvert()
const submitFormData = ref({
    id: '',
    ruleJson: [{ id: uuid(10), ladderMoney: '', presentedMoney: '', presentedGrowthValue: '' }] as SavingManageItem[],
    discountsState: true,
    switching: true,
    balancePayInfoData: [],
})

const loading = ref(false)
const ruleFormRef = ref<FormInstance>()
const rules = reactive<FormRules>({
    discountsState: [{ required: true, message: '请选择优惠方式', trigger: 'change' }],
    list: [{ required: true, message: '请填写完整的储值规则', trigger: 'blur' }],
})

const BalancePayInfoType = {
    ORDER_PAY: '商品订单',
    AI_CHAT: 'AI问答',
}

/*
 *lifeCircle
 */
initSavingManage()
/*
 *function
 */
async function initSavingManage() {
    const { code, data } = await doGetSavingManage()
    if (code !== 200 || !data) return ElMessage.error('获取储值管理信息失败')
    if (!data.ruleJson?.length) {
        data.ruleJson = [{ id: uuid(10), ladderMoney: '', presentedMoney: '', presentedGrowthValue: '' }]
    } else {
        data.ruleJson = data.ruleJson.map((item: SavingManageItem) => {
            return {
                id: item.id,
                ladderMoney: divTenThousand(item.ladderMoney).toString(),
                presentedMoney: divTenThousand(item.presentedMoney).toString(),
                presentedGrowthValue: item.presentedGrowthValue,
            }
        })
    }
    submitFormData.value = data
    if (data?.balancePayInfo) {
        if (!submitFormData.value.balancePayInfoData) {
            submitFormData.value.balancePayInfoData = []
        }
        ;(data?.balancePayInfo as BalancePayInfoItem[]).forEach(({ type, switchType }: { type: string; switchType: boolean }) => {
            if (switchType) {
                submitFormData.value.balancePayInfoData.push(type)
            }
        })
        console.log(submitFormData.value)
    }
}
const handleSubmit = async () => {
    if (!ruleFormRef.value) return
    try {
        await ruleFormRef.value.validate()
        if (!listValidate()) return ElMessage.error('请填写正确的储值规则')
        const { ruleJson: ruleJson, discountsState, id, balancePayInfoData } = submitFormData.value
        const { code, data } = await doPostSavingManage(
            id,
            discountsState,
            ruleJson.map((item) => ({
                ladderMoney: mulTenThousand(item.ladderMoney).toString(),
                presentedMoney: mulTenThousand(item.presentedMoney).toString(),
                presentedGrowthValue: item.presentedGrowthValue,
            })),
            Object.keys(BalancePayInfoType)?.flatMap((type) => ({ type, switchType: balancePayInfoData.includes(type) })),
        )
        if (code === 200) {
            ElMessage.success('修改储值管理信息成功')
            initSavingManage()
            return
        }
        ElMessage.error('修改储值管理信息失败')
    } catch (error) {
        console.log('error', error)
        return ElMessage.error('请填写正确的储值规则')
    }
}
function listValidate() {
    const isTopUp = submitFormData.value.ruleJson.every((item) => item.ladderMoney)
    if (!submitFormData.value.discountsState) return isTopUp
    const isSendMoney = submitFormData.value.ruleJson.every((item) => item.presentedMoney)
    const isSendIntegral = submitFormData.value.ruleJson.every((item) => item.presentedGrowthValue)
    return isTopUp && isSendMoney && isSendIntegral ? true : false
}

const handleChangeSwitch = async (e: any) => {
    loading.value = true
    const { code, data } = await doGPutSavingManage(e)
    code === 200 ? ElMessage.success('修改储值管理信息成功') : ElMessage.error('修改储值管理信息失败')
    loading.value = false
}
</script>

<template>
    <div class="vip-money">
        <!-- <div class="vip-money__title">
            <span> 储值功能帮助平台快速回笼资金，充实资金链。</span>
            <el-switch v-model="submitFormData.switching" :loading="loading" @change="(e) => handleChangeSwitch(e)" />
        </div> -->
        <el-form ref="ruleFormRef" :model="submitFormData" label-width="120px" :rules="rules">
            <el-form-item label="储值开关" prop="switching">
                <el-switch v-model="submitFormData.switching" :loading="loading" @change="(e) => handleChangeSwitch(e)" />
                <span style="color: #666; margin-left: 15px">(储值功能帮助平台快速回笼资金，充实资金链)</span>
            </el-form-item>
            <el-form-item v-if="submitFormData.switching" label="储值开启类型" prop="balancePayInfoData">
                <el-checkbox-group v-model="submitFormData.balancePayInfoData">
                    <el-checkbox
                        v-for="key in Object.keys(BalancePayInfoType)"
                        :key="key"
                        :label="BalancePayInfoType[key as keyof typeof BalancePayInfoType]"
                        :value="key"
                    ></el-checkbox>
                </el-checkbox-group>
            </el-form-item>
            <template v-if="submitFormData.switching">
                <el-form-item label="是否优惠" prop="preferential">
                    <el-radio-group v-model="submitFormData.discountsState">
                        <el-radio :label="true">赠送（储值+成长值）</el-radio>
                        <el-radio :label="false">无赠送</el-radio>
                    </el-radio-group>
                </el-form-item>
                <!-- <el-form-item>
                    <el-button
                        type="primary"
                        :disabled="submitFormData.ruleJson.length === 10"
                        @click="submitFormData.ruleJson.push({ id: uuid(10), ladderMoney: '1', presentedMoney: '0', presentedGrowthValue: '0' })"
                        >添加
                    </el-button>
                </el-form-item> -->
                <el-form-item label="储值规则" prop="list[0].sendIntegral" :show-message="false">
                    <el-button
                        style="margin-bottom: 20px"
                        type="primary"
                        :disabled="submitFormData.ruleJson.length === 10"
                        @click="submitFormData.ruleJson.push({ id: uuid(10), ladderMoney: '1', presentedMoney: '0', presentedGrowthValue: '0' })"
                        >添加
                    </el-button>
                    <!-- 赠送（储值+成长值） -->
                    <template v-if="submitFormData.discountsState">
                        <div v-for="item in submitFormData.ruleJson" :key="item.id" class="stored-value">
                            <span class="mr15">充</span>
                            <el-input
                                v-model.trim="item.ladderMoney"
                                :disabled="!submitFormData.discountsState"
                                onkeyup="value=this.value.replace(/\D+/g,'')"
                                class="width-20"
                            >
                            </el-input>
                            <span class="mr15 ml15">元，</span>
                            <span class="mr15">送</span>
                            <el-input
                                v-model.trim="item.presentedMoney"
                                :disabled="!submitFormData.discountsState"
                                onkeyup="value=this.value.replace(/\D+/g,'')"
                                class="width-20"
                            >
                                <template #append>元</template></el-input
                            >
                            <span class="mr15 ml15">送</span>
                            <el-input
                                v-model.trim="item.presentedGrowthValue"
                                :disabled="!submitFormData.discountsState"
                                onkeyup="value=this.value.replace(/\D+/g,'')"
                                class="width-25"
                            >
                                <template #append>成长值</template></el-input
                            >
                            <el-link
                                type="danger"
                                class="ml15"
                                :underline="false"
                                :disabled="submitFormData.ruleJson.length === 1"
                                @click="submitFormData.ruleJson = submitFormData.ruleJson.filter((i) => i.id !== item.id)"
                                >删除
                            </el-link>
                        </div>
                    </template>
                    <!-- 赠送（储值+成长值） -->
                    <!-- 无赠送 -->
                    <template v-else>
                        <div v-for="item in submitFormData.ruleJson" :key="item.id" class="stored-value" style="width: 100%">
                            <span class="mr15">充</span>
                            <el-input v-model.trim="item.ladderMoney" onkeyup="value=this.value.replace(/\D+/g,'')" style="width: 200px"> </el-input>
                            <span class="mr15 ml15">元</span>
                            <el-link
                                type="danger"
                                class="ml15"
                                :underline="false"
                                :disabled="submitFormData.ruleJson.length === 1"
                                @click="submitFormData.ruleJson = submitFormData.ruleJson.filter((i) => i.id !== item.id)"
                                >删除
                            </el-link>
                        </div>
                    </template>
                    <!-- 无赠送 -->
                </el-form-item>
            </template>
            <el-form-item v-if="submitFormData.switching">
                <el-button type="primary" round style="margin-top: 50px; padding: 0 30px" @click="handleSubmit">保存</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<style scoped lang="scss">
@include b(vip-money) {
    padding: 10px 15px;
    @include e(title) {
        @include flex;
        justify-content: space-between;
        padding: 15px;
        background: #f4fcff;
        margin-bottom: 30px;
    }
}
@include b(stored-value) {
    display: flex;
    margin-bottom: 15px;
    &:last-child {
        margin-bottom: 0px;
    }
}
.width-20 {
    width: 20%;
}
.width-25 {
    width: 25%;
}
.mr15 {
    margin-right: 15px;
}
.ml15 {
    margin-left: 15px;
}
</style>
