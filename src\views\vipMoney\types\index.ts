/*
 * @description:
 * @Author: lexy
 * @Date: 2022-10-12 13:46:37
 * @LastEditors: lexy
 * @LastEditTime: 2022-10-12 14:18:07
 */
/**
 * @LastEditors: lexy
 * @description: 储值管理
 * @param discountsState 优惠状态 0无优惠 1有优惠
 * @param  ruleJson 储值管理li
 * @param switching 储值管理开关 0关闭 1开启
 */
export interface SavingManageInfo {
    discountsState: boolean
    id: string
    ruleJson: SavingManageItem[]
    switching: boolean
    balancePayInfo: BalancePayInfoItem[]
}
/**
 * @LastEditors: lexy
 * @description: 储值管理
 * @param ladderMoney 充值金额
 * @param  presentedMoney 赠送金额
 * @param presentedGrowthValue 赠送成长值
 */
export interface SavingManageItem {
    id?: string
    ladderMoney: string
    presentedGrowthValue: string
    presentedMoney: string
}
/**
 * 交易类型
 * @param  type 类型
 * @param  switchType 是否开启
 */
export interface BalancePayInfoItem {
    type: 'ORDER_PAY' | 'AI_CHAT'
    switchType: boolean
}
