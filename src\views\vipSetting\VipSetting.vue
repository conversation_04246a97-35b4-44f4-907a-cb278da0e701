<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-11-01 16:21:52
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-10 13:47:50
-->
<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-10-12 09:27:04
 * @LastEditors: lexy
 * @LastEditTime: 2022-11-28 09:35:18
-->
<script setup lang="ts">
import { ref, defineAsyncComponent, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import PaidMember from '@/q-plugin/member/PaidMember.vue'
type TabListType = 'FreeMember' | 'PayingMember' | 'RightsMember' | 'GrowthValueSetting'
/*
 *variable ordinary
 */
const membersTabList = [
    {
        label: '免费会员',
        name: 'FreeMember',
    },
    {
        label: '会员权益',
        name: 'RightsMember',
    },
    {
        label: '成长值设置',
        name: 'GrowthValueSetting',
    },
]
const activeName = ref<TabListType>('FreeMember')
const defineAsyncComponentReactive = {
    FreeMember: defineAsyncComponent(() => import('@/views/vipSetting/components/FreeMember.vue')),
    RightsMember: defineAsyncComponent(() => import('@/views/vipSetting/components/RightsMember.vue')),
    GrowthValueSetting: defineAsyncComponent(() => import('./components/GrowthValueSetting.vue')),
}
/*
 *lifeCircle
 */
onMounted(() => {
    activeName.value = useRoute().query.name ? (useRoute().query.name as TabListType) : 'FreeMember'
})
</script>

<template>
    <div class="tab_container">
        <el-tabs v-model="activeName" class="demo-tabs">
            <el-tab-pane v-for="item in membersTabList.slice(0, 1)" :key="item.name" :label="item.label" :name="item.name"></el-tab-pane>
            <paid-member />
            <el-tab-pane v-for="item in membersTabList.slice(1)" :key="item.name" :label="item.label" :name="item.name"></el-tab-pane>
        </el-tabs>
    </div>
    <template v-if="defineAsyncComponentReactive[activeName]">
        <component :is="defineAsyncComponentReactive[activeName]" />
    </template>
</template>

<style scoped lang="scss"></style>
