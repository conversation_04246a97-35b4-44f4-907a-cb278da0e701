<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-04-18 17:01:23
 * @LastEditors: lexy
 * @LastEditTime: 2022-11-04 14:16:03
-->
<template>
    <div class="shopInfo">
        <el-form ref="currentFormRef" :model="submitForm" :rules="infoFormRules" :disabled="$route.meta.disabled">
            <el-form-item label="主体类型" props="subjectType" required>
                <el-radio-group v-model="submitForm.subjectType" @change="changeSubjectType">
                    <el-radio v-if="$route.path.includes('Shop')" :value="'PERSON'" size="default">个人</el-radio>
                    <el-radio :value="'COMPANY'" size="default">公司</el-radio>
                    <el-radio :value="'INDIVIDUAL'" size="default">个体工商户</el-radio>
                </el-radio-group>
            </el-form-item>
            <template v-if="submitForm.subjectType !== 'PERSON'">
                <el-form-item label="营业执照" prop="registerInfo.license" style="position: relative">
                    <q-upload
                        v-model:src="submitForm.registerInfo.license"
                        :width="120"
                        :height="120"
                        :disabled="$route.name === 'previewShop'"
                        @update:src="changeLicense"
                    />
                    <div class="shopInfo__exp ml20">
                        <el-image :preview-src-list="[DEFAULT_SYSTEM_IMG.LICENSE]" class="shopInfo__exp--img" :src="DEFAULT_SYSTEM_IMG.LICENSE" />
                        <!-- <div class="shopInfo__exp--tip">示例</div> -->
                    </div>
                </el-form-item>
                <el-form-item label="企业名称" prop="registerInfo.groupName">
                    <el-input v-model="submitForm.registerInfo.groupName" placeholder="请填写企业名称" maxlength="50" />
                </el-form-item>
                <el-form-item label="注册所在城市" prop="registerInfo.addressCodes">
                    <el-cascader
                        v-model="submitForm.registerInfo.addressCodes"
                        clearable
                        filterable
                        :check-strictly="true"
                        :props="addressCascaderProps"
                        style="width: 100%"
                        @change="changeAdressCodes"
                    >
                        <template #default="{ data }">
                            <span v-if="!data.children">{{ data.name }}[{{ data.code }}]</span>
                            <span v-else>{{ data.name }}</span>
                        </template>
                    </el-cascader>
                </el-form-item>
                <el-form-item label="企业注册地址" prop="registerInfo.enterpriseAdress">
                    <el-input v-model="submitForm.registerInfo.enterpriseAdress" placeholder="请填写企业注册地址" maxlength="50"></el-input>
                </el-form-item>
                <el-form-item label="统一社会信用代码" prop="registerInfo.creditCode">
                    <el-input v-model="submitForm.registerInfo.creditCode" placeholder="请填写统一社会信用代码" maxlength="18"></el-input>
                </el-form-item>
            </template>
            <el-form-item :label="`${legalPersonIdLabel}身份`" prop="registerInfo.legalPersonIdFront">
                <el-space :size="8">
                    <q-upload
                        v-model:src="submitForm.registerInfo.legalPersonIdFront"
                        :width="120"
                        :height="120"
                        :disabled="$route.name === 'previewShop'"
                        @update:src="(url) => changeIDCard(url, 'front')"
                    />
                    <el-form-item prop="registerInfo.legalPersonIdBack">
                        <q-upload
                            v-model:src="submitForm.registerInfo.legalPersonIdBack"
                            :width="120"
                            :height="120"
                            :disabled="$route.name === 'previewShop'"
                            @update:src="(url) => changeIDCard(url, 'back')"
                        />
                    </el-form-item>
                    <span class="button-preview" @click="handlePreview('registerInfo')">查看大图</span>
                    <!-- <el-form-item prop="handheldPhoto">
                        <q-upload
                            v-model:src="submitForm.registerInfo.handheldPhoto"
                            :width="120"
                            :height="120"
                            :disabled="$route.name === 'previewShop'"
                        />
                    </el-form-item> -->
                </el-space>
            </el-form-item>
            <el-form-item :label="`${legalPersonIdLabel}示例`">
                <div class="shopInfo__exp">
                    <el-image
                        :preview-src-list="[DEFAULT_SYSTEM_IMG.IDCARD_FRONT]"
                        :src="DEFAULT_SYSTEM_IMG.IDCARD_FRONT"
                        class="shopInfo__exp--img"
                    />
                    <!-- <div class="shopInfo__exp--tip">示例</div> -->
                </div>
                <div class="shopInfo__exp ml20">
                    <el-image :preview-src-list="[DEFAULT_SYSTEM_IMG.IDCARD_BACK]" :src="DEFAULT_SYSTEM_IMG.IDCARD_BACK" class="shopInfo__exp--img" />
                    <!-- <div class="shopInfo__exp--tip">示例</div> -->
                </div>
                <!-- <div class="shopInfo__exp ml20">
                    <el-image
                        :preview-src-list="[DEFAULT_SYSTEM_IMG.IDCARD_HOLDON]"
                        :src="DEFAULT_SYSTEM_IMG.IDCARD_HOLDON"
                        class="shopInfo__exp--img"
                    />
                </div> -->
            </el-form-item>
            <el-form-item>
                <div class="shopInfo__tips">请上传身份证正反面照片，仅限jpg、png，大小不超过2M，仅限上传2张</div>
            </el-form-item>
            <el-form-item :label="`${legalPersonIdLabel}姓名`" prop="registerInfo.legalPersonName">
                <el-input v-model="submitForm.registerInfo.legalPersonName" :placeholder="`请填写${legalPersonIdLabel}姓名`" maxlength="50" />
            </el-form-item>
            <el-form-item :label="`${legalPersonIdLabel}证件号`" prop="registerInfo.legalPersonNo">
                <el-input v-model="submitForm.registerInfo.legalPersonNo" :placeholder="`请填写${legalPersonIdLabel}证件号`" maxlength="18" />
            </el-form-item>
            <el-form-item label="有效期" prop="registerInfo.validType">
                <el-select v-model="submitForm.registerInfo.validType" reserve-keyword :default-first-option="true" placeholder="请选择有效期类型">
                    <el-option label="固定期限" value="FIXED"> </el-option>
                    <el-option label="永久有效" value="ALWAYS"> </el-option>
                </el-select>
            </el-form-item>
            <el-form-item v-if="submitForm.registerInfo.validType === 'FIXED'" label="有效期至" prop="registerInfo.validityPeriod" required>
                <el-date-picker
                    v-model="submitForm.registerInfo.validityPeriod"
                    type="daterange"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    format="YYYY/MM/DD"
                    value-format="YYYY-MM-DD"
                    :default-value="currentValidityPeriod"
                />
            </el-form-item>
            <el-form-item v-if="submitForm.subjectType !== 'PERSON'" label="法人手机号" prop="registerInfo.legalPersonPhone">
                <el-input v-model="submitForm.registerInfo.legalPersonPhone" type="tel" placeholder="请填写法人手机号" maxlength="11"></el-input>
            </el-form-item>
            <el-form-item label="授权经营者" prop="registerInfo.operatorStatus">
                <el-switch
                    v-model="submitForm.registerInfo.operatorStatus"
                    :active-value="OPERATOR_STATUS.NORMAL"
                    :inactive-value="OPERATOR_STATUS.NONE"
                />
            </el-form-item>
            <template v-if="submitForm.registerInfo.operatorStatus === OPERATOR_STATUS.NORMAL">
                <el-form-item label="姓名" prop="registerInfo.operatorUserName">
                    <el-input v-model="submitForm.registerInfo.operatorUserName" placeholder="请输入经营者身份证" maxlength="18"></el-input>
                </el-form-item>
                <el-form-item label="身份证" prop="registerInfo.operatorPersonNo">
                    <el-input v-model="submitForm.registerInfo.operatorPersonNo" placeholder="请输入经营者身份证" maxlength="18"></el-input>
                </el-form-item>
                <el-form-item label="手机号" prop="registerInfo.operatorPhone">
                    <el-input v-model="submitForm.registerInfo.operatorPhone" type="tel" placeholder="请输入经营者手机号" maxlength="11"></el-input>
                </el-form-item>
            </template>

            <el-form-item :label="`其他资质证明`" prop="otherQualifications">
                <el-row style="width: 100%">
                    <VueDraggableNext
                        v-if="submitForm.otherQualifications?.length"
                        v-model="submitForm.otherQualifications"
                        style="display: flex; flex-wrap: wrap; gap: 16px"
                        :disabled="$route.name === 'previewShop'"
                        :handle="'.draggable-item'"
                    >
                        <div v-for="(item, index) in submitForm.otherQualifications" :key="index" class="draggable-item" style="position: relative">
                            <el-image
                                :initial-index="index"
                                :preview-src-list="submitForm.otherQualifications"
                                :src="submitForm.otherQualifications[index]"
                                alt=""
                                class="selectMaterialStyle"
                                style="width: 120px; height: 120px"
                            />
                            <el-icon
                                v-if="item"
                                color="#7f7f7f"
                                size="20px"
                                style="position: absolute; right: -5px; top: -5px; background: #fff; border-radius: 50%; cursor: pointer"
                                @click="delImgHandle(index)"
                            >
                                <i-ep-circle-close />
                            </el-icon>
                        </div>
                        <div
                            v-if="submitForm.otherQualifications?.length < 15"
                            class="selectMaterialStyle"
                            style="width: 120px; height: 120px"
                            @click.stop="$route.name !== 'previewShop' && buttonlFn('otherQualifications')"
                        >
                            <el-icon class="avatar-uploader-icon">
                                <i-ep-plus />
                            </el-icon>
                        </div>
                    </VueDraggableNext>
                    <div
                        v-if="!submitForm.otherQualifications?.length"
                        class="selectMaterialStyle"
                        style="width: 120px; height: 120px"
                        @click.stop="$route.name !== 'previewShop' && buttonlFn('otherQualifications')"
                    >
                        <el-icon class="avatar-uploader-icon">
                            <i-ep-plus />
                        </el-icon>
                    </div>
                </el-row>
                <div style="color: rgba(69, 64, 60, 0.6); font-size: 12px">
                    尺寸建议750x750（正方形模式）像素以上，大小1M以下，最多15张 (可拖拽图片调整顺序 )
                </div>
            </el-form-item>
        </el-form>
    </div>
    <el-image-viewer v-if="showViewer" :url-list="previewList" @close="showViewer = false"></el-image-viewer>
</template>

<script setup lang="ts">
import QUpload from '@/components/q-upload/q-upload.vue'
import { FormRules } from 'element-plus'
import type { FormInstance } from 'element-plus'
import type { Ref } from 'vue'
import { OPERATOR_STATUS, ShopFormType } from '../types'
import { REGEX_CITIZEN_ID, REGEX_GROUP_CREDIT_CODE, REGEX_MOBILE } from '@/libs/validate'
import { DEFAULT_SYSTEM_IMG, REGEX } from '@/constant'
import { doGetBusinessInfoByOSS, doGetIDCardInfoByOSS } from '@/apis/shops'
import { ADR_OBJ_TYPE, findAddressCodesByString } from '@/utils/util'
import { AreaItem, useAddressStore } from '@/store/modules/address'

const $route = useRoute()
//父组件
const $parent = inject('addShops')
const { submitForm, dialogVisible, parameterId } = $parent as { submitForm: Ref<ShopFormType>; dialogVisible: Ref<boolean>; parameterId: Ref<string> }

const currentFormRef = ref<FormInstance>()
const addressStore = useAddressStore()
const addressCascaderProps = {
    value: 'code',
    label: 'name',
    emitPath: true,
    lazy: true,
    async lazyLoad(node, resolve) {
        const { level, pathValues = [] } = node
        let arr: any[] = []
        if (level === 0) {
            arr = await addressStore.getProvinces()
        } else if (level === 1) {
            arr = await addressStore.getCities(pathValues[0])
        } else if (level === 2) {
            arr = await addressStore.getDistricts(pathValues[1])
            arr = arr.map(({ name, code }) => ({ name, code, leaf: true }))
        }
        resolve(arr)
    },
}

// file 当前预览的文件对象用于取消删除
defineExpose({
    currentFormRef,
    componentFlag: 'info',
})

const currentValidityPeriod = computed(() => {
    if (submitForm.value.registerInfo.startDate && submitForm.value.registerInfo.endDate) {
        return [new Date(submitForm.value.registerInfo.startDate || ''), new Date(submitForm.value.registerInfo.endDate || '')]
    }
    return [new Date(), new Date()]
})

// 身份切换提示词
const legalPersonIdLabel = computed(() => (submitForm.value.subjectType === 'PERSON' ? '个人' : '法人'))

/**
 * @LastEditors: lexy
 * @description: 识别信息(许可证)
 * @param {*} url   上传后的地址
 * @returns {*}
 */
const changeLicense = (url: any) => {
    doGetBusinessInfoByOSS(url).then((res) => {
        if (res?.data) {
            submitForm.value.registerInfo.groupName = res.data.companyName
            submitForm.value.registerInfo.creditCode = res.data.creditCode
            nextTick(async () => {
                let address = res.data?.businessAddress?.match(REGEX.PROVINCE_CITY)?.groups
                if (address) {
                    submitForm.value.registerInfo.enterpriseAdress = address['street']
                    let adrList = (await findAddressCodesByString(address as ADR_OBJ_TYPE)) || []
                    submitForm.value.registerInfo.addressCodes = [...adrList]
                    submitForm.value.registerInfo.addressCode = adrList[adrList.length - 1]
                }
            })
            currentFormRef.value.validate()
        }
    })
}

/**
 * 手动调整注册地址信息
 * @param value
 */
const changeAdressCodes = (value: string[]) => {
    submitForm.value.registerInfo.addressCode = value[value.length - 1]
}

/**
 * @LastEditors: lexy
 * @description: 识别信息(身份证)
 * @param {*} url   上传后的地址
 * @returns {*}
 */
const changeIDCard = (url: string, type: 'front' | 'back') => {
    doGetIDCardInfoByOSS(url).then((res) => {
        if (res.data) {
            if (type === 'back') {
                submitForm.value.registerInfo.validType = res.data.validType
                submitForm.value.registerInfo.startDate = res.data.startDate
                submitForm.value.registerInfo.endDate = res.data.endDate
                submitForm.value.registerInfo.validityPeriod = [res.data.startDate, res.data.endDate]
            } else {
                if (res.data.legalPersonName) {
                    submitForm.value.registerInfo.legalPersonName = res.data.legalPersonName
                }
                if (res.data.legalPersonNo) {
                    submitForm.value.registerInfo.legalPersonNo = res.data.legalPersonNo
                }
            }
        }
    })
}
const infoFormRules = reactive<FormRules>({
    ['registerInfo.license']: [
        {
            required: true,
            message: '请上传营业执照',
            trigger: ['blur', 'change'],
        },
    ],
    ['registerInfo.groupName']: [
        {
            required: true,
            message: '请填写企业名称',
            trigger: ['blur', 'change'],
        },
    ],
    ['registerInfo.enterpriseAdress']: [
        {
            required: true,
            message: '请填写企业注册地址',
            trigger: ['blur', 'change'],
        },
    ],
    ['registerInfo.creditCode']: [
        {
            required: true,
            validator: (_, value: string, callback: (error?: any) => void) => {
                if (!REGEX_GROUP_CREDIT_CODE(value)) {
                    callback(new Error('请填写正确格式的信用代码！'))
                }
                callback()
            },
            trigger: ['blur', 'change'],
        },
    ],
    ['registerInfo.legalPersonIdFront']: [
        {
            required: true,
            message: `请上传正面照`,
            trigger: ['blur', 'change'],
        },
    ],
    ['registerInfo.legalPersonIdBack']: [
        {
            required: true,
            message: `请上传反面照`,
            trigger: ['blur', 'change'],
        },
    ],
    // ['registerInfo.handheldPhoto']: [
    //     {
    //         required: true,
    //         message: `请上传手持照`,
    //         trigger: 'change',
    //     },
    // ],
    ['registerInfo.legalPersonName']: [
        {
            required: true,
            message: `请填写${legalPersonIdLabel.value}姓名`,
            trigger: ['blur', 'change'],
        },
    ],
    ['registerInfo.addressCodes']: [
        {
            required: true,
            message: `请选择注册所在地`,
            trigger: ['blur', 'change'],
        },
    ],
    ['registerInfo.legalPersonPhone']: [
        {
            required: true,
            validator: (_, value: string, callback: (error?: any) => void) => {
                if (!value) {
                    callback(new Error('请填写法人手机号！'))
                }
                if (!REGEX_MOBILE(value)) {
                    callback(new Error('请填写正确格式的手机号！'))
                }
                callback()
            },
            trigger: ['blur', 'change'],
        },
    ],
    ['registerInfo.legalPersonNo']: [
        {
            required: true,
            validator: (_, value: string, callback: (error?: any) => void) => {
                if (!value) {
                    callback(new Error('请填写证件号！'))
                }
                if (!REGEX_CITIZEN_ID(value)) {
                    callback(new Error('请填写正确格式的证件号！'))
                }
                callback()
            },
            trigger: ['blur', 'change'],
        },
    ],
    ['registerInfo.operatorPhone']: [
        {
            required: true,
            validator: (_, value: string, callback: (error?: any) => void) => {
                if (!value) {
                    callback(new Error('请填写经营者手机号！'))
                }
                if (!REGEX_MOBILE(value)) {
                    callback(new Error('请填写正确格式的手机号！'))
                }
                callback()
            },
            trigger: ['blur', 'change'],
        },
    ],
    ['registerInfo.operatorUserName']: [
        {
            required: true,
            message: `请填写授权经营者姓名`,
            trigger: ['blur', 'change'],
        },
    ],
    ['registerInfo.operatorPersonNo']: [
        {
            required: true,
            validator: (_, value: string, callback: (error?: any) => void) => {
                if (!value) {
                    callback(new Error('请填写授权经营者身份证！'))
                }
                if (!REGEX_CITIZEN_ID(value)) {
                    callback(new Error('请填写正确格式的证件号！'))
                }
                callback()
            },
            trigger: ['blur', 'change'],
        },
    ],
    'registerInfo.validType': [
        {
            required: true,
            message: `请选择${legalPersonIdLabel.value}有效期类型`,
            trigger: ['blur', 'change'],
        },
    ],
    ['registerInfo.validityPeriod']: [
        {
            required: true,
            message: `请选择${legalPersonIdLabel.value}有效期`,
            trigger: ['blur', 'change'],
        },
    ],
})

/**
 * @LastEditors: lexy
 * @description: 删除资质照片
 * @param {number} index
 */
const delImgHandle = async (index: number) => {
    submitForm.value.otherQualifications?.splice(index, 1)
    await nextTick()
}

const buttonlFn = (val: string) => {
    dialogVisible.value = true
    parameterId.value = val
}

/**
 * 修改主体类型
 * @param val
 */
const changeSubjectType = (subjectType: string) => {
    if (subjectType !== 'INDIVIDUAL') {
        if (subjectType === 'PERSON') {
            submitForm.value.bankAccount.acctAttr = '0'
        }
        if (subjectType === 'COMPANY') {
            submitForm.value.bankAccount.acctAttr = '1'
        }
    } else if ($route.name === 'addShop') {
        submitForm.value.bankAccount.acctAttr = '0'
    }
}

/**
 * zrb:按钮预览大图
 *
 *
 * */
const showViewer = ref(false)
const previewList = ref<string[]>([])
const handlePreview = (type: string) => {
    switch (type) {
        case 'registerInfo':
            if (submitForm.value.registerInfo.legalPersonIdFront) previewList.value.push(submitForm.value.registerInfo.legalPersonIdFront)
            if (submitForm.value.registerInfo.legalPersonIdBack) previewList.value.push(submitForm.value.registerInfo.legalPersonIdBack)
            break
        default:
            break
    }
    if (previewList.value.length) showViewer.value = true
}
</script>

<style lang="scss">
@include b(shopInfo) {
    font-size: 12px;
    padding: 20px 25px 70px 46px;

    @include e(exp) {
        display: inline-block;
        width: 120px;
        height: 120px;

        @include m(img) {
            display: block;
            width: inherit;
            height: inherit;
            border: 1px solid;
        }

        @include m(tip) {
            position: fixed;
            bottom: 0;
            width: 100%;
            height: 28px;
            line-height: 28px;
            text-align: center;
            background: rgba(0, 0, 0, 0.36);
            color: #fff;
        }
    }

    @include e(tips) {
        color: #999;
    }
}

@include b(progress) {
    width: 20%;
    bottom: -15px;
    position: absolute;
}

.button-preview {
    cursor: pointer;
    color: #999;
    margin-left: 10px;
}
</style>
<style scoped>
.avatar-uploader .avatar {
    width: 178px;
    height: 178px;
    display: block;
}
</style>

<style>
.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
    border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 120px;
    height: 120px;
    text-align: center;
}

.ml20 {
    margin-left: 20px;
}

.selectMaterialStyle {
    width: 60px;
    height: 60px;
    border-radius: 5px;
    overflow: hidden;
    border: 1px dashed #ccc;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;

    span {
        color: #999;
        font-size: 20px;
    }
}
</style>
