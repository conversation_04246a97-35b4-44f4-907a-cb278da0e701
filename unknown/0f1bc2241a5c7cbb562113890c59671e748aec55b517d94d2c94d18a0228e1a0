<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-04-18 17:15:49
 * @LastEditors: lexy
 * @LastEditTime: 2022-11-04 14:15:55
-->
<template>
    <div class="shopFinance">
        <el-form ref="currentFormRef" :model="submitForm" label-width="110px" :rules="financeRules" :disabled="$route.meta.disabled">
            <el-form-item v-show="submitForm.subjectType === 'INDIVIDUAL'" label="账户类型" prop="bankAccount.acctAttr">
                <el-radio-group v-model="submitForm.bankAccount.acctAttr" class="ml-2" inline-prompt @change="changeAcct">
                    <el-radio value="1">对公</el-radio>
                    <el-radio value="0">对私</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="收款人" prop="bankAccount.payee">
                <el-input v-model="submitForm.bankAccount.payee" placeholder="请填写收款人" maxlength="50" disabled></el-input>
            </el-form-item>
            <el-form-item
                v-if="(submitForm.subjectType === 'INDIVIDUAL' && submitForm.bankAccount.acctAttr === '0') || submitForm.subjectType === 'PERSON'"
                label="预留手机号"
                prop="bankAccount.bankReservePhone"
                required
            >
                <el-input v-model="submitForm.bankAccount.bankReservePhone" placeholder="请填写银行预留手机号" maxlength="11"></el-input>
            </el-form-item>
            <el-form-item v-if="submitForm.subjectType !== 'PERSON'" label="开户行城市" prop="bankAccount.openBankPC">
                <el-cascader
                    ref="adrCascaderRef"
                    v-model="submitForm.bankAccount.openBankPC"
                    clearable
                    :check-strictly="true"
                    :props="addressCascaderProps"
                    placeholder="请填写开户行所在省市"
                    @change="changePC"
                />
            </el-form-item>
            <el-form-item label="银行名" prop="bankAccount.bankName">
                <el-input v-model="submitForm.bankAccount.bankName" placeholder="请填写银行名" maxlength="20" />
            </el-form-item>
            <el-form-item label="账号" prop="bankAccount.bankAccount">
                <el-input v-model="submitForm.bankAccount.bankAccount" placeholder="请填写账号" maxlength="32"></el-input>
            </el-form-item>
            <el-form-item label="开户行" prop="bankAccount.openAccountBank">
                <el-input v-model="submitForm.bankAccount.openAccountBank" placeholder="请填写开户行" maxlength="20"></el-input>
            </el-form-item>
            <template v-if="submitForm.bankAccount.acctAttr === '1'">
                <el-form-item label="银行代码" prop="bankAccount.openBankNo" required>
                    <q-page-select
                        v-model="submitForm.bankAccount.openBankNo"
                        placeholder="请填写银行代码"
                        allow-create
                        default-first-option
                        :options="bankCodeOptions"
                        :reserve-keyword="false"
                        :total="total"
                        :page-info="{
                            size: 10,
                        }"
                        remote
                        filterable
                        maxlength="20"
                        remote-show-suffix
                        :loading="remoteLoading"
                        :option-key="'bankCode'"
                        :label-key="'bankName'"
                        :value-key="'bankCode'"
                        :remote-method="(pageConfig) => handlePageChange(pageConfig, 'bankCode')"
                        @page-change="(pageConfig) => handlePageChange(pageConfig, 'bankCode')"
                        @change="(val: any) => changeBankCodesInfo(val, 'openBankNo')"
                    >
                        <template #option="{ item }">
                            <span style="float: left">{{ item.bankName }}(代码：{{ item.bankCode }})</span>
                            <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">
                                {{ item.netBankCode }}
                            </span>
                        </template>
                    </q-page-select>
                </el-form-item>
                <el-form-item label="银行行号" prop="bankAccount.payBankNumber" required>
                    <q-page-select
                        v-model="submitForm.bankAccount.payBankNumber"
                        placeholder="请填写银行行号"
                        allow-create
                        default-first-option
                        :options="bankCodeOptions"
                        :total="total"
                        :reserve-keyword="false"
                        remote
                        :page-info="{
                            size: 10,
                        }"
                        filterable
                        maxlength="20"
                        remote-show-suffix
                        :loading="remoteLoading"
                        :option-key="'netBankCode'"
                        :label-key="'bankName'"
                        :value-key="'netBankCode'"
                        :remote-method="(pageConfig) => handlePageChange(pageConfig, 'netBankCode')"
                        @page-change="(pageConfig) => handlePageChange(pageConfig, 'netBankCode')"
                        @change="(val: any) => changeBankCodesInfo(val, 'payBankNumber')"
                    >
                        <template #option="{ item }">
                            <span style="float: left">{{ item.bankName }}(代码：{{ item.bankCode }})</span>
                            <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">
                                {{ item.netBankCode }}
                            </span>
                        </template>
                    </q-page-select>
                </el-form-item>
            </template>
        </el-form>
    </div>
</template>

<script lang="ts" setup>
import type { Ref } from 'vue'
import { FormRules } from 'element-plus'
import { ShopFormType } from '../types'
import { REGEX_MOBILE } from '@/libs/validate'
import { useAddressStore } from '@/store/modules/address'
import { doGetBankNumByAreaCode } from '@/apis/shops/index'
const $route = useRoute()
//父组件
const $parent = inject('addShops')
const submitForm = ($parent as { submitForm: Ref<ShopFormType> }).submitForm
const currentFormRef = ref()
// 开户城市信息主体
const adrCascaderRef = ref()
const addressStore = useAddressStore()
const addressCascaderProps = {
    value: 'code',
    label: 'name',
    emitPath: true,
    lazy: true,
    async lazyLoad(node, resolve) {
        const { level, pathValues = [] } = node
        let arr: any[] = []
        if (level === 0) {
            arr = await addressStore.getProvinces()
        } else if (level === 1) {
            arr = await addressStore.getCities(pathValues[0])
            arr = arr.map(({ name, code }) => ({ name, code, leaf: true }))
        }
        resolve(arr)
    },
}
const remoteLoading = ref<boolean>(false)
type BankCodeInfoType = {
    netBankCode: string // 银行行号
    bankCode: string // 银行代码
    bankName: string // 支行名称
    provinceCode: string // 省代码
    areaCode: string // 地区码
}
const bankCodeOptions = ref<BankCodeInfoType[]>([])
defineExpose({
    currentFormRef,
    componentFlag: 'finance',
})

// 校验手机号
function checkPhone(rule: any, value: any, callback: any) {
    if (value === '') {
        callback(new Error('请填写联系方式'))
    } else if (!REGEX_MOBILE(value)) {
        callback(new Error('请填写正确的手机号'))
    } else {
        callback()
    }
}
const financeRules = reactive<FormRules>({
    'bankAccount.bankReservePhone': [
        {
            required: true,
            validator: checkPhone,
            trigger: 'blur',
        },
    ],
    'bankAccount.openBankNo': [
        {
            required: true,
            message: '请填写银行代码',
            trigger: 'blur',
        },
    ],
    'bankAccount.openBankPC': [
        {
            required: true,
            message: '请选择开户行城市',
            trigger: 'blur',
        },
    ],
    'bankAccount.payBankNumber': [
        {
            required: true,
            message: '请填写银行行号',
            trigger: 'blur',
        },
    ],
    'bankAccount.payee': [
        {
            required: true,
            message: '请填写收款人',
            trigger: 'blur',
        },
    ],
    'bankAccount.bankName': [
        {
            required: true,
            message: '请填写银行名',
            trigger: 'blur',
        },
    ],
    'bankAccount.bankAccount': [
        {
            required: true,
            message: '请填写账号',
            trigger: 'blur',
        },
    ],
    'bankAccount.openAccountBank': [
        {
            required: true,
            message: '请填写开户行',
            trigger: 'blur',
        },
    ],
})
/**
 * 修改省市
 * @param val
 */
const changePC = (val: string[]) => {
    submitForm.value.bankAccount.openBankProvince = val[0]
    submitForm.value.bankAccount.openBankCity = val[1]
    resetBankCodesInfo()
}
/**
 * 修改对公账户
 * 账户状态（对公:1 / 对私: 0)
 */
const changeAcct = (changeAcct: string | number) => {
    if (changeAcct.toString() === '1') {
        submitForm.value.bankAccount.payee = submitForm.value.registerInfo.groupName
        submitForm.value.acctAttr = '1'
    }
    if (changeAcct.toString() === '0') {
        submitForm.value.bankAccount.payee = submitForm.value.registerInfo.legalPersonName
        submitForm.value.acctAttr = '0'
    }
}

/**
 * 银行代码等信息变更
 * @param value 调整的数据
 */
const changeBankCodesInfo = (value: string | BankCodeInfoType, key?: string) => {
    if (typeof value === 'string' && key) {
        submitForm.value.bankAccount[key] = value
    } else {
        submitForm.value.bankAccount.openBankNo = value.bankCode
        submitForm.value.bankAccount.openAccountBank = value.bankName
        submitForm.value.bankAccount.payBankNumber = value.netBankCode
    }
}

/**
 * 重置银行卡号信息
 */
const resetBankCodesInfo = () => {
    submitForm.value.bankAccount.openBankNo = ''
    submitForm.value.bankAccount.payBankNumber = ''
    submitForm.value.bankAccount.openAccountBank = ''
}

const total = ref(0)
// 处理分页变化
const handlePageChange = ({ pageNum, pageSize, query = '' }: { pageNum: number; pageSize: number; query?: string }, key: string) => {
    remoteLoading.value = true
    const nodes = adrCascaderRef.value.getCheckedNodes()
    if (nodes?.[0]?.data?.code) {
        const { bankName, openBankNo = '' } = submitForm.value.bankAccount
        doGetBankNumByAreaCode(pageNum, pageSize, nodes?.[0]?.data?.code, bankName, openBankNo ? { [key]: openBankNo } : {})
            .then((res) => {
                if (res?.data) {
                    bankCodeOptions.value = res.data?.list || []
                    total.value = Number(res.data?.total) || 0
                }
            })
            .finally(() => {
                remoteLoading.value = false
            })
    }
}
</script>

<style lang="scss">
@include b(shopFinance) {
    padding: 20px 25px 70px 46px;
}
</style>
