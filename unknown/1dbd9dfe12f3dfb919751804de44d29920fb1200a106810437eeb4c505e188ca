<script setup lang="ts">
import EditorFormData from '../packages/components/index/formModel'
import { useDecorationStore } from '@/store/modules/decoration/index'
import { cloneDeep } from 'lodash-es'
import { ElMessage } from 'element-plus'
import { VueDraggableNext } from 'vue-draggable-next'
import type { ComponentItem } from '../packages/components/index/formModel'

const $decorationStore = useDecorationStore()
const $emit = defineEmits(['change'])
const activeName = ref('first')
const first = ref(true)
const second = ref(true)
const componentList = ref<ComponentItem[]>([
    {
        icon: 'lunbotu',
        value: 'swiper',
        label: '轮播图',
    },
    {
        icon: 'shangpin',
        value: 'goods',
        label: '商品',
    },
    {
        icon: 'sousuo',
        value: 'search',
        label: '搜索',
    },

    {
        icon: 'dianpudaohang',
        value: 'navigation',
        label: '金刚区',
    },
    {
        icon: 'mofang',
        value: 'cubeBox',
        label: '魔方',
    },
])
// 营销组件
const marketComponents = ref<ComponentItem[]>([
    {
        icon: 'cylx-onlyPromotion',
        value: 'onlyPromotion',
        label: '会员专享',
    },
])
const activePageType = computed(() => {
    return $decorationStore.activePageType
})
/**
 * 点击添加组件
 * @param {*} currentComponent
 */
const handleAddComponent = (currentComponent: ComponentItem) => {
    if (activePageType.value !== 'customize') {
        ElMessage.warning('该页面无法添加组件')
        return
    } else {
        const FormData = cloneDeep(EditorFormData[currentComponent.value])
        $emit('change', { ...currentComponent, id: Date.now(), formData: FormData })
    }
}
</script>

<template>
    <div class="editor__component editor__component_new">
        <div class="editor_component_wrap">
            <el-scrollbar style="height: 75%">
                <div class="editor_component_wrap_main">
                    <div class="component_title" @click="first = !first">
                        <div :class="first ? 'jiantou_bot' : 'jiantou_right'"></div>
                        <div class="component_title_text">基本组件</div>
                    </div>
                    <VueDraggableNext
                        v-show="first"
                        :list="componentList"
                        class="component_box"
                        :group="{ name: 'custom', pull: 'clone', put: false }"
                        item-key="label"
                    >
                        <div v-for="item in componentList" :key="item.label" style="width: 93px" @click="handleAddComponent(item)">
                            <div class="component--item">
                                <div class="iconfont component--item--icon" :class="`icon-${item.icon}`"></div>
                                <div>{{ item.label }}</div>
                            </div>
                        </div>
                    </VueDraggableNext>
                </div>
            </el-scrollbar>
            <el-scrollbar style="height: 35%">
                <div class="editor_component_wrap_main">
                    <div class="component_title" @click="first = !first">
                        <div :class="first ? 'jiantou_bot' : 'jiantou_right'"></div>
                        <div class="component_title_text">营销组件</div>
                    </div>
                    <VueDraggableNext
                        v-show="second"
                        :list="marketComponents"
                        class="component_box"
                        :group="{ name: 'custom', pull: 'clone', put: false }"
                        item-key="label"
                    >
                        <div v-for="item in marketComponents" :key="item.label" style="width: 93px" @click="handleAddComponent(item)">
                            <div class="component--item">
                                <div
                                    :class="`${!item.icon.includes('cylx') ? 'iconfont' : 'cylx-iconfont'} ${item.icon}`"
                                    class="component--item--icon"
                                ></div>
                                <div>{{ item.label }}</div>
                            </div>
                        </div>
                    </VueDraggableNext>
                </div>
            </el-scrollbar>
        </div>
    </div>
</template>

<style lang="scss" scoped>
@import '@/assets/css/decoration/editPage.scss';
.component--item--icon {
    font-size: 28px;
    text-align: center;
    margin-bottom: 19px;
}
.editor__component_new .is-horizontal {
    display: none;
    overflow-x: hidden;
}
</style>
