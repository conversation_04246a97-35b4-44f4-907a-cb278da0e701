/*
 * @description:
 * @Author: lexy
 * @Date: 2022-09-13 15:36:13
 * @LastEditors: lexy
 * @LastEditTime: 2022-09-13 18:32:54
 */
/**
 * @LastEditors: lexy
 * @description: 评价枚举
 */
export enum EvaluationType {
    //有内容
    CONTENT,
    //有图片
    IMAGE,
}
/**
 * @LastEditors: lexy
 * @description:
 * @param {*} contentCount 有内容数量
 * @param {*} evaluate  Evaluate
 * @param {*} mediaCount 图片数量
 * @param {*} praiseCount 好评数量
 * @param {*} praiseRatio 好评率
 * @param {*} totalCount 好评总数
 */
export interface ApiEvaluation {
    contentCount: string
    evaluate: Evaluate
    mediaCount: string
    praiseCount: string
    praiseRatio: string
    totalCount: string
}
/**
 * @LastEditors: lexy
 * @description:
 * @param {*} comment 文本描述
 * @param {*} image  商品图片
 * @param {*} isExcellent 精选
 * @param {*} medias 评价图片
 * @param {*} name 商品名称
 * @param {*} productId 产品id
 * @param {*} rate 评价星级
 * @param {*} specs 规格
 * @param {*} shopReply 商家回复
 */
export interface Evaluate {
    comment: string
    createTime: string
    deleted: boolean
    id: string
    image: string
    isExcellent: boolean
    itemId: string
    medias: string[]
    name: string
    orderNo: string
    packageId: string
    productId: string
    rate: number
    shopId: string
    skuId: string
    specs: string[]
    updateTime: string
    userId: string
    shopReply?: string
    avatar?: string
    nickname?: string
    isDisplay?: boolean
}
export interface NewEvaluate extends Evaluate {
    text: string
}
export type Rate = 1 | 2 | 3 | 4 | 5
export interface EvaluateSearchData {
    name: string
    nickname: string
    clinchTime: string
    rate: string
}
export interface EvaluateSearchParams {
    name: string
    nickname: string
    rate: string
    startTime: string
    endTime: string
}
