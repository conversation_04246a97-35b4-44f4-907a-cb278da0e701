<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-05-14 15:22:21
 * @LastEditors: lexy
 * @LastEditTime: 2024-04-19 16:34:44
-->
<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { InfoFilled, Select } from '@element-plus/icons-vue'
import Line from './Line.vue'
import { doSmsSet, doGetSms } from '@/apis/setting'
/*
 *variable
 */
const enumServerForm = reactive({
    TENCENT: {
        type: 'TENCENT',
        // 供应商
        providerName: '腾讯',
        // 供应商APPid
        providerAppId: '',
        // 供应商appSecrete
        providerAppSecret: '',
        // 短信签名
        signature: '',
        // 模板信息
        smsTemplate: {},
    },
    ALIYUN: {
        type: 'ALIYUN',
        // 供应商
        providerName: '阿里',
        // 供应商APPid
        providerAppId: '',
        // 供应商appSecrete
        providerAppSecret: '',
        // 短信签名
        signature: '',
        // 模板信息
        smsTemplate: {},
    },
})
const smsTemplateForm = ref(
    // templateName smsTemplateType smsTemplateContent 后端采取默认数据 不会采取前端数据的 所以无需用户填写
    {
        templateName: '禁止用户输入字段',
        templateCode: '',
        smsTemplateType: 'CAPTCHA',
        smsTemplateContent: '您的验证码为 ${code}，请勿告知他人',
    },
)
const currentRadio = ref<'TENCENT' | 'ALIYUN'>('TENCENT')
/*
 *lifeCircle
 */
initForm('TENCENT')
/*
 *function
 */
const validFormat = () => {
    return Object.values(enumServerForm[currentRadio.value]).includes('') || Object.values(smsTemplateForm.value).includes('')
}
const submitHandle = async () => {
    if (validFormat()) {
        ElMessage.error('请填写完成表单')
        return
    }
    enumServerForm[currentRadio.value].smsTemplate = { ...smsTemplateForm.value }
    const { code, success } = await doSmsSet(enumServerForm[currentRadio.value])
    if (code === 200 && success) {
        ElMessage.success('保存成功')
        initForm(currentRadio.value)
    } else {
        ElMessage.error('保存失败')
    }
}

const currentUsing = ref('')
async function initForm(val: any) {
    reset()
    const { code, data, success } = await doGetSms(val)
    if (code === 200 && success) {
        data.conf?.smsTemplate && (smsTemplateForm.value = data.conf.smsTemplate)

        currentUsing.value = data.using

        Reflect.deleteProperty(data.conf, 'smsTemplate')

        enumServerForm[currentRadio.value] = {
            type: val,
            ...data.conf,
        }
    }
}
function reset() {
    smsTemplateForm.value = {
        // templateName smsTemplateType smsTemplateContent 后端采取默认数据 不会采取前端数据的 所以无需用户填写
        templateName: '禁止用户输入字段',
        templateCode: '',
        smsTemplateType: 'CAPTCHA',
        smsTemplateContent: '您的验证码为 ${code}，请勿告知他人',
    }
}
const checkUsing = (str: string) => currentUsing.value === str
</script>

<template>
    <Line name="服务器设置" color="#08CC00" />
    <el-form>
        <el-radio-group v-model="currentRadio" size="large" @change="initForm">
            <el-radio-button label="TENCENT">
                <span class="btn-icon" :class="{ active: checkUsing('TENCENT') }">
                    <el-icon v-show="checkUsing('TENCENT')"><Select /></el-icon>
                </span>
                <span :class="{ 'text-color': checkUsing('TENCENT') }"> 腾讯 </span>
            </el-radio-button>
            <el-radio-button label="ALIYUN">
                <span class="btn-icon" :class="{ active: checkUsing('ALIYUN') }">
                    <el-icon v-show="checkUsing('ALIYUN')"><Select /></el-icon>
                </span>
                <span :class="{ 'text-color': checkUsing('ALIYUN') }"> 阿里 </span>
            </el-radio-button>
        </el-radio-group>
        <div class="m-t-20">
            <el-form-item label-width="100px">
                <template #label>
                    应用ID
                    <el-tooltip effect="dark" content="providerAppId" placement="top">
                        <el-icon class="label-icon"><InfoFilled /></el-icon>
                    </el-tooltip>
                </template>
                <el-input v-model.trim="enumServerForm[currentRadio].providerAppId" class="cusInput" />
            </el-form-item>
            <el-form-item label-width="100px">
                <template #label>
                    应用KEY
                    <el-tooltip effect="dark" content="providerAppSecret" placement="top">
                        <el-icon class="label-icon"><InfoFilled /></el-icon>
                    </el-tooltip>
                </template>

                <el-input v-model.trim="enumServerForm[currentRadio].providerAppSecret" class="cusInput" />
            </el-form-item>
            <el-form-item label-width="100px">
                <template #label>
                    签名内容
                    <el-tooltip effect="dark" content="signature" placement="top">
                        <el-icon class="label-icon"> <InfoFilled /></el-icon>
                    </el-tooltip>
                </template>

                <el-input v-model.trim="enumServerForm[currentRadio].signature" class="cusInput" />
            </el-form-item>
        </div>
        <div>
            <el-form-item label-width="100px">
                <template #label>
                    模板ID
                    <el-tooltip effect="dark" content="templateCode" placement="top">
                        <el-icon class="label-icon"><InfoFilled /></el-icon>
                    </el-tooltip>
                </template>
                <el-input v-model.trim="smsTemplateForm.templateCode" class="cusInput" />
            </el-form-item>
        </div>
        <el-form-item>
            <el-button type="primary" @click="submitHandle">保存</el-button>
        </el-form-item>
    </el-form>
</template>

<style lang="scss" scoped>
@include b(tab_container) {
    width: 100%;
    height: 100%;
}
@include b(cusInput) {
    width: 300px;
    height: 30px;
}

@include b(btn-icon) {
    position: absolute;
    inset: 0;
}

@include b(text-color) {
    position: relative;
    color: #fff;
    background-color: transparent;
    z-index: 3;
}

@include b(active) {
    background-color: #409eff;
    color: #fff;
    @include b(el-icon) {
        position: absolute;
        top: 0;
        right: 0;
    }
}

@include b(el-radio-group) {
    :deep(.el-radio-button__inner) {
        --el-radio-button-checked-text-color: --el-text-color-regular;
        --el-radio-button-checked-bg-color: #fff;
    }
}

@include b(m-t-20) {
    margin-top: 20px;
}

@include b(el-form-item) {
    :deep(.el-form-item__label) {
        display: flex;
        align-items: center;
    }
}

@include b(label-icon) {
    margin: 0 5px;
    font-size: 18px;
    cursor: pointer;
}
</style>
