<!--
 * @description: 组件渲染
 * @Author: lexy
 * @Date: 2022-08-26 13:13:18
 * @LastEditors: lexy
 * @LastEditTime: 2024-02-04 17:15:06
-->
<script setup lang="ts">
import { PropType, ref, computed, watch, onMounted, defineExpose, defineAsyncComponent } from 'vue'
import { useVModel } from '@vueuse/core'
import { useDecorationStore } from '@/store/modules/decoration'
import preview from '../packages/components/index/preview'
import editorFormData from '../packages/components/index/formModel'
import type { ComponentItem } from '../packages/components/index/formModel'
import { VueDraggableNext } from 'vue-draggable-next'
import { cloneDeep } from 'lodash'
/*
 *variable
 */
const $props = defineProps({
    components: {
        type: Array as PropType<ComponentItem[]>,
        default() {
            return []
        },
    },
    isPreview: {
        type: <PERSON><PERSON><PERSON>,
        default() {
            return false
        },
    },
})
const $emit = defineEmits(['change', 'add', 'del'])
const $decorationStore = useDecorationStore()
const delFlag = ref(-1)
// 拖动的组件的下角标
const dragStarIndex = ref(-1)
// 选中的当前项
const curreentFlag = ref(-1)
const activePageType = computed(() => {
    return $decorationStore.activePageType
})
const myScrollbar = ref()
/*
 *lifeCircle
 */
onMounted(() => {
    // onWrapSroll()
})
/*
 *function
 */
const handleAddComponent = (e: any) => {
    console.log('----1')

    const index = e.newDraggableIndex
    const com = $props.components[index]
    const FormData = editorFormData[com.value]
    const cloneFormData = cloneDeep(FormData)
    $emit(
        'add',
        {
            ...com,
            id: Date.now(),
            formData: cloneFormData,
        },
        index,
    )
}
/**
 * @LastEditors: lexy
 * @description: 删除当前组件
 * @param {number} i
 */
const handleDelCurCom = (i: number) => {
    delTip()
    $emit('del', { index: i })
}
/**
 * 父级调用设置当前选中的组件
 */
const setCurrentFlag = (i: number) => {
    curreentFlag.value = i
}
/**
 * @LastEditors: lexy
 * @description: 监听组件设置切换
 * @param {ComponentItem} currentComponent 当前组件信息
 * @param {number} i
 */
function handleCurrentComponent(currentComponent: ComponentItem, i: number) {
    if ($props.isPreview) return
    curreentFlag.value = i
    $decorationStore.SET_ACTIVE_COMINDEX(i)
    $emit('change', currentComponent)
}
// function onWrapSroll() {
//     const scrollbarEl = myScrollbar.value.wrap$
//     scrollbarEl.onscroll = function () {
//         if (document.querySelector('.el-popover')) {
//             document.querySelector('.el-popover')?.remove()
//         }
//     }
// }
function delTip() {
    const popover = document.querySelector('.el-popover')
    if (popover) popover.remove()
}
defineExpose({
    setCurrentFlag,
})
</script>

<template>
    <div class="editor__preview">
        <el-scrollbar ref="myScrollbar" style="height: 100%" :noresize="true">
            <VueDraggableNext
                :list="$props.components"
                :sort="true"
                :group="'custom'"
                animation="500"
                :scroll="true"
                :disabled="$props.isPreview"
                @add="handleAddComponent"
            >
                <transition-group>
                    <div
                        v-for="(component, i) of $props.components"
                        :id="`editor-preview-com-${i}`"
                        :key="i + 1"
                        :class="[
                            'component--item',
                            component.value !== undefined && delFlag === i ? 'iscur__component--item' : '',
                            curreentFlag === i ? 'select__component--item' : '',
                        ]"
                        :style="$props.isPreview ? 'border:none' : ''"
                        style="width: 375px"
                        @click="handleCurrentComponent(component, i)"
                        @mouseover="delFlag = i"
                        @mouseleave="delFlag = -1"
                    >
                        <div
                            v-show="activePageType !== 'classification' && activePageType !== 'control' && delFlag === i && !$props.isPreview"
                            class="component--item__tan"
                        >
                            <div class="component--item__text" @click="handleCurrentComponent(component, i)">编辑</div>
                            <el-icon class="component--item__icon" @click="handleDelCurCom(i)"><i-ep-delete /></el-icon>
                        </div>
                        <component :is="preview[component.value]" :form-data="component.formData"></component>
                    </div>
                </transition-group>
            </VueDraggableNext>
        </el-scrollbar>
    </div>
</template>

<style lang="scss" scoped>
@import '@/assets/css/decoration/editPage.scss';
</style>
