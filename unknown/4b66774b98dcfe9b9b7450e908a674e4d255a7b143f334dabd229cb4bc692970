import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { Search, Swipe, SwipeItem, Icon, Image as VanImage, CountDown } from 'vant'
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import 'cropperjs/dist/cropper.css'
import '@/assets/css/base.scss'
import '@/assets/css/font/iconfont.css'
import '@/assets/css/font/iconfont.js'
import '@/assets/css/font/cylx-icon/iconfont.css'
import '@/assets/css/font/cylx-icon/iconfont.js'
import 'vant/lib/index.css'
import 'element-plus/dist/index.css'
import { initPlugin } from '@/libs/plugin'
import { createPinia } from 'pinia'

const pinia = createPinia()
const app = createApp(App)

app.use(Search)
    .use(Swipe)
    .use(SwipeItem)
    .use(ElementPlus, {
        locale: { ...zhCn },
    })
    .use(VanImage)
    .use(CountDown)
    .use(Icon)
    .use(router)
    .use(pinia)
    .use(initPlugin)
    .mount('#app')
