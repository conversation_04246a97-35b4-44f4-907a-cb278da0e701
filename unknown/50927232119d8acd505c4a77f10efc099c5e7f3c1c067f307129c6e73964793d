<script setup lang="ts">
import { FormRules, FormInstance, ElMessage } from 'element-plus'
import {
    doSaveBasicSet,
    doGetBasicSet,
    doSetDefaultStore,
    doGetDefaultStore,
    doGetGoodsAuditSetting,
    doPutGoodsAuditSetting,
    doGetIntergalTimeoutSetting,
    doPutIntergalTimeoutSetting,
    doGetRandomInjection,
    doSetRandomInjection,
    doGetOnlyPromotionConfigSet,
    doSetOnlyPromotionConfigSet,
    doGetOnlyPromotionData,
} from '@/apis/setting'
import Line from './Line.vue'
import qSelectShop from '@components/q-select-shop/q-select-shop.vue'
import QPluginPurchaseOrderSetting from '@/q-plugin/supplier/PurchaseOrderSetting.vue'
import { ShopItem } from '@/components/q-select-shop/type'

/*
 *variable
 */
const purchaseOrderSettingRef = ref<InstanceType<typeof QPluginPurchaseOrderSetting> | null>(null)
const oneHourSecond = 60 * 60
const oneDaySecond = 24 * oneHourSecond
const timeout = reactive({
    d: 0,
    h: 0,
    m: 0,
    supplierD: 0,
    supplierH: 0,
    supplierM: 0,
    payTimeout: 0,
    confirmTimeout: 0,
    commentTimeout: 0,
    afsAuditTimeout: 0,
    supplierAfsAuditTimeout: 0,
    integralTimeout: 0,
    productAuditType: '',
    defaultStoreId: '',
})
const ruleFormRef = ref()
// 选中的默认推荐店铺
const selectedStore = ref<ShopItem[]>([])
// 控制选择店铺弹框变量
const dialogVisible = ref(false)
// 会员专享活动预告配置信息
const onlyPromotionActivities = ref([])
const onlyPromotionPreConfig = ref({
    onlyItems: [],
    open: false,
    preTime: 12,
})

// 随机注水值
const randomInjection = ref<{
    max: number
    min: number
    showSaleNum?: boolean
}>({ max: 0, min: 0, showSaleNum: false })

// 未支付订单校验
const notPayValid = (rule: any, value: any, callback: any) => {
    const minutes = timeout.d * oneDaySecond + timeout.h * oneHourSecond + timeout.m * 60
    if (minutes < 15 * 60 || minutes > 30 * 24 * 60 * 60) {
        callback(new Error('可以设置的区间为15分钟至30天'))
        return
    }
    callback()
}
// 0-30区间校验
const validIntervalCheck = (rule: any, value: any, callback: any) => {
    if (!value) {
        callback(new Error('请填写天数'))
    } else if (value < 0 || value > 30) {
        callback(new Error('区间为0-30'))
    } else {
        callback()
    }
}
const rules = reactive<FormRules>({
    confirmTimeout: [{ validator: validIntervalCheck, trigger: 'blur' }],
    commentTimeout: [{ validator: validIntervalCheck, trigger: 'blur' }],
    afsAuditTimeout: [{ validator: validIntervalCheck, trigger: 'blur' }],
    payTimeout: [{ validator: notPayValid, trigger: 'blur' }],
    randomInjection: [
        {
            validator: (rule: any, value: any, callback: any) => {
                const { max = 0, min = 0 } = randomInjection.value
                if (!max || !min) {
                    callback('请输入随机注水值！')
                } else if (Number(max) < 0 || Number(min) < 0) {
                    callback('请输入正确的随机注水值,注水值必须大于0！')
                } else if (Number(max) < Number(min)) {
                    callback('请输入正确的随机注水值，最大值不得小于最小值！')
                } else {
                    callback()
                }
            },
            trigger: ['blur', 'change'],
        },
    ],
})
/*
 *lifeCircle
 */
onMounted(() => {
    initForm()
    initGoodsAuditSetting()
    initIntergalTimeoutSetting()
    initOnlyPromotionData()
})
const submitBasicForm = () => {
    submitHandle()
    purchaseOrderSettingRef.value?.qPlugin?.currentComponentRef?.handOrderTime()
    // handOrderTime()
    handleSubmitGoodsAudit()
    handleSubmitIntergalTimeout()
}

const handleSubmitGoodsAudit = () => {
    doPutGoodsAuditSetting(timeout.productAuditType)
}
const handleSubmitIntergalTimeout = () => {
    doPutIntergalTimeoutSetting(timeout.integralTimeout * oneDaySecond)
}
/*
 *function
 */
const submitHandle = () => {
    const formEl = ruleFormRef.value as FormInstance
    if (!formEl) return
    formEl.validate((valid) => {
        if (!valid) {
            return
        }
        const { onlyItems, preTime, open } = onlyPromotionPreConfig.value
        if (!preTime) {
            return ElMessage.warning('选择提前预告时间')
        }
        // if (!onlyItems || !onlyItems.length) {
        //     return ElMessage.warning('选择提前预告的会员专享活动')
        // }
        const onlyInfos = onlyItems?.map(({ id, onlyName }) => ({ onlyId: id, name: onlyName }))
        const form = {
            //支付超时时间 单位秒
            payTimeout: timeout.d * oneDaySecond + timeout.h * oneHourSecond + timeout.m * 60,
            //确认收货超时时间 单位秒
            confirmTimeout: timeout.confirmTimeout * oneDaySecond,
            //评价超时时间 单位秒
            commentTimeout: timeout.commentTimeout * oneDaySecond,
            //商家售后审核超时时间
            afsAuditTimeout: timeout.afsAuditTimeout * oneDaySecond,
            //供应商售后审核超时时间
            supplierAfsAuditTimeout: timeout.supplierAfsAuditTimeout * oneDaySecond,
        }
        const { max = 0, min = 0, showSaleNum = false } = randomInjection.value
        Promise.allSettled([
            doSaveBasicSet(form),
            doSetDefaultStore(timeout.defaultStoreId),
            doSetRandomInjection(Number(min), Number(max), showSaleNum),
            doSetOnlyPromotionConfigSet(open, preTime, onlyInfos || []),
        ]).then((results) => {
            let allSuccess = results.find(
                (res) => (res.status === 'fulfilled' && (res.value.code !== 200 || !res.value.success)) || res.status === 'rejected',
            )
            if (!allSuccess) {
                ElMessage.success('保存成功')
            } else {
                ElMessage.error(allSuccess?.value?.msg || '保存失败')
            }
        })
    })
}
const secondToDays = (seconds: string) => {
    return Math.floor(parseInt(seconds) / oneDaySecond)
}

const secondToPayTimeout = (seconds: string) => {
    const nums = parseInt(seconds)
    let dd = Math.floor(nums / oneDaySecond)
    let hh = Math.floor((nums % oneDaySecond) / oneHourSecond)
    let mm = Math.floor((nums % oneHourSecond) / 60)
    return [dd, hh, mm]
}

async function initForm() {
    Promise.all([doGetBasicSet(), doGetDefaultStore(), doGetRandomInjection()]).then((results) => {
        results.forEach(({ data, code }, index) => {
            if (code !== 200) {
                throw Error(`获取设置失败`)
            }
            switch (index) {
                case 0:
                    if (data) {
                        const timeSplit = secondToPayTimeout(data.payTimeout)
                        timeout.d = timeSplit[0]
                        timeout.h = timeSplit[1]
                        timeout.m = timeSplit[2]

                        timeout.confirmTimeout = secondToDays(data.confirmTimeout)
                        timeout.commentTimeout = secondToDays(data.commentTimeout)
                        timeout.afsAuditTimeout = secondToDays(data.afsAuditTimeout)
                        timeout.supplierAfsAuditTimeout = secondToDays(data.supplierAfsAuditTimeout)
                    }
                    break
                case 1:
                    selectedStore.value = [data as ShopItem]
                    timeout.defaultStoreId = data.id
                    break
                case 2:
                    randomInjection.value = data || {}
                    break
                default:
                    break
            }
        })
    })
}

async function initGoodsAuditSetting() {
    const { code, data } = await doGetGoodsAuditSetting()
    if (code !== 200) {
        return
    }
    timeout.productAuditType = data
}

async function initIntergalTimeoutSetting() {
    const { code, data } = await doGetIntergalTimeoutSetting()
    if (code !== 200) return
    timeout.integralTimeout = secondToDays(data)
}

async function initOnlyPromotionData() {
    const {
        data: { records: activities },
    } = await doGetOnlyPromotionData()
    if (activities?.length) {
        onlyPromotionActivities.value = activities
    }

    const { code, data } = await doGetOnlyPromotionConfigSet()
    if (code === 200 && data) {
        onlyPromotionPreConfig.value = {
            preTime: data.preTime || 12,
            open: data.open || false,
            onlyItems: (data.infos || []).map(({ onlyId, name }) => ({ id: onlyId, onlyName: name })),
        }
    }
}

// 打开选择店铺弹框
const openSelectOpen = () => {
    dialogVisible.value = true
}
// 点击确定更改父组件中的数据
const confirm = (rows: ShopItem[]) => {
    if (!rows || !rows.length) return
    const { name, id, logo } = rows[0]
    // 更改的值
    timeout.defaultStoreId = id
    selectedStore.value = rows
    dialogVisible.value = false
}

//zrb:最大预热时间(小时)
const preMax = ref(720)
</script>

<template>
    <el-form ref="ruleFormRef" :model="timeout" :rules="rules">
        <Line name="下单设置" color="#08CC00" />
        <div class="msg">
            <el-form-item label="未支付的顾客订单" label-width="160px" prop="payTimeout" style="flex: 1">
                <el-input-number v-model="timeout.d" style="width: 60px" :min="0" :controls="false" />
                <text class="ml12 mr12">天</text>
                <el-input-number v-model="timeout.h" style="width: 60px" :min="0" :controls="false" />
                <text class="ml12 mr12">小时</text>
                <el-input-number v-model="timeout.m" style="width: 60px" :min="0" :controls="false" />
                <span class="ml12">分钟后，自动关闭</span>
            </el-form-item>
            <span class="ml42 msg__text">最长可设置30天，&nbsp;最短可设置15分钟</span>
        </div>
        <QPluginPurchaseOrderSetting ref="purchaseOrderSettingRef" />
        <!-- <div class="msg">
            <el-form-item label="未支付的采购订单" label-width="160px" prop="payTimeout" style="flex: 1">
                <el-input-number v-model="timeout.supplierD" style="width: 60px" :min="0" :controls="false" />
                <text class="ml12 mr12">天</text>
                <el-input-number v-model="timeout.supplierH" style="width: 60px" :min="0" :controls="false" />
                <text class="ml12 mr12">小时</text>
                <el-input-number v-model="timeout.supplierM" style="width: 60px" :min="0" :controls="false" />
                <span class="ml12">分钟后，自动关闭</span>
            </el-form-item>
            <span class="ml42 msg__text">最长可设置30天，&nbsp;最短可设置15分钟</span>
        </div> -->
        <div class="msg">
            <el-form-item label="已发货订单" label-width="160px" prop="confirmTimeout" style="flex: 1">
                <el-input-number v-model="timeout.confirmTimeout" style="width: 200px" :min="3" :max="30" :controls="false" />
                <span class="ml12">天后自动确认收货</span>
            </el-form-item>
            <span class="ml42 msg__text">请考虑物流运输时间，最长可设置30天</span>
        </div>
        <div class="msg">
            <el-form-item label="自动好评 已完成订单" label-width="160px" prop="commentTimeout" style="flex: 1">
                <el-input-number v-model="timeout.commentTimeout" style="width: 200px" :min="1" :max="30" :controls="false" />
                <span class="ml12">天后自动好评</span>
            </el-form-item>
            <span class="ml42 msg__text">时间范围：1~30天</span>
        </div>
        <div class="msg">
            <el-form-item label="已发货(积分商城)订单" label-width="160px" prop="integralTimeout" style="flex: 1">
                <el-input-number v-model="timeout.integralTimeout" style="width: 200px" :min="1" :max="30" :controls="false" />
                <span class="ml12">天后自动确认收货</span>
            </el-form-item>
            <span class="ml42 msg__text">请考虑物流运输时间，最长可设置30天</span>
        </div>
        <Line name="售后设置" color="#F57373" />
        <div class="msg">
            <el-form-item label="自动通过" label-width="160px" prop="afsAuditTimeout" style="flex: 1">
                <text class="ml12 mr12">申请</text>
                <el-input-number v-model="timeout.afsAuditTimeout" :min="0" style="width: 137px" /><span class="ml12"
                    >天后，商家未拒绝自动审核通过</span
                >
            </el-form-item>
            <span class="ml42 msg__text">默认：3 天，设置后以设置时间为准</span>
        </div>
        <div class="msg">
            <el-form-item label="自动通过" label-width="160px" prop="supplierAfsAuditTimeout" style="flex: 1">
                <text class="ml12 mr12">申请</text>
                <el-input-number v-model="timeout.supplierAfsAuditTimeout" :min="0" style="width: 137px" /><span class="ml12"
                    >天后，供应商未拒绝自动审核通过</span
                >
            </el-form-item>
            <span class="ml42 msg__text">默认：3 天，设置后以设置时间为准</span>
        </div>
        <Line name="商品审核" color="#F8C373" />
        <div class="msg">
            <el-form-item label="商品审核" label-width="160px" prop="goodsPass" style="flex: 1">
                <el-radio-group v-model="timeout.productAuditType">
                    <el-radio value="SYSTEM_AUDIT">自动通过</el-radio>
                    <el-radio value="MANUALLY_AUDIT">人工审核</el-radio>
                </el-radio-group>
            </el-form-item>
        </div>
        <Line name="商城配置" color="#409eff" />
        <div class="msg">
            <el-row>
                <el-col :span="24">
                    <el-form-item label="默认店铺" label-width="160px" prop="defaultStoreId">
                        <el-space class="selectLine" :size="30">
                            <div>
                                <el-avatar
                                    v-if="selectedStore[0]?.logo"
                                    style="vertical-align: middle; margin: 0 10px 0 0"
                                    :size="40"
                                    :src="selectedStore[0].logo"
                                />
                                <span v-if="selectedStore[0]?.name" class="selectLine__name">{{ selectedStore[0].name }}</span>
                            </div>
                            <el-button type="primary" @click="openSelectOpen()">{{ timeout.defaultStoreId ? '更换' : '选择店铺' }}</el-button>
                        </el-space>
                    </el-form-item>
                </el-col>
                <el-form-item label="随机注水量" label-width="160px" prop="randomInjection">
                    <el-col :span="10">
                        <el-input v-model="randomInjection.min" type="number" placeholder="最小值" style="width: 100%" />
                    </el-col>
                    <el-col class="text-center" :span="1" style="margin: 0 0.5rem">至</el-col>
                    <el-col :span="10">
                        <el-input v-model="randomInjection.max" placeholder="最大值" style="width: 100%" />
                    </el-col>
                </el-form-item>
                <el-form-item label="铺货销量展示" label-width="160px" prop="randomInjection.showSaleNum" style="flex: 1">
                    <el-radio-group v-model="randomInjection.showSaleNum">
                        <el-radio :value="false">关闭</el-radio>
                        <el-radio :value="true">开启</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-row>
        </div>
        <Line name="会员专享预告配置" color="#409eff" />
        <div class="msg">
            <el-row>
                <!-- <el-col :span="24">
                    <el-form-item label="设置参与活动" label-width="160px" prop="onlyItems" style="flex: 1">
                        <el-select
                            v-model="onlyPromotionPreConfig.onlyItems"
                            value-key="id"
                            placeholder="请选择活动名称（可多选）"
                            multiple
                            clearable
                        >
                            <el-option v-for="item in onlyPromotionActivities" :key="item.id" :label="item.onlyName" :value="item" />
                        </el-select>
                    </el-form-item>
                </el-col> -->
                <el-col :span="12">
                    <el-form-item label="提前展示时长设置" label-width="160px" prop="onlyPromotionPreConfig.open" style="flex: 1">
                        <el-input-number
                            v-model="onlyPromotionPreConfig.preTime"
                            :min="0"
                            :step="1"
                            :max="preMax"
                            :step-strictly="true"
                            :controls="false"
                        >
                            <template #suffix>小时</template>
                        </el-input-number>
                        <span class="ml12 msg__text">最长可设置{{ preMax }}小时</span>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="是否开启" label-width="100px" prop="onlyPromotionPreConfig.open" style="flex: 1">
                        <el-radio-group v-model="onlyPromotionPreConfig.open" style="width: 100%">
                            <el-radio :value="false">关闭</el-radio>
                            <el-radio :value="true">开启</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
            </el-row>
        </div>
        <el-form-item label-width="20px">
            <el-button type="primary" @click="submitBasicForm()">保存</el-button>
        </el-form-item>
    </el-form>
    <!-- 店铺选择弹框 -->
    <el-dialog v-model="dialogVisible" :title="`选择店铺`" append-to-body center destroy-on-close>
        <q-select-shop :selected-shop="selectedStore" :shop-max="1" @update:selected-shop="(val: ShopItem[]) => confirm(val)" />
    </el-dialog>
</template>
<style lang="scss" scoped>
@include b(msg) {
    @include flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    @include e(text) {
        height: 32px;
        width: 381px;
    }
}
@include b(ml42) {
    margin-left: 42px;
}
@include b(ml12) {
    margin-left: 12px;
}
@include b(ml56) {
    margin-left: 56px;
}
@include b(ml240) {
    margin-left: 240px;
}
@include b(colorRed) {
    color: #f57373;
}
@include b(mr12) {
    margin-right: 12px;
}
</style>
