import { doGetSupplierExamineGoods, doUpdateSupplierSellStatus } from '@/apis/good'
import auditGoodsVue from '../components/audit-goods.vue'
import { ElMessage } from 'element-plus'

const useExamineListHooks = () => {
    let cacheCurrentCheckRows: any[] = [] // 用于暂存当前选定的行信息
    const searchParams = reactive({
        name: '',
        platformCategoryId: '',
        secondPlatformCategoryId: '',
        productType: '',
        shopId: '',
    })
    const currentTab = ref<'' | 'ALREADY_PASSED' | 'UNDER_REVIEW' | 'REFUSE'>('')
    const tableHeight = ref('calc(100vh - 380px)')
    const selectItems = ref<any[]>([])
    const showAuditDialog = ref(false)
    const auditOneMoreGoodsRefs = ref<InstanceType<typeof auditGoodsVue> | null>(null)

    const initList = async () => {
        let goodsList = [],
            total = 0
        try {
            const result = await doGetSupplierExamineGoods({ ...searchParams, ...tableList.page, productAuditStatus: currentTab.value })
            if (result.code === 200) {
                goodsList = result.data.records
                total = result.data.total
            }
        } finally {
            tableList.goods = goodsList
            tableList.total = total
        }
    }

    const salePriceRange = computed(() => (salePrices: string[] = []) => {
        const min = Math.min(...salePrices.map((item) => parseInt(item))) / 10000
        const max = Math.max(...salePrices.map((item) => parseInt(item))) / 10000
        if (max === min) {
            return max
        } else {
            return `${min}-${max}`
        }
    })
    const getSearch = (e: typeof searchParams) => {
        Object.keys(searchParams).forEach((key) => (searchParams[key] = e[key]))
        initList()
    }
    const goodsStatus = {
        全部: '',
        待审核: 'UNDER_REVIEW',
        已通过: 'ALREADY_PASSED',
        已拒绝: 'REFUSE',
    }

    const handleTabClick = () => {
        tableList.page.current = 1
        initList()
    }

    const handleSearchShow = (e: boolean) => {
        if (e) {
            tableHeight.value = 'calc(100vh - 520px)'
        } else {
            tableHeight.value = 'calc(100vh - 380px)'
        }
    }
    const tableList = reactive({
        page: { size: 10, current: 1 },
        goods: [],
        total: 0,
    })
    const handleAuditGoods = (goodsList: any[]) => {
        if (goodsList.length === 0) {
            return ElMessage.error({ message: '请选择需要审核的商品信息' })
        }
        cacheCurrentCheckRows = goodsList
        showAuditDialog.value = true
    }
    const handleConfirmGoodsAudit = async () => {
        const rs: any = await auditOneMoreGoodsRefs.value?.validateForm()
        const { code, msg } = await doUpdateSupplierSellStatus(
            {
                explain: rs?.explain,
                productIds: cacheCurrentCheckRows?.map((item: any) => item.id),
            },
            rs?.status,
        )
        if (code === 200) {
            ElMessage.success({ message: msg || '更新状态成功' })
            showAuditDialog.value = false
            initList()
        } else {
            ElMessage.error({ message: msg || '更新状态失败' })
        }
    }
    const handleCloseAuditDialog = () => {
        cacheCurrentCheckRows = []
    }
    const handleCurrentChange = (currentPage: number) => {
        tableList.page.current = currentPage
        initList()
    }
    const handleSizeChange = (size: number) => {
        tableList.page.size = size
        tableList.page.current = 1
        initList()
    }
    const reasonRefusalDialog = ref(false)
    const reason = ref('')
    // 拒绝原因
    const reasonRefusal = (row: any) => {
        reasonRefusalDialog.value = !reasonRefusalDialog.value
        reason.value = row?.explain
    }
    initList()
    return {
        tableHeight,
        getSearch,
        handleSearchShow,
        currentTab,
        goodsStatus,
        handleTabClick,
        tableList,
        salePriceRange,
        initList,
        selectItems,
        handleAuditGoods,
        showAuditDialog,
        auditOneMoreGoodsRefs,
        handleCloseAuditDialog,
        handleConfirmGoodsAudit,
        handleCurrentChange,
        handleSizeChange,
        reasonRefusal,
        reasonRefusalDialog,
        reason,
    }
}

export default useExamineListHooks
