@include b(tree-container) {
    // height: calc(100vh - 240px);
    height: calc(100vh - 230px);
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
        display: none;
    }
}
@include b(cate) {
    color: #858585;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-size: 14px;
    font-weight: bold;
    @include e(img) {
        display: flex;
        justify-content: flex-end;
        align-items: center;
    }
}
@include b(borderBox) {
    @include e(first) {
        background-color: #f2f2f6;
        // padding: 0px 10px;
        display: flex;
        justify-content: space-between;
        @include m(deal) {
            margin-right: 10px;
            display: flex;
            justify-content: space-between;
            width: 200px;
            cursor: pointer;
        }
    }
    @include e(child) {
        padding: 15px 10px 15px 20px;
        // border-bottom: 1px solid #f2f2f2;
        display: flex;
        justify-content: space-between;
        align-items: center;
        @include m(left) {
            display: flex;
            justify-content: space-between;
            width: 350px;
            padding-left: 10px;
            align-items: center;
        }
        @include m(info) {
            display: flex;
            align-items: center;
        }
        @include m(right) {
            display: flex;
            justify-content: space-between;
            width: 88px;
            cursor: pointer;
        }
    }
    v-deep .el-icon-arrow-right:before {
        content: "\e791";
    }
    v-deep .el-collapse-item__content {
        padding-bottom: 0;
    }
    v-deep .el-collapse-item__header {
        background-color: #f2f2f6;
        padding-left: 10px;
    }
}
@include b(dialog) {
    width: 560px;
    border: 1px solid #bababa;
    @include e(header) {
        @include flex(space-between);
        padding: 0 5px;
        height: 30px;
        background: #bababa;
        font-size: 14px;
        color: #6a6a6a;
        @include m(name) {
            display: inline-block;
            width: 120px;
        }
        @include m(category) {
            display: inline-block;
            width: 140px;
        }
        @include m(percent) {
            display: inline-block;
            width: 100px;
        }
        @include m(rate) {
            display: inline-block;
            width: 140px;
        }
        @include m(img) {
            display: inline-block;
            width: 100px;
        }
        label {
            text-align: center;
            width: 80px;
        }
    }
    @include e(content) {
        @include flex(space-between);
        padding: 0 5px;
        height: 60px;
        gap: 10px;
        @include m(tool) {
            width: 80px;
            @include flex(space-around);
        }
    }
}
@include b(add-btn) {
    color: #2e99f3;
    cursor: pointer;
    &:hover {
        color: #128cf0;
    }
}
@include b(del-btn) {
    color: #ff7417;
    cursor: pointer;
    &:hover {
        color: #ff7417;
    }
}
.borderBox__child:hover {
    background-color: #f5f5f5;
    cursor: move;
}

.emptyLine {
    width: 100%;
    height: 80px;
    background-color: white;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    font-size: 14px;
    color: #b3b3b3;
    border-bottom: 1px solid #ebeef5;
    border-top: 1px solid #ebeef5;
}
.dialog__content {
    height: auto;
    .avatar-uploader {
        margin: 0 30px 0 20px;
        border: 1px dotted #ebeef5;
        border-radius: 3px;
        position: relative;
    }
    .avatar-uploader .avatar {
        width: 88px;
        height: 88px;
        display: block;
    }
    .icon {
        content: "\e63b";
        width: 20px;
        height: 20px;
        position: absolute;
        display: block;
        right: -10px;
        top: -10px;
        z-index: 10000;
        color: red;
    }
    .el-icon.avatar-uploader-icon {
        // display: none;
        width: 88px !important;
        height: 88px !important;
        font-size: 12px !important;
    }
}
@include b(selectMaterialStyle) {
    @include flex(space-between, center);
    position: relative;
    cursor: pointer;
    margin: 4px 10px 4px 12px;
    width: 100px;
    height: 100px;
    padding: 11px;
    .avatar {
        object-fit: contain;
        width: 84px !important;
        height: 84px !important;
        border-radius: 5px;
        overflow: hidden;
        border: 1px dashed #ccc;
    }

    @include e(span) {
        color: #999;
        font-size: 20px;
    }
}

.del-icon {
    position: absolute;
    color: red;
    right: 0px;
    top: 0px;
}
