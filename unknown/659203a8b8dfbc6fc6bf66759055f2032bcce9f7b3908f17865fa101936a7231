<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-12-05 10:33:57
 * @LastEditors: lexy
 * @LastEditTime: 2022-12-13 14:46:51
-->
<script setup lang="ts">
import { useVModel } from '@vueuse/core'
import defaultOnlyPromotionData from './onlyPromotion'
import type { PropType } from 'vue'
import selectMaterial from '@/views/material/selectMaterial.vue'
import iconfont from '@/assets/css/font/iconfont.json'
/*
 *variable
 */
const $props = defineProps({
    formData: {
        type: Object as PropType<typeof defaultOnlyPromotionData>,
        default: defaultOnlyPromotionData,
    },
})
const $emit = defineEmits(['update:formData'])
const subForm = useVModel($props, 'formData', $emit)
const rules = ref({
    titleStyle: {
        text: [{ required: true, message: '请输入标题' }],
    },
    headerRight: {
        text: [{ required: true, message: '请输入右部文本' }],
    },
})
// 图片上传弹窗
const dialogVisible = ref(false)
/*
 *lifeCircle
 */
/*
 *function
 */
const handleResetColor = (type: string) => {
    if (type === 'start') {
        subForm.value.bgStyle.startColor = '#fff'
    } else if (type === 'end') {
        subForm.value.bgStyle.endColor = '#fff'
    }
}

/**
 * 获取图标类名
 * @param clazz 图标类名
 * @returns 图标类名
 */
const getIconClass = (clazz: string) => {
    return 'iconfont ' + iconfont.css_prefix_text + clazz
}
// 右部图标选择
const iconChange = (val: string) => {
    subForm.value.headerRight.iconUrl = val
}

/**
 * 图片上传
 */
const paramId = ref('')
const buttonlFn = (id: string) => {
    paramId.value = id
    dialogVisible.value = true
}
// @cropped-file-change="" 裁剪后返回的单个素材
// @checked-file-lists=""  选中素材返回的素材合集
const croppedFileChange = (val: string) => {
    subForm.value[paramId.value].url = val
}
const checkedFileLists = (val: string[]) => {
    subForm.value[paramId.value].url = val?.shift() || ''
}

const selectMaterialFn = (val: boolean) => {
    dialogVisible.value = val
    !val && (paramId.value = '')
}
</script>

<template>
    <div>
        <el-form :model="subForm" label-width="80px" :rules="rules">
            <el-card style="max-width: 480px; margin-bottom: 20px">
                <template #header>头部设置</template>
                <el-form-item label="显示头部">
                    <el-radio-group v-model="subForm.showHeader">
                        <el-radio :value="1">显示</el-radio>
                        <el-radio :value="0">隐藏</el-radio>
                    </el-radio-group>
                </el-form-item>
                <template v-if="subForm.showHeader === 1">
                    <el-form-item label="选择风格">
                        <el-radio-group v-model="subForm.bgStyle.type">
                            <el-radio :value="1">背景色</el-radio>
                            <el-radio :value="2">背景图</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item v-if="subForm.bgStyle.type === 1" label="背景色">
                        <el-row :gutter="[20, 10]" style="margin-bottom: 10px">
                            <el-col :span="6" align="center">
                                <el-color-picker v-model="subForm.bgStyle.startColor" />
                            </el-col>
                            <el-col :span="14" align="center">
                                <el-input v-model="subForm.bgStyle.startColor" />
                            </el-col>
                            <el-col :span="4" align="center">
                                <el-button type="text" @click="handleResetColor('start')">重置</el-button>
                            </el-col>
                        </el-row>
                        <el-row :gutter="[20, 10]" style="margin-bottom: 10px">
                            <el-col :span="6" align="center">
                                <el-color-picker v-model="subForm.bgStyle.endColor" />
                            </el-col>
                            <el-col :span="14" align="center">
                                <el-input v-model="subForm.bgStyle.endColor" />
                            </el-col>
                            <el-col :span="4" align="center">
                                <el-button type="text" @click="handleResetColor('end')">重置</el-button>
                            </el-col>
                        </el-row>
                        <el-row :gutter="[20, 10]">
                            <el-col :span="6" align="center"> 角度 </el-col>
                            <el-col :span="14" align="center">
                                <el-input-number v-model="subForm.bgStyle.deg" :min="0" :max="360" :controls="false" placeholder="渐变角度0～360度" />
                            </el-col>
                        </el-row>
                    </el-form-item>
                    <el-form-item v-if="subForm.bgStyle.type === 2" label="图片上传">
                        <div v-if="!subForm.bgStyle.url" class="selectMaterialStyle" @click="buttonlFn('bgStyle')">
                            <span class="selectMaterialStyle__span">+</span>
                        </div>
                        <img v-else alt="" class="selectMaterialStyle" :src="subForm.bgStyle.url" @click="buttonlFn('bgStyle')" />
                    </el-form-item>
                    <el-form-item label="标题类型">
                        <el-radio-group v-model="subForm.titleStyle.type">
                            <el-radio :value="1">图片</el-radio>
                            <el-radio :value="2">文字</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item v-if="subForm.titleStyle.type === 1" label="图片" prop="titleStyle.url">
                        <div v-if="!subForm.titleStyle.url" class="selectMaterialStyle" @click="buttonlFn('titleStyle')">
                            <span class="selectMaterialStyle__span">+</span>
                        </div>
                        <img v-else alt="" class="selectMaterialStyle" :src="subForm.titleStyle.url" @click="buttonlFn('titleStyle')" />
                    </el-form-item>
                    <el-form-item v-if="subForm.titleStyle.type === 2" label="文字" prop="titleStyle.text">
                        <el-input v-model="subForm.titleStyle.text" :minlength="1" :maxlength="6" :show-word-limit="true" />
                    </el-form-item>
                    <el-form-item label="右部文本" prop="headerRight.text">
                        <el-input v-model="subForm.headerRight.text" :minlength="1" :maxlength="4" :show-word-limit="true" />
                    </el-form-item>
                    <el-form-item label="右部图标" prop="headerRight.iconUrl">
                        <el-select
                            v-model="subForm.headerRight.iconUrl"
                            clearable
                            filterable
                            fit-input-width
                            placeholder="请选择图标"
                            style="width: 100%"
                            @change="iconChange"
                        >
                            <template #prefix>
                                <i :class="subForm.headerRight.iconUrl" />
                            </template>
                            <el-option
                                v-for="item in iconfont.glyphs"
                                :key="getIconClass(item.font_class)"
                                :label="item.name"
                                :value="getIconClass(item.font_class)"
                            >
                                <div>
                                    <i :class="getIconClass(item.font_class)" />
                                    <span>{{ item.name }}</span>
                                </div>
                            </el-option>
                        </el-select>
                    </el-form-item>
                </template>
            </el-card>
            <el-card style="max-width: 480px; margin-bottom: 20px">
                <template #header>商品列表</template>
                <el-form-item label="显示分类">
                    <el-radio-group v-model="subForm.showCategory">
                        <el-radio :value="1">显示</el-radio>
                        <el-radio :value="0">隐藏</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="选择风格">
                    <el-radio-group v-model="subForm.listStyle">
                        <el-radio :value="1">单列展示</el-radio>
                        <el-radio :value="2">两列展示(纵向)</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-card>
            <el-card style="max-width: 480px; margin-bottom: 20px">
                <template #header>商品设置</template>
                <el-form-item label="商品数量">
                    <el-slider
                        v-model="subForm.goodNum"
                        :show-tooltip="false"
                        :show-input="true"
                        :max="10"
                        :show-input-controls="false"
                        size="small"
                    />
                </el-form-item>
                <el-form-item label="展示信息">
                    <el-checkbox-group v-model="subForm.showInfo">
                        <el-checkbox :value="1">商品名称</el-checkbox>
                        <el-checkbox :value="2">抢购进度</el-checkbox>
                        <el-checkbox v-if="subForm.listStyle === 1" :value="6">剩余库存</el-checkbox>
                        <el-checkbox :value="3">商品价格</el-checkbox>
                        <el-checkbox v-if="subForm.showInfo.includes(3)" :value="4">商品原价</el-checkbox>
                        <el-checkbox :value="5">截止时间</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="抢购按钮">
                    <el-radio-group v-model="subForm.showBtn">
                        <el-radio :value="1">显示</el-radio>
                        <el-radio :value="0">隐藏</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-card>
        </el-form>
        <!-- 选择素材 e -->
        <selectMaterial
            :dialog-visible="dialogVisible"
            :upload-files="1"
            @select-material-fn="selectMaterialFn"
            @cropped-file-change="croppedFileChange"
            @checked-file-lists="checkedFileLists"
        />
        <!-- 选择素材 d -->
    </div>
</template>

<style lang="scss" scoped>
@include b(choosedGood) {
    width: 285px;
    padding: 18px;
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    border: 1px dashed #f2f2f2;
    @include e(item) {
        width: 72px;
        height: 72px;
        border: 1px solid #d5d5d5;
        margin-right: 10px;
        line-height: 70px;
        text-align: center;
        font-size: 20px;
        color: #000;
        cursor: pointer;
    }
}
::v-deep(.el-input-number--small) {
    width: 80px;
}

@include b(selectMaterialStyle) {
    width: 60px;
    height: 60px;
    border-radius: 5px;
    overflow: hidden;
    border: 1px dashed #ccc;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    @include e(span) {
        color: #a7a7a7;
        font-size: 14px;
        line-height: 24px;
    }
}
</style>
