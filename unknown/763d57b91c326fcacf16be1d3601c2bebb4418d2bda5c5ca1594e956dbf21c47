<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-07-25 10:57:36
 * @LastEditors: lexy
 * @LastEditTime: 2023-11-04 15:43:08
-->
<script setup lang="ts">
import { ref, reactive, inject } from 'vue'
import { ElMessage } from 'element-plus'
import { Fn, useVModel } from '@vueuse/core'
// @ts-ignore
import { regionData } from 'element-china-area-data'
import { AddressFn } from '@/components/q-address'
import QAddress from '@/components/q-address/q-address.vue'
import { dogetOrderNotDetailsByOrderNo, doPutOrderDetailsByOrderNo, importNeedDeliveryOrders } from '@/apis/order'
import { doGetLogisticsList } from '@/apis/set/platformDelivery'
import { doLogisticsSetDef, doGetLogisticsCompany, doGetLogisticsExpressUsableList } from '@/apis/freight/freightAdd'
import type { Api<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON>, <PERSON>msD<PERSON><PERSON> } from '@/views/order/orderShipment/types'
import type { FormInstance, FormRules, TableInstance } from 'element-plus'
import { DISTRIBUTION } from '../../types/order'
type DeviceList = {
    deviceNo: string
    id: string
    printName: string
}
type ApiEnumLogisticCompany = 'id' | 'logisticsCompanyCode' | 'logisticsCompanyName' | 'logisticsCompanyStatus'
export type ApiLogisticCompany = Record<ApiEnumLogisticCompany, string>
interface ApiAddressItem {
    address: string
    cityCode: string
    contactName: string
    contactPhone: string
    defReceive: string
    defSend: string
    deleted: false
    id: string
    provinceCode: string
    regionCode: string
    shopId: string
    defSelfSupplier?: 'YES' | 'NO'
    defSelfShop?: 'YES' | 'NO'
}

/*
 *variable
 */
const $props = defineProps({
    currentNo: {
        type: String,
        required: true,
    },
    isShow: { type: Boolean, required: true },
    currentShopOrderNo: { type: String, required: true },
})
const $emit = defineEmits(['update:isShow'])
const { divTenThousand } = useConvert()
const _isShow = useVModel($props, 'isShow', $emit)
const tableRef = ref<TableInstance>()
const currentShopType = ref<'shopOrders' | 'supplierOrders'>('shopOrders')
// 校验实例
const ruleFormRef = ref<FormInstance>()
// 校验
const rules = reactive<FormRules>({
    address: [],
    company: [
        {
            required: true,
            message: '请选择物流公司',
            trigger: 'change',
        },
    ],
    expressNo: [
        { required: true, message: '请填写物流单号', trigger: 'blur' },
        { min: 8, max: 20, message: '请填写正确的物流单号', trigger: 'blur' },
    ],
})
// 发货类型枚举
const deliverType = [
    { key: 'EXPRESS', value: '手动发货' },
    { key: 'PRINT_EXPRESS', value: '打印快递单并发货' },
    // { key: 'WITHOUT', value: '无需物流发货' },
]
const deliverDialogFormData = reactive({
    deliverType: 'EXPRESS',
    receiver: { name: '', mobile: '', address: '' },
    expressCompany: '',
    expressCode: '',
    addressaddress: '',
    expressNo: '',
})
// 未发货列表
const deliverList = ref<ApiNotDelivey[]>([])
// 发货地址数据
const deliveryAddressData = ref<ApiAddressItem[]>([])
// 地区信息
const orderReceiver = ref<ApiOrderReceiver>()
//物流公司列表
const companySelectList = ref<ApiLogisticCompany[]>([])
//打印机列表
const ParentListFn = inject('reloadParentListFn') as Fn
const currentOrderDispatcher = reactive({
    shopOrders: {
        orderReceiver: {} as ApiOrderReceiver,
        deliverList: [] as ApiNotDelivey[],
        deliverDialogFormData: {
            deliverType: 'EXPRESS',
            receiver: { name: '', mobile: '', address: '' },
            expressCompany: '',
            expressCode: '',
            addressaddress: '',
            expressNo: '',
        },
    },
    supplierOrders: {
        orderReceiver: {} as ApiOrderReceiver,
        deliverList: [] as ApiNotDelivey[],
        deliverDialogFormData: {
            deliverType: 'EXPRESS',
            receiver: { name: '', mobile: '', address: '' },
            expressCompany: '',
            expressCode: '',
            addressaddress: '',
            expressNo: '',
        },
    },
})
const distributionMode = ref<keyof typeof DISTRIBUTION>('EXPRESS')
const platform = ref('')
/*
 *lifeCircle
 */
initNotDeliverList()
initLogisticsList()
initLogisticsCompany()
/*
 *function
 */
/**
 * @LastEditors: lexy
 * @description: 获取未发货订单
 * @returns {*}
 */
async function initNotDeliverList() {
    if (!$props.currentNo || !$props.currentShopOrderNo) return ElMessage.error('未填写订单号')
    const { data } = await importNeedDeliveryOrders([$props.currentNo])
    if (data?.shopOrders?.length) {
        const { orderReceiver: Receiver, shopOrders, extra: distributeData } = data.shopOrders[0]
        const shopOrderItems = shopOrders?.[0]?.shopOrderItems || []
        currentOrderDispatcher.shopOrders.orderReceiver = Receiver
        for (const key in currentOrderDispatcher.shopOrders.deliverDialogFormData.receiver) {
            currentOrderDispatcher.shopOrders.deliverDialogFormData.receiver[key as keyof typeof deliverDialogFormData.receiver] = Receiver[key]
        }
        // 收货人地址信息拼接
        currentOrderDispatcher.shopOrders.deliverDialogFormData.receiver.address = `${Receiver.address}`
        // for 记录发货数量初始值
        for (let i = 0; i < shopOrderItems.length; i++) {
            shopOrderItems[i].deliveryNum = shopOrderItems[i].num
        }
        currentOrderDispatcher.shopOrders.deliverList = shopOrderItems
        if (!shopOrderItems?.length) {
            currentShopType.value = 'supplierOrders'
        }
        distributionMode.value = distributeData?.distributionMode || 'EXPRESS'
        platform.value = distributeData?.platform || ''
        deliverDialogFormData.deliverType = distributionMode.value === 'VIRTUAL' ? 'WITHOUT' : 'EXPRESS'
    } else {
        currentShopType.value = 'supplierOrders'
    }
    if (data?.supplierOrders?.length) {
        const { orderReceiver: Receiver, shopOrders, extra: distributeData } = data.supplierOrders[0]
        const shopOrderItems = shopOrders?.[0]?.shopOrderItems || []
        currentOrderDispatcher.supplierOrders.orderReceiver = Receiver
        for (const key in currentOrderDispatcher.supplierOrders.deliverDialogFormData.receiver) {
            currentOrderDispatcher.supplierOrders.deliverDialogFormData.receiver[key as keyof typeof deliverDialogFormData.receiver] = Receiver[key]
        }
        // 收货人地址信息拼接
        currentOrderDispatcher.supplierOrders.deliverDialogFormData.receiver.address = `${Receiver.address}`
        // for 记录发货数量初始值
        for (let i = 0; i < shopOrderItems.length; i++) {
            shopOrderItems[i].deliveryNum = shopOrderItems[i].num
        }
        currentOrderDispatcher.supplierOrders.deliverList = shopOrderItems
        distributionMode.value = distributeData?.distributionMode || 'EXPRESS'
        platform.value = distributeData?.platform || ''
        deliverDialogFormData.deliverType = distributionMode.value === 'VIRTUAL' ? 'WITHOUT' : 'EXPRESS'
    }
    // return
    // const {
    //     data: { orderReceiver: Receiver, shopOrderItems },
    //     code,
    // } = await dogetOrderNotDetailsByOrderNo($props.currentNo, $props.currentShopOrderNo)
    // if (code !== 200) return ElMessage.error('获取未发货订单失败')
    // orderReceiver.value = Receiver
    // for (const key in deliverDialogFormData.receiver) {
    //     deliverDialogFormData.receiver[key as keyof typeof deliverDialogFormData.receiver] = Receiver[key]
    // }
    // // 收货人地址信息拼接
    // deliverDialogFormData.receiver.address = `${Receiver.address}`
    // // for 记录发货数量初始值
    // for (let i = 0; i < shopOrderItems.length; i++) {
    //     shopOrderItems[i].deliveryNum = shopOrderItems[i].num
    // }
    // deliverList.value = shopOrderItems
    tableRef.value?.toggleAllSelection()
}
/**
 * @LastEditors: lexy
 * @description: 获取可用物流服务
 * @returns {*}
 */
async function initLogisticsCompany() {
    const { code, data } = await doGetLogisticsExpressUsableList({ size: 1000, current: 1 })
    code !== 200 ? ElMessage.error('获取物流服务列表失败') : (companySelectList.value = data.records)
}
/**
 * @LastEditors: lexy
 * @description: 获取发货物流地址列表
 * @returns {*}
 */
async function initLogisticsList() {
    const { data, code } = await doGetLogisticsList({})
    code !== 200
        ? ElMessage({ message: '请刷新重试...', type: 'warning' })
        : (deliveryAddressData.value = data.records?.filter((item) => item.defSend === 'YES'))
    console.log('deliveryAddressData.value', deliveryAddressData.value)
    const sender = deliveryAddressData.value.find((item) => item.defSend === 'YES')
    if (!sender) {
        return ElMessage.error('请设置物流')
    }
    deliverDialogFormData.addressaddress = sender.id
}
/**
 * @LastEditors: lexy
 * @description: 设为默认地址
 * @returns {*}
 */
const handleDefultAddress = async () => {
    const { code } = await doLogisticsSetDef(deliverDialogFormData.addressaddress, 'DEF_SEND')
    code === 200 ? ElMessage.success('设置成功') : ElMessage.error({ message: '设置失败' })
}

/**
 * @LastEditors: lexy
 * @description: 发货请求
 * @returns {*}
 */
const handleeSubmit = async () => {
    const orderList = tableRef.value?.getSelectionRows()
    if (!orderList.length) return ElMessage.error({ message: '请选择商品' })
    try {
        if (!ruleFormRef.value) {
            return
        }
        await ruleFormRef.value.validate()
        const params: ParamsDeliver = {
            deliverType: '',
            receiver: { name: '', mobile: '', address: '' },
            sender: { name: '', mobile: '', address: '' },
            cargo: '',
            remark: '',
            dropDeliver: true,
            expressCompany: {
                expressCompanyName: '',
                expressCompanyCode: '',
                expressNo: '',
            },
            items: [],
            shopId: '',
            selfShopType: currentShopType.value === 'shopOrders' ? 'SELF_SHOP' : 'SELF_SUPPLIER',
        }
        let { deliverType, receiver, expressCompany, expressNo } = currentOrderDispatcher[currentShopType.value].deliverDialogFormData
        deliverType = distributionMode.value === 'VIRTUAL' ? 'WITHOUT' : 'EXPRESS'
        params.deliverType = deliverType
        params.receiver = receiver
        params.expressCompany = null
        console.log(distributionMode.value, '')
        if (deliverType !== 'WITHOUT') {
            console.log('expressCompany', expressCompany)
            params.expressCompany = { expressCompanyName: '', expressCompanyCode: '', expressNo: '' }
            params.expressCompany.expressCompanyCode = expressCompany
            const company = companySelectList.value.find((item) => item.logisticsCompanyCode === expressCompany)
            if (company) {
                params.expressCompany.expressCompanyName = company.logisticsCompanyName
            } else {
                return Promise.reject('获取物流公司失败')
            }
            params.expressCompany.expressNo = expressNo
        }
        params.items = orderList.map((item: ApiNotDelivey) => ({ itemId: item.id, num: item.deliveryNum }))
        if (params.deliverType === 'PRINT_EXPRESS') {
            //  打印面单发货携带发货人信息
            getSenderAddress(params)
        }
        params.shopId = orderList?.[0]?.shopId
        const { code, msg } = await doPutOrderDetailsByOrderNo($props.currentNo, params)
        if (code !== 200) return ElMessage.error(msg || '发货失败')
        _isShow.value = false
        ParentListFn()
        ElMessage.success('发货成功')
    } catch (error) {
        console.dir(error)
    }
}
/**
 * @LastEditors: lexy
 * @description: 获取发货地址 联系人...
 * @returns {*}
 */
function getSenderAddress(params: ParamsDeliver) {
    const sender = deliveryAddressData.value.find((item) => item.id === deliverDialogFormData.addressaddress)
    if (!sender) return ElMessage.error({ message: '未获取到发货信息' })
    const { address, contactName, contactPhone } = sender
    const addressArr = [sender.provinceCode, sender.cityCode, sender.regionCode]
    params.sender.address = `${AddressFn(regionData, addressArr)}${address}`
    params.sender.mobile = contactPhone
    params.sender.name = contactName
}
const handleClose = () => {
    _isShow.value = false
}
const handleChangeShopType = () => {
    tableRef.value?.toggleAllSelection()
    currentOrderDispatcher[currentShopType.value].deliverDialogFormData.addressaddress = ''
    if (deliveryAddressDataComputed.value.length === 1) {
        currentOrderDispatcher[currentShopType.value].deliverDialogFormData.addressaddress = deliveryAddressDataComputed.value?.[0]?.id
    }
}
const handleChangeDeliveryType = (val: string) => {
    if (val === 'PRINT_EXPRESS') {
        if (deliveryAddressDataComputed.value.length === 1) {
            currentOrderDispatcher[currentShopType.value].deliverDialogFormData.addressaddress = deliveryAddressDataComputed.value?.[0]?.id
        }
    }
}
const deliveryAddressDataComputed = computed(() => {
    return deliveryAddressData.value.filter((item) =>
        currentShopType.value === 'shopOrders' ? item.defSelfShop === 'YES' : item.defSelfSupplier === 'YES',
    )
})

const isNotOpenOrder = computed(() => platform.value === 'WECHAT_MINI_APP' && ['VIRTUAL', 'INTRA_CITY_DISTRIBUTION'].includes(distributionMode.value))
</script>

<template>
    <el-form ref="ruleFormRef" :model="currentOrderDispatcher?.[currentShopType]?.deliverDialogFormData" class="notShipment" :rules="rules">
        <el-table
            ref="tableRef"
            empty-text="暂无数据~"
            :data="currentOrderDispatcher?.[currentShopType]?.deliverList"
            max-height="250"
            style="width: 100%; margin-bottom: 20px"
            :header-row-style="() => ({ fontSize: '14px', color: '#333333', fontWeight: 'bold' })"
        >
            <el-table-column v-if="!isNotOpenOrder" type="selection" width="55" />
            <el-table-column label="商品信息">
                <template #default="{ row }">
                    <el-row>
                        <el-avatar style="width: 68px; height: 68px" shape="square" size="large" :src="row.image" />
                        <div style="width: 200px; padding-left: 10px">
                            <div>
                                <div class="notShipment__show">{{ row.productName }}</div>
                            </div>
                            <div>{{ row.specs?.join(',') }}</div>
                            <el-row style="width: 100px" justify="space-between" align="middle" class="money_text"
                                >￥{{ divTenThousand(row.dealPrice).mul(row.num) }}
                            </el-row>
                        </div>
                    </el-row>
                </template>
            </el-table-column>
            <el-table-column label="数量" width="100">
                <template #default="{ row }">
                    <div>*{{ row.num }}</div>
                </template>
            </el-table-column>
            <el-table-column label="发货数量" width="100">
                <template #default="{ row }">
                    <el-input-number
                        v-model="row.deliveryNum"
                        style="width: 100%"
                        :controls="false"
                        placeholder=""
                        :min="1"
                        :max="row.num"
                        :disabled="isNotOpenOrder"
                    />
                </template>
            </el-table-column>
        </el-table>
        <el-form-item label-width="90px">
            <template #label>
                <div class="send">订单号</div>
            </template>
            <div class="send">{{ $props.currentNo }}</div>
        </el-form-item>
        <el-form-item label-width="90px">
            <template #label>
                <div class="send">创建时间</div>
            </template>
            <div class="send">{{ currentOrderDispatcher?.[currentShopType]?.deliverList[0]?.createTime }}</div>
        </el-form-item>
        <el-form-item v-if="deliverDialogFormData.deliverType !== 'WITHOUT'" label-width="90px">
            <template #label>
                <div class="send">收货地址</div>
            </template>
            <div v-if="currentOrderDispatcher?.[currentShopType]?.orderReceiver">
                {{ currentOrderDispatcher?.[currentShopType]?.orderReceiver.name }},{{
                    currentOrderDispatcher?.[currentShopType]?.orderReceiver.mobile
                }}
                <!-- <q-address :address="['330000', '330100', '330122']" /> -->
                {{ currentOrderDispatcher?.[currentShopType]?.deliverDialogFormData.receiver.address }}
            </div>
            <div v-else>暂无地址信息</div>
        </el-form-item>
        <el-form-item label-width="90px" label="商家类型">
            <el-radio-group v-model="currentShopType" class="ml-4" size="large" @change="handleChangeShopType">
                <el-radio v-if="currentOrderDispatcher.shopOrders.deliverList?.length" label="shopOrders" size="large">自营店铺</el-radio>
                <el-radio v-if="currentOrderDispatcher.supplierOrders.deliverList?.length" label="supplierOrders" size="large">自营供应商</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item label-width="90px">
            <template #label>
                <div>发货方式</div>
            </template>
            <template v-if="distributionMode === 'EXPRESS'">
                <el-radio-group
                    v-model="currentOrderDispatcher[currentShopType].deliverDialogFormData.deliverType"
                    class="ml-4"
                    size="large"
                    @change="handleChangeDeliveryType"
                >
                    <el-radio v-for="item in deliverType" :key="item.key" :label="item.key" size="large">{{ item.value }}</el-radio>
                </el-radio-group>
            </template>
            <template v-else-if="distributionMode === 'INTRA_CITY_DISTRIBUTION'">
                <span style="color: #f00">由【达达快送】提供同城配送服务</span>
            </template>
            <template v-else>
                <span style="color: #f00">您可通过网络形式(如:聊天工具将商品发给用户)发货</span>
            </template>
        </el-form-item>
        <!-- <el-form-item v-if="currentOrderDispatcher?.[currentShopType]?.deliverDialogFormData.deliverType !== 'WITHOUT'" label-width="90px" prop=""> -->
        <el-form-item v-if="deliverDialogFormData.deliverType !== 'WITHOUT'" label-width="90px" prop="">
            <template #label>
                <div class="send">物流服务</div>
            </template>
            <el-row style="width: 100%">
                <el-col :span="20">
                    <el-select
                        v-model="currentOrderDispatcher[currentShopType].deliverDialogFormData.expressCompany"
                        class="m-2"
                        placeholder="请选择"
                        style="width: 100%; height: 30px"
                    >
                        <el-option
                            v-for="item in companySelectList"
                            :key="item.logisticsCompanyName"
                            :label="item.logisticsCompanyName"
                            :value="item.logisticsCompanyCode"
                        />
                    </el-select>
                </el-col>
            </el-row>
        </el-form-item>
        <!-- <el-form-item v-if="deliverDialogFormData.deliverType !== 'WITHOUT'" label-width="90px" prop="">
            <template #label>
                <div class="send">选择打印机</div>
            </template>
            <el-row style="width: 100%">
                <el-col :span="20">
                    <el-select v-model="deliverDialogFormData.expressCode" class="m-2" placeholder="选择打印机" style="width: 100%; height: 30px">
                        <el-option v-for="item in printerList" :key="item.id" :label="item.printName" :value="item.id" />
                    </el-select>
                </el-col>
                
            </el-row>
        </el-form-item> -->
        <!-- <el-form-item
            v-if="currentOrderDispatcher?.[currentShopType]?.deliverDialogFormData.deliverType === 'EXPRESS'"
            label-width="90px"
            prop="expressNo"
        > -->
        <el-form-item
            v-if="distributionMode === 'EXPRESS' && currentOrderDispatcher[currentShopType].deliverDialogFormData.deliverType === 'EXPRESS'"
            label-width="90px"
            prop="expressNo"
        >
            <template #label>
                <div>物流单号</div>
            </template>
            <el-row style="width: 100%">
                <el-col :span="20">
                    <el-input
                        v-model="currentOrderDispatcher[currentShopType].deliverDialogFormData.expressNo"
                        placeholder=""
                        style="width: 100%; height: 30px"
                        maxlength="40"
                    />
                </el-col>
            </el-row>
        </el-form-item>
        <el-form-item
            v-if="currentOrderDispatcher?.[currentShopType]?.deliverDialogFormData.deliverType === 'PRINT_EXPRESS'"
            label-width="90px"
            prop=""
        >
            <template #label>
                <div>发货地址</div>
            </template>
            <el-row style="width: 100%">
                <el-col :span="20">
                    <el-select
                        v-model="currentOrderDispatcher[currentShopType].deliverDialogFormData.addressaddress"
                        placeholder="选择发货地址"
                        style="width: 100%; height: 30px"
                    >
                        <el-option
                            v-for="item in deliveryAddressDataComputed"
                            :key="item.id"
                            :value="item.id"
                            :label="`${AddressFn(regionData, [item.provinceCode, item.cityCode, item.regionCode])}${item.address}`"
                        />
                    </el-select>
                </el-col>
                <!-- <el-col :span="4">
                    <div style="color: #2e99f3; margin-left: 15px; cursor: pointer" @click="handleDefultAddress">设为默认</div>
                </el-col> -->
            </el-row>
        </el-form-item>
    </el-form>
    <footer class="footer"><el-button @click="handleClose">取消</el-button> <el-button type="primary" @click="handleeSubmit">确认</el-button></footer>
</template>

<style lang="scss" scoped>
@import './style/table.scss';
@include b(footer) {
    @include flex(flex-end);
}
</style>
