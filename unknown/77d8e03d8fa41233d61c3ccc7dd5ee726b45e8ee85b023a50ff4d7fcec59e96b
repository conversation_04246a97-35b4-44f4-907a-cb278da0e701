/*
 * @description:
 * @Author: lexy
 * @Date: 2023-02-23 09:33:09
 * @LastEditors: lexy
 * @LastEditTime: 2023-03-29 10:37:07
 */
export interface RequestOptions {
    // 将请求参数拼接到url
    joinParamsToUrl?: boolean
    // 请求拼接路径
    urlPrefix?: string
    isMock: boolean
}
/**
 * 响应数据格式
 */
export type Result<T = any> = {
    // 响应码
    code: number
    // 响应提示信息
    msg: string
    // 响应数据
    data: T
    success: boolean
}
