/*
 * @description:
 * @Author: lexy
 * @Date: 2022-07-28 16:01:12
 * @LastEditors: lexy
 * @LastEditTime: 2022-07-29 18:17:04
 */
/**
 * @LastEditors: lexy
 * @description: 编辑(新增/修改) 支付商户信息
 * @param {string} appid
 * @param {string} detailsId
 * @param {string} keyCert 关键证书
 * @param {string} keyPrivate 关键密钥
 * @param {string} keyPublic 公钥
 * @param {string} mchId 商户号
 * @param {PAYTYPE} payType 支付类型
 * @param {PAYTYPE} platforms 支付平台
 */
export interface ApiParameters {
    subjectName: string
    appid: string
    detailsId: string
    keyCert: string
    keyPrivate: string
    keyPublic: string
    mchId: string
    payType: keyof typeof PAYTYPE
    platforms: Array<keyof typeof PLATFORMS>
}
/**
 * @LastEditors: lexy
 * @description: 支付类型
 * @param BALANCE 平衡
 * @param WECHAT 微信
 * @param ALIPAY 支付宝
 */
export enum PAYTYPE {
    BALANCE,
    WECHAT,
    ALIPAY,
}
/**
 * @LastEditors: lexy
 * @description: 支付平台
 * @param WECHAT_MINI_APP 微信的迷你应用程序
 * @param   WECHAT_MP 微信
 * @param   PC PC
 * @param   H5 H5
 * @param   IOS IOS
 * @param   ANDROID 安卓
 * @param   HARMONY 鸿蒙
 */
enum PLATFORMS {
    WECHAT_MINI_APP,
    WECHAT_MP,
    PC,
    H5,
    IOS,
    ANDROID,
    HARMONY,
}
/**
 * @LastEditors: lexy
 * @description: 平台
 * @returns {*}
 */
const platforms = [
    { key: 'WECHAT_MINI_APP', value: '微信小程序' },
    { key: 'WECHAT_MP', value: '微信公众号' },
    // { key: 'PC', value: 'PC端' },
    { key: 'H5', value: 'H5' },
    { key: 'IOS', value: 'IOS' },
    { key: 'ANDROID', value: '安卓' },
    { key: 'HARMONY', value: '鸿蒙' },
]
export { platforms }
