<template>
    <ChromeTab :tab-list="typeList" :value="typeTabRadio" @handle-tabs="changeTypeTab" />

    <el-tabs v-model="relateTypeTab" style="margin: 10px 20px" @tab-change="changeRelateTypeTab">
        <el-tab-pane v-for="pane in relateTypeList" :key="pane.label" :label="pane.label" :name="pane.name">
            <el-space class="flex flex-wrap">
                <el-button link @click="importDialogShow = true">批量导入</el-button>
                <el-button type="danger" plain @click="batchDel">批量删除</el-button>
                <el-button type="primary" plain :icon="Plus" @click="handleAdd">添加</el-button>
            </el-space>
        </el-tab-pane>
    </el-tabs>
    <LexiconTable ref="tableRef" :type="typeTabRadio" :relate-type="relateTypeTab" @update-lexicon="updateLexicon" />
    <LexiconDialog
        v-model="dialogConfig.show"
        :action="dialogConfig.action"
        :type="typeTabRadio"
        :relate-type="relateTypeTab"
        :id="dialogConfig.id"
        @submit-success="tableRef?.reload()"
    />
    <el-dialog v-model="importDialogShow" title="导入" destroy-on-close>
        <Import
            ref="fileImportRef"
            :type="typeTabRadio"
            :relate-type="relateTypeTab"
            @import-success=";(importDialogShow = false), tableRef?.reload()"
        />
        <template #footer>
            <el-button @click="importDialogShow = false">取 消</el-button>
            <el-button type="primary" @click="handleConfirmImport">确 定</el-button>
        </template>
    </el-dialog>
</template>
<script lang="ts" setup>
import ChromeTab from '@/components/chrome-tab/index.vue'
import LexiconTable from './components/lexicon-table.vue'
import LexiconDialog from './components/lexicon-dialog.vue'
import Import from './components/import.vue'
import { LEXICON_TYPE, LEXICON_RELATE_TYPE } from '@/apis/order/evaluationLexicon'
import { Plus } from '@element-plus/icons-vue'
import { Ref } from 'vue'
const tableRef = ref()
const typeTabRadio = ref<keyof typeof LEXICON_TYPE>('USER')
const relateTypeTab = ref<keyof typeof LEXICON_RELATE_TYPE>('PRODUCT')
const typeList = [
    { label: '用户词库', name: 'USER' },
    { label: '商家词库', name: 'MERCHANT' },
]
const relateTypeList = [
    { label: '商品词库', name: 'PRODUCT' },
    { label: '类目词库', name: 'CATEGORY' },
]
const changeTypeTab = (value: any) => {
    if (value) typeTabRadio.value = value
}
const changeRelateTypeTab = (value: any) => {
    if (value) relateTypeTab.value = value
}
const fileImportRef: Ref<InstanceType<typeof fileImport> | null> = ref(null)
const importDialogShow = ref(false)
const dialogConfig = ref<{
    show: boolean
    action: 'add' | 'edit'
    id?: string
}>({
    show: false,
    action: 'add',
})

// 添加
const handleAdd = () => {
    dialogConfig.value.show = true
    dialogConfig.value.action = 'add'
    dialogConfig.value.id = undefined
    tableRef.value?.reload(true)
}
// 批量删除
const batchDel = () => {
    if (tableRef.value) {
        tableRef.value.batchDel()
        tableRef.value.reload()
    }
}

// 更新词库
const updateLexicon = (id: string) => {
    dialogConfig.value.show = true
    dialogConfig.value.action = 'edit'
    dialogConfig.value.id = id
}

const handleConfirmImport = async () => {
    fileImportRef.value?.uploadExcelRef?.submit()
}
</script>
