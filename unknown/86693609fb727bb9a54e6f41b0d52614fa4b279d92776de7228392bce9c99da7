<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-08-12 15:53:46
 * @LastEditors: lexy
 * @LastEditTime: 2022-11-04 14:14:17
-->
<script setup lang="ts">
import { ElMessage } from 'element-plus'
import Line from './Line.vue'
import { doCourierUpdateAndEdit, doGetCourierInfo } from '@/apis/set/PrintSet'
/*
 *variable
 */
const printSetFormRef = ref()
const id = ref()
const rules = reactive({
    key: [{ required: true, message: '请输入快递100 key', trigger: 'blur' }],
    customer: [{ required: true, message: '请输入客户号', trigger: 'blur' }],
    secret: [{ required: true, message: '请输入快递100 secret', trigger: 'blur' }],
})
const printSetForm = reactive({ key: '', customer: '', secret: '' })
/*
 *lifeCircle
 */
initCourierInfo()
/*
 *function
 */
async function initCourierInfo() {
    const { code, data } = await doGetCourierInfo()
    if (code !== 200) return
    id.value = data.id
    printSetForm.customer = data.customer
    printSetForm.key = data.key
    printSetForm.secret = data.secret
}
const handleSubmit = async () => {
    try {
        await printSetFormRef.value.validate()
        const { key, customer, secret } = printSetForm

        const { code } = id.value
            ? await doCourierUpdateAndEdit(customer, key, secret, id.value)
            : await doCourierUpdateAndEdit(customer, key, secret)
        if (code !== 200) return ElMessage.error(id.value ? '快递设置更新失败' : '快递设置新增失败')
        ElMessage.success(id.value ? '快递设置更新成功' : '快递设置新增成功')
    } catch (error: any) {
        const errorArr = []
        for (const key in error) {
            errorArr.push(error[key][0].message)
        }
        ElMessage.error({
            message: errorArr.join(' '),
            onClose: () => {
                printSetFormRef.value.resetFields()
            },
        })
    }
}
</script>

<template>
    <Line name="快递100" color="#08CC00" />
    <el-form
        ref="printSetFormRef"
        :show-message="false"
        :model="printSetForm"
        label-width="120px"
        label-position="left"
        :rules="rules"
        style="padding: 1% 0 3% 3%"
    >
        <el-form-item label="快递100 key" label-width="120px" prop="key">
            <el-input v-model.trim="printSetForm.key" style="width: 90%" maxlength="40"></el-input>
        </el-form-item>
        <el-form-item label="快递100 客户号" label-width="120px" prop="customer">
            <el-input v-model.trim="printSetForm.customer" style="width: 90%" maxlength="40"></el-input>
        </el-form-item>
        <el-form-item label="快递100 secret" label-width="120px" prop="secret">
            <el-input v-model.trim="printSetForm.secret" style="width: 90%" maxlength="60"></el-input>
        </el-form-item>
    </el-form>
    <el-button style="margin-left: 4%" type="primary" @click="handleSubmit">保存</el-button>
</template>

<style scoped lang="scss"></style>
