<script setup lang="ts">
import { ref } from 'vue'
import { useDraggable } from '@vueuse/core'

const el = ref<HTMLElement | null>(null)
const position = ref({ x: 40, y: 200 })
useDraggable(el, {
    preventDefault: true,
    onMove(val) {
        position.value = val
    },
})
</script>

<template>
    <div class="test" :style="{ top: position.y + 'px', left: position.x + 'px' }">
        <div ref="el" style="touch-action: none">👋 Drag me!</div>
    </div>
</template>

<style lang="scss" scoped>
.test {
    position: fixed;
    width: 100px;
    height: 200px;
    background-color: red;
}
</style>
