<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-08-08 17:53:14
 * @LastEditors: lexy
 * @LastEditTime: 2023-06-30 14:18:39
-->
<script setup lang="ts">
import { PropType, ref, computed, watch, onMounted } from 'vue'
import defaultGoodData from './goods'
import { ApiGoodItemType, ListStyle } from './goods'
import PricesLine from './pricesLine.vue'
import Coner from './coner.vue'
import useConvert from '@/composables/useConvert'
import type { CategoryItem } from './goods'
const def_good_pic = 'https://devoss.chongyoulingxi.com/system-front/mobile/def_commodity.png'
const def_category_img = 'https://devoss.chongyoulingxi.com/system-front/mobile/def_category.png'
const sortList = ['人气', '销量', '新品', '价格']
/*
 *variable
 */
const $props = defineProps({
    formData: {
        type: Object as PropType<typeof defaultGoodData>,
        default() {
            return defaultGoodData
        },
    },
})
const activeItemSort = ref<string>('')
const { divTenThousand } = useConvert()
const goodsList = ref<any[] | ApiGoodItemType[]>([])
const categoryList = ref<any[] | CategoryItem[]>([])
const curActiveCategoryId = ref<string>('')
const pageStyle = computed(() => ({
    padding: `0px 10px`,
    fontSize: '15px',
    minHeight: '200px',
}))
/**
 * @LastEditors: lexy
 * @description: 动态显示商品边框样式
 */
const goodStyle = computed(() => {
    let margin = ''
    let width = ''
    const { listStyle } = $props.formData
    switch (listStyle) {
        case 'goods-style--four':
            margin = `0px 10px 0px 0px`
            break
        case 'goods-style--two':
            margin = `0px 0px ${10}px 0px`
            width = `calc(50% - ${10 / 2}px)`
            break
        case 'goods-style--five':
            margin = `0px 0px ${10}px 0px`
            width = `calc(50% - ${10 / 2}px)`
            break
        default:
            margin = `10px 0`
            break
    }
    return { margin, width }
})
/**
 * @LastEditors: lexy
 * @description: 模式2调整边距
 */
const goodStyle2 = computed(() => {
    const margin = `0px 0px 10px 0px`
    return { margin }
})

watch(
    $props,
    (val) => {
        if (val.formData.sort) {
            activeItemSort.value = sortList[0]
        }
        getCategoryGoodList()
    },
    { immediate: true },
)
/*
 *lifeCircle
 */
onMounted(() => {
    getCategoryGoodList()
})
/*
 *function
 */
const activeClass = (id: string) => {
    if ($props.formData.firstCatList?.length) {
        Object.assign($props.formData, { currentCategoryId: id })
    } else {
        curActiveCategoryId.value = id
    }
}
function getCategoryGoodList() {
    const { ponentType = 1, currentCategoryId = '', firstCatList = [], goods = [] } = $props.formData
    // 选择商品数量时对应显示预览效果
    if (ponentType === 1) {
        if (!firstCatList.length) {
            categoryList.value = Array.from({ length: 2 }, (v, i) => ({ ...defaultGoodData.categoryTemp, id: (i + 1).toString() }))
            curActiveCategoryId.value = categoryList.value[0].id
            goodsList.value = Array.from({ length: 5 }, (v, i) => ({ ...defaultGoodData.goods, id: (i + 1).toString() }))
        } else {
            const item = firstCatList.find((i) => i.id === currentCategoryId)
            curActiveCategoryId.value = currentCategoryId
            if (item) {
                goodsList.value = Array.from({ length: item.productNum || 5 }, (v, i) => ({ ...defaultGoodData.goodsTemp, id: 'good' + i }))
            }
        }
    } else if (ponentType === 2) {
        goodsList.value = goods.length ? goods : Array.from({ length: 5 }, (v, i) => ({ ...defaultGoodData.goods, id: (i + 1).toString() }))
    }
}

const waterfallHeight = (i: number) => {
    if ($props.formData.listStyle === 'goods-style--five') {
        return { height: [5, -5][i % 2] + 240 + 'px' }
    }
    return {}
}
function formatprice(salePricesArr: Array<string> | string) {
    if (Array.isArray(salePricesArr)) {
        return salePricesArr.length ? divTenThousand(salePricesArr[0]) : '99.00'
    } else {
        return divTenThousand(salePricesArr)
    }
}
const tabbarHeight = computed(() => `${$props.formData.categoryStyle === 'pic-text' ? 68 : 42}px`)
</script>

<template>
    <div class="goods__ponent-page">
        <!-- 头部分类 s -->
        <div v-if="$props.formData.ponentType === 1 && categoryList?.length" class="tab__bar-box" :style="{ height: tabbarHeight }">
            <div class="con">
                <div
                    v-if="$props.formData.featuredCategory"
                    :class="[
                        'class__item',
                        $props.formData.categoryStyle === 'pic-text' ? 'class__pic' : 'class__text',
                        'recommend' === curActiveCategoryId ? ($props.formData.categoryStyle === 'pic-text' ? 'pic-active' : 'text-active') : '',
                    ]"
                    @click="activeClass('recommend')"
                >
                    <div v-if="$props.formData.categoryStyle === 'pic-text'" class="category_img">
                        <img :src="def_category_img" />
                    </div>
                    <span class="item__name">全部</span>
                </div>
                <div
                    v-for="(item, idx) in categoryList"
                    :key="idx"
                    :class="[
                        'class__item',
                        $props.formData.categoryStyle === 'pic-text' ? 'class__pic' : 'class__text',
                        item.id === curActiveCategoryId ? ($props.formData.categoryStyle === 'pic-text' ? 'pic-active' : 'text-active') : '',
                    ]"
                    @click="activeClass(item.id)"
                >
                    <div v-if="$props.formData.categoryStyle === 'pic-text'" class="category_img">
                        <img :src="item.pic || def_category_img" />
                    </div>
                    <span class="item__name">{{ item.name }}</span>
                </div>
            </div>
            <div v-if="$props.formData.moreGoods" class="sort-btn" :style="{ height: tabbarHeight }">
                <div class="sort-btn__icon" />
            </div>
        </div>
        <div v-if="$props.formData.sort" class="sort-group">
            <div v-for="item in sortList" :key="item" class="sort-item" :class="{ selected: item === activeItemSort }" @click="activeItemSort = item">
                {{ item }}
            </div>
        </div>
        <!-- 头部分类 e -->
        <!-- 商品主体展示 s -->
        <!-- $props.formData.listStyle === ListStyle['goods-style--two'] && goodsList.length === 1 ? '' : $props.formData.listStyle -->
        <div
            v-if="($props.formData.ponentType === 1 && categoryList.length) || ($props.formData.ponentType === 2 && goodsList.length)"
            class="goods"
            :class="$props.formData.listStyle === ListStyle['goods-style--two'] && goodsList.length === 1 ? '' : $props.formData.listStyle"
            :style="{ ...pageStyle, scrollbarWidth: 'none' }"
        >
            <!-- 大图模式 -->
            <template v-if="$props.formData.listStyle === ListStyle['goods-style--one']">
                <div v-for="(item, idx) in goodsList" :key="idx" :class="['goods-item']">
                    <!-- tag -->
                    <coner :tag-show="$props.formData.showContent.tagShow" :tag-style="$props.formData.showContent.tagStyle"></coner>
                    <!-- tag -->
                    <!-- 大图模式 s -->
                    <div class="goods-item__large_box">
                        <div class="goods-item__large">
                            <img :class="[$props.formData.ponentType === 1 ? 'show__mall' : 'show__big']" :src="item.img || def_good_pic" />
                        </div>
                        <div class="goods-item__large_foot" style="padding: 0 7px">
                            <div class="goods-item__name" style="width: 100%">
                                {{ item ? item.productName : '商品名称' }}
                            </div>
                            <prices-line
                                :good="item"
                                :show-price="!!$props.formData.showPrice"
                                :is-preview="true"
                                :show-sales="$props.formData.showSales"
                                :button-style="$props.formData.showContent.buttonStyle"
                            />
                        </div>
                        <div class="goods-item__large_foot--placeholder-node"></div>
                    </div>
                </div>
            </template>
            <!-- 大图模式 -->
            <!-- 一行两个 goodStyle2 goodStyle-->
            <template v-else-if="$props.formData.listStyle === ListStyle['goods-style--two']">
                <div
                    v-for="(item, idx) in goodsList"
                    :key="idx"
                    :class="['goods-item']"
                    :style="{ ...(idx === $props.formData.goods.length - 1 ? goodStyle2 : goodStyle) }"
                >
                    <coner :tag-show="$props.formData.showContent.tagShow" :tag-style="$props.formData.showContent.tagStyle"></coner>
                    <div class="goods-item__icon">
                        <div class="ipic" style="border-radius: 10px 10px 0 0">
                            <img :class="[$props.formData.ponentType === 1 ? 'show__mall' : 'show__big']" :src="item.img || def_good_pic" />
                        </div>
                    </div>

                    <div class="goods-item__foot" style="padding: 0 7px">
                        <div class="goods-item__name">
                            {{ item ? item.productName : '商品名称一行两个' }}
                        </div>
                        <div class="goods-item__bottom">
                            <prices-line
                                :good="item"
                                :show-price="!!$props.formData.showPrice"
                                :is-preview="true"
                                :show-sales="$props.formData.showSales"
                                :button-style="$props.formData.showContent.buttonStyle"
                            />
                        </div>
                    </div>
                </div>
            </template>
            <!-- 一行两个 -->
            <!-- 详细列表 -->
            <template v-else-if="$props.formData.listStyle === ListStyle['goods-style--three']">
                <div
                    v-for="(item, idx) in goodsList"
                    :key="idx"
                    :class="['goods-item', 'goods-item-three_shadow']"
                    :style="{ ...(idx === $props.formData.goods.length - 1 ? goodStyle2 : goodStyle) }"
                >
                    <coner :tag-show="$props.formData.showContent.tagShow" :tag-style="$props.formData.showContent.tagStyle"></coner>
                    <div class="goods-item__three">
                        <div class="goods-item__three_img">
                            <img :src="item.img || def_good_pic" />
                        </div>
                    </div>

                    <div class="goods-item__foot" style="padding: 0 7px">
                        <div class="goods-item__name">
                            {{ item ? item.productName : '商品名称' }}
                        </div>
                        <div class="goods-item__bottom">
                            <prices-line
                                :good="item"
                                :show-price="!!$props.formData.showPrice"
                                :is-preview="true"
                                :show-sales="$props.formData.showSales"
                                :button-style="$props.formData.showContent.buttonStyle"
                            />
                        </div>
                    </div>
                </div>
            </template>
            <!-- 详细列表 -->
            <!-- 横向滑动 -->
            <template v-else-if="$props.formData.listStyle === ListStyle['goods-style--four']">
                <div
                    v-for="(item, idx) in goodsList"
                    :key="idx"
                    :class="['goods-item', 'goods-item-three_shadow']"
                    :style="{ ...(idx === $props.formData.goods.length - 1 ? goodStyle2 : goodStyle) }"
                >
                    <coner :tag-show="$props.formData.showContent.tagShow" :tag-style="$props.formData.showContent.tagStyle"></coner>
                    <div class="goods-item__four">
                        <div class="goods-item__four_img">
                            <img :src="item.img || def_good_pic" />
                        </div>
                    </div>

                    <div class="goods-item__foot" style="padding: 0 7px">
                        <div class="goods-item__name" style="font-weight: 600; font-size: 11px">
                            {{ item ? item.productName : '商品名称' }}
                        </div>
                        <div class="goods-item__bottom">
                            <prices-line
                                :good="item"
                                :show-price="!!$props.formData.showPrice"
                                :is-preview="true"
                                :show-sales="$props.formData.showSales"
                                :button-style="$props.formData.showContent.buttonStyle"
                            />
                        </div>
                    </div>
                </div>
            </template>
            <!-- 横向滑动 -->
            <!-- 瀑布 -->
            <template v-else>
                <div
                    v-for="(item, idx) in goodsList"
                    :key="idx"
                    :class="['goods-item']"
                    :style="{ ...waterfallHeight(idx), ...(idx === $props.formData.goods.length - 1 ? goodStyle2 : goodStyle) }"
                >
                    <coner :tag-show="$props.formData.showContent.tagShow" :tag-style="$props.formData.showContent.tagStyle"></coner>
                    <div class="goods-item__icon">
                        <div class="ipic" style="border-radius: 10px 10px 0 0">
                            <img :class="[$props.formData.ponentType === 1 ? 'show__mall' : 'show__big']" :src="item.img || def_good_pic" />
                        </div>
                    </div>

                    <div class="goods-item__foot" style="padding: 0 7px">
                        <div class="goods-item__name">
                            {{ item ? item.productName : '商品名称' }}
                        </div>
                        <div class="goods-item__bottom" style="margin-top: 10px">
                            <prices-line
                                :good="item"
                                :show-price="!!$props.formData.showPrice"
                                :is-preview="true"
                                :show-sales="$props.formData.showSales"
                                :button-style="$props.formData.showContent.buttonStyle"
                            />
                        </div>
                    </div>
                </div>
            </template>
            <!-- 瀑布 -->
        </div>
        <!-- 商品主体展示 e -->
        <div
            v-if="
                ($props.formData.ponentType === 1 && !(categoryList && categoryList.length)) ||
                ($props.formData.ponentType === 2 && !goodsList.length)
            "
            class="no__goods-item"
        >
            <img :src="def_good_pic" />
        </div>
    </div>
</template>

<style scoped lang="scss">
@import '@/assets/css/decoration/goods.scss';

.goods__ponent-page {
    .tab__bar-box {
        width: 100%;
        overflow-y: hidden;
        overflow-x: scroll;
        scrollbar-width: none;
        position: relative;
        margin-bottom: 12px;

        .con {
            display: inline-flex;
            flex-wrap: nowrap;
            align-items: center;
            width: auto;
            min-width: 100%;
            height: inherit;
            padding-right: 50px;
        }

        .class__item {
            display: inline-flex;
            justify-content: center;
            text-align: center;
            align-items: center;
            position: relative;
            font-size: 14px;
            cursor: pointer;
            height: inherit;
            min-width: 72px;
            transition: all 0.3s ease; // 添加整体过渡效果

            .category_img {
                width: 40px;
                height: 40px;
                border-radius: 100%;
                overflow: hidden;
                background-color: #fff;
                img {
                    width: inherit;
                    height: inherit;
                    object-fit: contain;
                    background: transparent;
                }
            }

            span {
                display: inline-block;
                white-space: nowrap;
                line-height: 140%;
                color: $cylx-text-gray-color;
                font-family: PingFangSC-Medium;
                transition: color 0.3s ease; // 添加文字颜色
            }

            // 添加悬停效果
            &:hover {
                transform: translateY(-2px);
            }
        }
        .class__pic {
            z-index: 8;
            flex-direction: column;
            height: 66px;
            gap: 4;
            padding: 2px;
            .category_img {
                background: linear-gradient(180deg, rgb(255, 255, 255) 0.763%, rgba(255, 255, 255, 0.86) 67.176%, rgba(255, 255, 255, 0) 100%);
            }
            span {
                font-size: 14px;
            }
        }

        .pic-active {
            .category_img {
                background: linear-gradient(180deg, rgb(232, 217, 245) 0.763%, rgba(255, 255, 255, 0.86) 67.176%, rgba(255, 255, 255, 0) 100%);
            }
            span {
                color: $theme-color;
                font-weight: 500;
            }
        }

        .class__text {
            z-index: 8;
            display: inline-block;
            position: relative;
            padding: 10px 4px;
            span {
                color: $cylx-text-color;
                font-size: 15px;
            }
        }

        .text-active {
            span {
                color: $theme-color;
            }
            &::after {
                content: '';
                position: absolute;
                top: 40px;
                bottom: 0px;
                left: calc(50% - 21px);
                width: 42px;
                height: 2px;
                border-radius: 2px;
                /* 主色/常规 */
                background: #a74be8;
                transform-origin: center bottom;
                animation: arcAppear 0.3s ease forwards; // 添加圆弧出现动画
            }
        }

        .sort-btn {
            content: '';
            position: sticky;
            left: calc(100% - 50px);
            right: 0;
            top: 0;
            width: 50px;
            bottom: 0;
            background-color: white;
            display: inline-flex;
            align-items: center;
            z-index: 10;
            .sort-btn__icon {
                width: 19px;
                height: 19px;
                margin-left: 15px;
                background-image: url('@/assets/image/decoration/menu.png');
                background-repeat: no-repeat;
                background-position: center;
                background-size: contain;
            }
        }
    }

    .goods-item__icon {
        width: 100%;
        height: 160px;

        .ipic {
            display: inline-block;
            width: 100%;
            height: 100%;
            background-color: rgba(233, 247, 253, 1);
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 10px;

            .show__mall {
                display: inline-block;
                width: 44px;
                height: 46px;
            }

            .show__big {
                display: inline-block;
                width: 100%;
                height: 100%;
            }
        }
    }

    .goods-style--three {
        .goods-item__icon {
            height: 128px;
            width: 128px;
            margin-right: 10px;
            flex: none;

            .ipic {
                height: 128px;
            }
        }
    }

    .spellpre__goods--delivery {
        color: #a3a3a3;
        font-size: 12px;
        font-weight: 400;
        padding-top: 10px;
    }
}
// 添加关键帧动画
@keyframes arcAppear {
    from {
        transform: scaleX(0.2);
        opacity: 0;
    }

    to {
        transform: scaleX(1);
        opacity: 1;
    }
}
</style>
