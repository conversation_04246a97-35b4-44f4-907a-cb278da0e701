<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-11-22 09:24:37
 * @LastEditors: lexy
 * @LastEditTime: 2023-01-30 10:17:04
-->
<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus'
import { doGetFreeMemberList, doPostAvailableMember, doPostFreeMemberSetLabel } from '@/apis/member'
import { type Ref } from 'vue'
import EditMember from './EditMember.vue'
import SetLabel from './set-label/index.vue'
/*
 *variable
 */
const router = useRouter()
const { divHundred } = useConvert()
// 分享弹窗
const sharePopupShow = ref(false)
const memberList = ref([])
// const currentMemberId: Ref<string | undefined> = ref()
const currentMemberDialogConfig: { id: string; currentMemberLevel: number | undefined } = reactive({
    id: '',
    currentMemberLevel: undefined,
})
const showDialog = ref(false)
const editMemberRef: Ref<InstanceType<typeof EditMember> | null> = ref(null)
/*
 *lifeCircle
 */
initStodioList()
/*
 *function
 */
const handleDelClick = async (row: any) => {
    ElMessageBox.confirm('确定需要删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(async () => {
        const { code, msg } = await doPostAvailableMember(row.id)
        if (code === 200) {
            ElMessage.success('删除成功')
            initStodioList()
        } else {
            ElMessage.error(msg ? msg : '删除失败')
        }
    })
}
const handleAddLevel = () => {
    showDialog.value = true
    currentMemberDialogConfig.currentMemberLevel = memberList.value.length + 1
}
const handleNavToEdit = (id: string, level: number) => {
    currentMemberDialogConfig.id = id
    currentMemberDialogConfig.currentMemberLevel = level
    showDialog.value = true
}
async function initStodioList() {
    const { code, data } = await doGetFreeMemberList()
    if (code !== 200) return ElMessage.error('获取免费会员失败')
    memberList.value = data
}
const handleCloseDialog = () => {
    showDialog.value = false
    currentMemberDialogConfig.id = ''
    currentMemberDialogConfig.currentMemberLevel = undefined
}
const handleConfirm = async () => {
    await editMemberRef?.value?.handleSubmit()
    handleCloseDialog()
    initStodioList()
}

const setLabelRef = ref<InstanceType<typeof SetLabel> | null>(null)
const labelSetInfo = reactive({
    showLabelDialog: false,
    currentSetLabelForm: {
        id: '',
        name: '',
        fontColor: '',
        labelColor: '',
        priceLabelName: '',
        priceFontColor: '',
        priceLabelColor: '',
    },
})
const handleLabelSet = (row: any) => {
    Object.keys(labelSetInfo.currentSetLabelForm).forEach((key) => {
        if (row?.labelJson?.[key]) {
            // @ts-ignore
            labelSetInfo.currentSetLabelForm[key] = row?.labelJson?.[key]
        }
    })
    labelSetInfo.currentSetLabelForm.id = row?.id
    labelSetInfo.currentSetLabelForm.name = row?.freeMemberName
    labelSetInfo.showLabelDialog = true
}
const handleCloseSetLabelDialog = () => {
    Object.keys(labelSetInfo.currentSetLabelForm).forEach((key) => {
        // @ts-ignore
        labelSetInfo.currentSetLabelForm[key] = ''
    })
}
const handleConfirmSetLabel = async () => {
    const setLabelData = await setLabelRef.value?.getFormModel()
    const { code, msg } = await doPostFreeMemberSetLabel(setLabelData)
    if (code === 200) {
        ElMessage.success({ message: msg || '标签设置成功' })
        initStodioList()
        labelSetInfo.showLabelDialog = false
    } else {
        ElMessage.error({ message: msg || '标签设置失败' })
    }
}
</script>
<template>
    <div class="handle_container">
        <el-button round type="primary" style="margin-bottom: 15px" :disabled="memberList.length >= 10 ? true : false" @click="handleAddLevel">
            添加等级
        </el-button>
    </div>
    <div class="table_container">
        <el-table
            ref="multipleTableRef"
            :data="memberList"
            :header-row-style="{ fontSize: '12px', color: '#909399' }"
            :header-cell-style="{ background: '#f6f8fa', fontWeight: 400, color: '#333333' }"
            :cell-style="{ fontSize: '12px', color: '#333333' }"
        >
            <el-table-column label="会员等级" align="center">
                <template #default="{ $index }">
                    <div class="level">vip{{ $index + 1 }}</div>
                </template>
            </el-table-column>
            <el-table-column label="免费会员名称" align="center">
                <template #default="{ row }">
                    <span>{{ row.freeMemberName }}</span>
                </template>
            </el-table-column>
            <el-table-column label="所需成长值" align="center">
                <template #default="{ row }">
                    <!-- <template #default="{ row, $index }"> -->
                    <div class="pricing">
                        <!-- <div v-if="$index === 0">注册信息</div> -->
                        <!-- <div>获取{{ row.needValue }}成长值</div> -->
                        <div>{{ row.needValue }}</div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="会员权益" align="center">
                <template #default="{ row }">
                    <div v-for="item in row.relevancyRightsList" :key="item.id" class="interests">
                        <div v-if="item.rightsType === 'GOODS_DISCOUNT'">商品折扣{{ divHundred(item.extendValue) }}折</div>
                        <div v-else-if="item.rightsType === 'INTEGRAL_MULTIPLE'">积分{{ divHundred(item.extendValue) }}倍</div>
                        <div v-else>{{ item.rightsName }}</div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" width="150" align="center">
                <template #default="{ row, $index }: { row: any, $index: number }">
                    <el-link style="padding: 0 5px" :underline="false" type="primary" size="small" @click="handleNavToEdit(row.id, $index + 1)">
                        编辑
                    </el-link>
                    <el-link style="padding: 0 5px" :underline="false" type="primary" size="small" @click="handleLabelSet(row)">标签设置</el-link>
                    <el-link v-if="$index > 0" style="padding: 0 5px" :underline="false" type="primary" size="small" @click="handleDelClick(row)">
                        删除
                    </el-link>
                </template>
            </el-table-column>
        </el-table>
    </div>
    <el-dialog v-model="showDialog" :title="currentMemberDialogConfig.id ? '编辑会员' : '添加会员'" destroy-on-close @close="handleCloseDialog">
        <edit-member ref="editMemberRef" :member-id="currentMemberDialogConfig.id" :member-level="currentMemberDialogConfig.currentMemberLevel" />
        <template #footer>
            <el-button @click="handleCloseDialog">取 消</el-button>
            <el-button type="primary" @click="handleConfirm">保 存</el-button>
        </template>
    </el-dialog>
    <el-dialog v-model="labelSetInfo.showLabelDialog" title="标签设置" destroy-on-close @close="handleCloseSetLabelDialog">
        <set-label ref="setLabelRef" :label-info="labelSetInfo.currentSetLabelForm" />
        <template #footer>
            <el-button @click="labelSetInfo.showLabelDialog = false">取 消</el-button>
            <el-button type="primary" @click="handleConfirmSetLabel">保 存</el-button>
        </template>
    </el-dialog>
</template>

<style scoped lang="scss">
@include b(level) {
    font-size: 12px;
    color: #ce732f;
}
@include b(pricing) {
    font-size: 12px;
    color: #838383;
}
@include b(interests) {
    font-size: 12px;
    color: #333333;
}
</style>
