<!--
 * @description: 装修入口页面
 * @Author: lexy
 * @Date: 2022-07-22 10:21:44
 * @LastEditors: lexy
 * @LastEditTime: 2024-04-16 13:33:21
-->
<script setup lang="ts">
import EditorComponent from './components/editor-component.vue'
import EditorPreview from './components/editor-preview.vue'
import EditorForm from './components/editor-form.vue'
import MallTemplate from './template/index.vue'
import SetPages from './pages/index.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useDecorationStore } from '@/store/modules/decoration/index'
import { doPostPlatformPagesSave, doPutPlatformPages } from '@/apis/decoration'
import type { ComponentItem } from './packages/components/index/formModel'
/*
 *variable
 */
const $decorationStore = useDecorationStore()
// 左侧导航栏下标
// const currentTab = ref(0)
// 当前编辑组件
const currentComponent = ref<ComponentItem>()
// 组件列表
const components = ref<ComponentItem[]>([])
// 控件列表
// const controllerComponentList = ref<ComponentItem[]>([])
const editorPreviewRef = ref<typeof EditorPreview>()
// 获取当前页面操作栏
const activePageType = computed(() => {
    return $decorationStore.activePageType
})

//当前编辑类型  template模板 pages页面
const activeType = ref('template')
//当前编辑页面类型
const activePagesType = ref('SHOP_HOME_PAGE')
//当前编辑页面名称
const name = ref('')
//当前编辑页面说明
const remark = ref('')
//当前编辑页面id
const id = ref('')
//是否在编辑/新增页面
const iseditor = ref(false)
//是否是第一次进入
const isFirst = ref(true)
/*
 *lifeCircle
 */
/*
 *function
 */

/**
 * @LastEditors: lexy
 * @description: 设置当前组件
 * @param {*} component
 */
const setCurrentComponent = (component: ComponentItem) => {
    currentComponent.value = component
}
/**
 * @LastEditors: lexy
 * @description: 新增组件
 * @param {ComponentItem} component
 * @param {number} index
 */
const handleAddComponent = (component: ComponentItem, index: number) => {
    if (activePageType.value === 'control' || activePageType.value === 'classification') {
        ElMessage.warning('该页面无法添加组件')
        return
    }
    if (component && index !== undefined) {
        // 拖拽添加
        components.value[index] = component
    } else {
        // 点击添加
        components.value.push(component)
    }
    currentComponent.value = component
    editorPreviewRef.value?.setCurrentFlag(index)
}
/**
 * @LastEditors: lexy
 * @description: 删除组件
 * @returns {*}
 */
const handleDelComponent = (e: { index: number }) => {
    components.value.splice(e.index, 1)
}
/**
 * @LastEditors: lexy
 * @description: 保存页面
 */
const handleSavePage = async (close: boolean = true) => {
    if (!components.value.length) {
        ElMessage.warning('请添加组件')
        return
    }
    if (!name.value) {
        ElMessage.warning('请填写页面名称')
        return
    }
    if (!remark.value) {
        ElMessage.warning('请填写页面说明')
        return
    }
    const submitForm = {
        id: id.value,
        name: name.value,
        remark: remark.value,
        properties: components.value,
        type: 'SHOP_HOME_PAGE',
        businessType: 'O2O',
        templateType: 'SHOP',
        endpointType: $decorationStore.getEndpointType,
    }
    const { code, data } = id.value ? await doPutPlatformPages(submitForm) : await doPostPlatformPagesSave(submitForm)
    // if (data) {
    //     id.value = data
    // }
    if (code === 100011) {
        ElMessage.warning('页面名称重复，请修改')
    } else if (code === 200) {
        ElMessage.success('保存成功')
        if (close) {
            iseditor.value = false
            initPreviewAndSet()
            initParams()
        }
    } else {
        ElMessage.warning('保存失败,请重试')
    }
}

/**
 * @LastEditors: zrb
 * @description: 关闭编辑页面回调
 */
function handleClosePage() {
    ElMessageBox.confirm('修改将不会保存，确定关闭?').then(() => {
        iseditor.value = false
        initPreviewAndSet()
        initParams()
    })
}

/**
 * @LastEditors: zrb
 * @description: 初始化编辑框内容,解决编辑后关闭再新增会复用上一编辑数据
 */
function initParams() {
    id.value = ''
    name.value = ''
    remark.value = ''
}

/**
 * @LastEditors: lexy
 * @description: 初始化组件配置页和预览页
 */
function initPreviewAndSet() {
    currentComponent.value = undefined
    editorPreviewRef.value?.setCurrentFlag(-1)
}

/**
 * @LastEditors: lexy
 * @description: 页面编辑回调
 */
function handleEditorPages(data: any) {
    iseditor.value = true
    if (data) {
        name.value = data.name
        remark.value = data.remark
        id.value = data.id
        components.value = JSON.parse(data.properties)
    } else {
        components.value = []
    }
}
/**
 * @LastEditors: lexy
 * @description: tab点击事件
 */
function handleTabClick() {
    isFirst.value = false
    if (iseditor.value) initPreviewAndSet()
    iseditor.value = false
    name.value = ''
    remark.value = ''
    id.value = ''
}
</script>

<template>
    <div class="editorWrapper">
        <!-- 头部 -->
        <div class="head">
            <div class="head__top">
                <el-tabs v-model="activeType" class="head__top--tabs" type="card" @tab-click="handleTabClick">
                    <el-tab-pane label="店铺模板" name="template"></el-tab-pane>
                    <el-tab-pane label="店铺首页" name="pages"></el-tab-pane>
                </el-tabs>
                <div class="head__top--terminal">
                    店铺模板装修({{ $decorationStore.getEndpointType === 'H5_APP' ? 'H5、APP 端' : '微信小程序端' }})
                </div>
            </div>
            <div v-if="activeType === 'pages' && iseditor" class="head__editor">
                <div class="head__editor_left">
                    <div>页面名称</div>
                    <el-input v-model="name" style="width: 200px; margin-left: 10px" maxlength="10" />

                    <div style="margin-left: 20px">页面说明</div>
                    <el-input v-model="remark" style="width: 500px; margin-left: 10px" maxlength="20" />
                </div>
                <div class="head__editor_right">
                    <el-button style="margin-right: 20px; width: 100px" @click="handleClosePage"> 关闭</el-button>
                    <el-button type="info" style="margin-right: 20px; width: 100px" @click="handleSavePage(false)" v-if="id"> 仅保存</el-button>
                    <el-button type="primary" style="margin-right: 20px; width: 100px" @click="handleSavePage"> 保存关闭</el-button>
                </div>
            </div>
        </div>
        <!-- 商城模板 -->
        <mall-template v-if="activeType === 'template'" />
        <!-- 页面列表 -->
        <set-pages v-if="activeType === 'pages' && !iseditor" :type="activePagesType" :is-first="isFirst" @editor-pages="handleEditorPages" />
        <!-- 页面详情 -->
        <div v-show="activeType === 'pages' && iseditor" class="editorPage">
            <div v-if="activePagesType === 'SHOP_HOME_PAGE' || activePagesType === 'SHOP_CUSTOMIZED_PAGE'" class="editorPage_left">
                <div class="editor_view">
                    <!-- 组件列表 -->
                    <editor-component @change="handleAddComponent" />
                </div>
            </div>
            <div class="editorPage_main">
                <div class="tab_con">
                    <!-- 组件以及页面预览 -->
                    <editor-preview
                        ref="editorPreviewRef"
                        :components="components"
                        @change="setCurrentComponent"
                        @add="handleAddComponent"
                        @del="handleDelComponent"
                    />
                </div>
            </div>
            <div v-if="currentComponent" class="editorPage_right">
                <div class="editorPage_right_wrap">
                    <!-- 样式编辑 -->
                    <editor-form :current-component="currentComponent" />
                </div>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
@import '@/assets/css/decoration/editPage.scss';
@include b(head) {
    background: #fff;
    @include e(top) {
        display: flex;
        border: 1px solid #bbbbbb;
        justify-content: space-between;
        @include m(terminal) {
            width: 200px;
            height: 40px;
            border-right: 1px solid #bbbbbb;
            font-size: 14px;
            text-align: center;
            line-height: 40px;
            color: #000;
        }
        @include m(tabs) {
            height: 40px;
            font-size: 14px;
        }
    }
    @include e(pagesType) {
        height: 50px;
    }
    @include e(editor) {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 45px;
    }
    @include e(editor_left) {
        display: flex;
        align-items: center;
        margin-left: 60px;
    }

    @include e(editor_right) {
        display: flex;
        align-items: center;
        flex-direction: row;
    }
}
#editor__preview_position {
    position: relative;
    padding-bottom: 15px;
    overflow: auto;
    width: 375px;
    height: calc(100vh - 80px);
    border: 1px solid #ccc;
    background-color: #fff;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin-bottom: 100px;
    visibility: hidden;
}
#editor__from_position {
    width: 435px;
    height: 667px;
    overflow-y: scroll;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding-bottom: 50px;
}
/**新的编辑样式**/
.editorPage_right_wrap {
    width: 405px;
    display: flex;
    flex-direction: column;
    height: 100%;
}
div::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
}

#savetip {
    font-size: 14px;
    color: #888;
    margin-left: 6px;
}
.no-l-b {
    border-right: none !important;
}
</style>
