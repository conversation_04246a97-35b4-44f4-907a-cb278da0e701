<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-03-26 14:55:22
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-05 13:59:20
-->
<template>
    <div ref="AddShops" class="shopForm fdc1">
        <el-steps :active="stepIndex" simple class="shopForm__steps">
            <el-step title="1.基本信息" icon="none"></el-step>
            <el-step title="2.信息登记" icon="none"></el-step>
            <el-step title="3.收款账户" icon="none"></el-step>
        </el-steps>
        <keep-alive>
            <component
                :is="reactiveComponent[currentStep]"
                ref="componentRef"
                :supplier-view-model="supplierViewModel"
                @map-change="handleMapChange"
            />
        </keep-alive>
        <div class="shopForm__tool">
            <el-button v-if="prevStep !== ''" type="default" round @click="preHandle">上一步</el-button>
            <el-button v-if="nextStep !== ''" type="primary" round @click="nextHandle">下一步</el-button>
            <template v-if="$route.name !== 'previewShop'">
                <el-button v-if="nextStep === ''" type="info" plain round @click="handleCancel">取消</el-button>
                <!-- zrb:如果是编辑态，则一直显示更新按钮 -->
                <el-button v-if="$route.query.shopId" type="primary" plain round :loading="submitLoading" @click="submitHandle">更新</el-button>
                <!-- zrb:添加态则最后一部才显示保存 -->
                <el-button v-else-if="nextStep === ''" type="primary" plain round :loading="submitLoading" @click="submitHandle">保存</el-button>
            </template>
        </div>
        <!-- 选择素材 e -->
        <selectMaterial
            :dialog-visible="dialogVisible"
            :upload-files="uploadFileLimit"
            @select-material-fn="selectMaterialFn"
            @cropped-file-change="croppedFileChange"
            @checked-file-lists="croppedFileChange"
        />
        <!-- 选择素材 d -->
    </div>
</template>

<script lang="ts" setup>
import type { Ref, Component } from 'vue'
import { ref, computed } from 'vue'
import { onBeforeRouteLeave } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { useRouter, useRoute } from 'vue-router'
import { doAddShops, doEditShop, doShopAudit, doGetShopSigningCategoryList, doGetCategory } from '@/apis/shops'
import storage from '@/utils/Storage'
import { ShopFormType, BankAccountType, OPERATOR_STATUS } from './types'
import { cloneDeep } from 'lodash'
// 选择素材 e
import selectMaterial from '@/views/material/selectMaterial.vue'
const dialogVisible = ref(false)
const parameterId = ref<string>('')
const uploadFileLimit = computed(() => (parameterId.value === 'shopDetailsPhoto' ? 5 : parameterId.value === 'otherQualifications' ? 15 : 1))
/**
 * 选择素材弹窗切换
 */
const selectMaterialFn = (val: boolean) => {
    dialogVisible.value = val
    if (!val) {
        parameterId.value = ''
    }
}
interface AsyncComponent {
    NewShopBase: Component
    NewShopInfo: Component
    NewShopFinance: Component
}
type EnumStep = 'NewShopBase' | 'NewShopInfo' | 'NewShopFinance'
const componentRef = ref()
const $router = useRouter()
const $route = useRoute()
const firstAssignment = ref(true)
const submitLoading = ref(false)
// 供应商视图模式
const supplierViewModel = computed(() => ($route.path.includes('supplier') ? '供应商' : '店铺'))
const navBackUrl = computed(() => ($route.path.includes('supplier') ? 'supplierPage' : 'shopList'))
let isManual = false
// 当前步骤
const currentStep = ref<EnumStep>('NewShopBase')
// 动态组件列表
const reactiveComponent: AsyncComponent = {
    NewShopBase: defineAsyncComponent(() => import('./components/NewShopBase.vue')),
    NewShopInfo: defineAsyncComponent(() => import('./components/NewShopInfo.vue')),
    NewShopFinance: defineAsyncComponent(() => import('./components/NewShopFinance.vue')),
}
onMounted(() => {
    initShopForm()
})
// 商铺添加步骤
const stepIndicator = {
    NewShopBase: {
        prev: '',
        next: 'NewShopInfo',
        stepIndex: 0,
    },
    NewShopInfo: {
        prev: 'NewShopBase',
        next: 'NewShopFinance',
        stepIndex: 1,
    },
    NewShopFinance: {
        prev: 'NewShopInfo',
        next: '',
        stepIndex: 2,
    },
}
// 当前添加步骤
const currentStepIndicator = computed(() => {
    return stepIndicator[currentStep.value]
})

const stepIndex = computed(() => {
    return currentStepIndicator.value.stepIndex
})

const prevStep = computed(() => {
    return currentStepIndicator.value.prev
})

const nextStep = computed(() => {
    return currentStepIndicator.value.next
})

// 测试数据
const submitForm = ref<ShopFormType>({
    // companyName: '',
    address: '',
    bankAcc: '',
    bankAccount: {
        payee: '',
        bankName: '',
        openAccountBank: '',
        bankAccount: '',
        acctAttr: '0',
        bankReservePhone: '',
        openBankNo: '',
        payBankNumber: '',
        openBankProvince: '',
        openBankCity: '',
        openBankPC: ['', ''],
    },
    briefing: '',
    contractNumber: '',
    location: { type: 'Point', coordinates: ['121.583336', '29.990282'] },
    logo: '',
    name: '',
    registerInfo: {
        license: '',
        // handheldPhoto: '',
        legalPersonIdFront: '',
        legalPersonIdBack: '',
        enterpriseAdress: '',
        startDate: '',
        endDate: '',
        groupName: '',
        creditCode: '',
        validType: '',
        validityPeriod: [],
        legalPersonName: '',
        legalPersonNo: '',
        legalPersonPhone: '',
        addressCode: '',
        operatorStatus: OPERATOR_STATUS.NONE,
    },
    // shopTitelPhoto: '',
    // shopDetailsPhoto: [],
    otherQualifications: [],
    registerMobile: '',
    subjectType: 'COMPANY',
    shopType: 'ORDINARY',
    signingCategory: [],
    extractionType: 'CATEGORY_EXTRACTION',
    drawPercentage: '',
    mode: 'B2B2C',
    shopMode: 'COMMON',
    businessType: 'TRUSTEESHIP',
})
const handleMapChange = (e: { address: string; position: string[] }) => {
    // 编辑状态下初始化不对详细地址进行重写
    if ($route.query.shopId && firstAssignment.value) {
        firstAssignment.value = false
        return
    }
    submitForm.value.address = e.address
    submitForm.value.location.coordinates = e.position || []
}
const tempArr: Ref<FormInstance>[] = []
/**
 * @LastEditors: lexy
 * @description: 存储组件实例
 * @param {*} component
 */
const handleSaveComponent = (component: Ref<FormInstance>) => {
    if (!tempArr[stepIndex.value]) {
        tempArr.push(componentRef.value)
    }
}

const handleGetComponent = () => {
    if (tempArr[stepIndex.value]) {
        return tempArr[stepIndex.value]
    } else {
        return componentRef.value
    }
}

// 上一步
const preHandle = () => {
    handleSaveComponent(componentRef.value)
    currentStep.value = prevStep.value as EnumStep
}
// 下一步
const nextHandle = () => {
    if (submitForm.value.subjectType === 'PERSON') {
        submitForm.value.bankAccount.acctAttr = '0'
    }
    if (submitForm.value.subjectType === 'COMPANY') {
        submitForm.value.bankAccount.acctAttr = '1'
    }
    if (submitForm.value.bankAccount.acctAttr === '0') {
        submitForm.value.bankAccount.payee = submitForm.value.registerInfo.legalPersonName
    }
    if (submitForm.value.bankAccount.acctAttr === '1') {
        submitForm.value.bankAccount.payee = submitForm.value.registerInfo.groupName || ''
    }
    handleGetComponent().currentFormRef.validate((valid: any) => {
        if (valid) {
            handleSaveComponent(componentRef.value)
            currentStep.value = nextStep.value as EnumStep
        }
    })
}
const handleCancel = () => {
    $router.push({
        name: navBackUrl.value,
    })
}
const submitHandle = async () => {
    submitLoading.value = true
    const isEdit = !!$route.query.shopId
    const {
        registerInfo: { validType, validityPeriod, addressCodes /*, handheldPhoto */ },
        mode,
        // shopDetailsPhoto,
        otherQualifications,
        bankAccount,
    } = submitForm.value
    let formValues: any = { ...submitForm.value }

    if (validType === 'FIXED' && validityPeriod?.length > 0) {
        formValues.registerInfo.startDate = validityPeriod[0]
        formValues.registerInfo.endDate = validityPeriod[1]
    }

    if (otherQualifications?.length) {
        formValues.otherQualifications = formValues.otherQualifications.join(',')
    }
    if (bankAccount.openBankPC?.length < 2) {
        formValues.bankAccount.openBankCity = bankAccount.openBankProvince
    }
    if (addressCodes) {
        formValues.registerInfo.province = addressCodes[0] || ''
        formValues.registerInfo.city = addressCodes[1] || ''
        formValues.registerInfo.district = addressCodes[2] || ''
    }
    // if (shopDetailsPhoto?.length) {
    //     formValues.shopDetailsPhoto = formValues.shopDetailsPhoto.join(',')
    // }
    // if (!handheldPhoto) {
    //     ElMessage.error('请上传手持证件照！')
    // } else {
    //     formValues.handheldPhoto = handheldPhoto
    // }

    if (supplierViewModel.value === '供应商') {
        formValues.shopMode = 'SUPPLIER'
        formValues.mode = 'S2B2C'
    } else if (mode === 'B2B2C') {
        formValues.shopMode = 'COMMON'
    } else if (mode === 'O2O') {
        formValues.shopMode = 'O2O'
    }

    if (supplierViewModel.value === '供应商') {
        delete formValues.businessType
    }

    try {
        await handleGetComponent().currentFormRef.validate(async (valid: any) => {
            if (valid) {
                if (isEdit) {
                    switch ($route.query.type) {
                        case 'EDIT':
                            await handleEditShop(formValues)
                            break
                        default:
                            await throughApplication(formValues, $route.query.type as 'EDIT' | 'through')
                            break
                    }
                    return
                }
                const cloneParams = cloneDeep(formValues)
                cloneParams.signingCategory = cloneParams.signingCategory?.map((item: any) => ({
                    id: item.id,
                    parentId: item.parentId,
                    currentCategoryId: item.currentCategoryId,
                    customDeductionRatio: item.customDeductionRatio,
                }))
                const { code, msg } = await doAddShops(cloneParams).catch((err) => {
                    submitLoading.value = false
                    return err
                })
                if (code !== 200) {
                    submitLoading.value = false
                    if (code === 100003) {
                        return ElMessage.error(msg || '添加失败')
                    }
                    return ElMessage.error(msg || '添加失败')
                }
                let time = setTimeout(() => {
                    submitLoading.value = false
                    isManual = true
                    $router.push({
                        name: navBackUrl.value,
                    })
                    clearTimeout(time)
                }, 2000)
            } else {
                submitLoading.value = false
            }
        })
    } finally {
        submitLoading.value = false
    }
}
// @cropped-file-change="" 裁剪后返回的单个素材
// @checked-file-lists=""  选中素材返回的素材合集
const croppedFileChange = (val: string | string[]) => {
    if (!val) return
    if (parameterId.value === 'logo') {
        var img = typeof val === 'string' ? val : ''
        if (Array.isArray(val)) {
            img = val?.shift() || ''
        }
        submitForm.value.logo = img
    } else if (parameterId.value === 'shopDetailsPhoto' || parameterId.value === 'otherQualifications') {
        if (!submitForm.value[parameterId.value]) {
            submitForm.value[parameterId.value] = []
        }
        if (Array.isArray(val)) {
            submitForm.value[parameterId.value] = [...(submitForm.value[parameterId.value] || []), ...val]
        } else if (typeof val === 'string' && val) {
            submitForm.value[parameterId.value]?.push(val)
        }
    }
}
/**
 * @LastEditors: lexy
 * @description: 处理编辑 等待接收
 * @param {*} params
 * @returns {*}
 */
async function handleEditShop(params: any) {
    const cloneParams = cloneDeep(params)
    cloneParams.signingCategory = cloneParams.signingCategory?.map((item: any) => ({
        id: item.id,
        parentId: item.parentId,
        currentCategoryId: item.currentCategoryId,
        customDeductionRatio: item.customDeductionRatio,
    }))
    const { code, success, msg } = await doEditShop(cloneParams)
    if (code === 200 && success === true) {
        ElMessage.success('更新成功')
        isManual = true
        $router.push({
            name: navBackUrl.value,
        })
    } else {
        ElMessage.error(msg)
    }
}
/**
 * @LastEditors: lexy
 * @description: 通过审核
 * @param {*} params
 * @param {*} type
 * @returns {*}
 */
async function throughApplication(params: any, type: 'EDIT' | 'through') {
    if (type === 'through') {
        const { code: c } = await doEditShop(submitForm.value)
        if (c !== 200) return ElMessage.error('通过申请失败')
        const { code, success } = await doShopAudit(params, true)
        if (code !== 200) return ElMessage.error('通过申请失败')
        isManual = true
        $router.push({
            name: navBackUrl.value,
        })
    }
}
async function initShopForm() {
    if ($route.query.shopId) {
        const SHOPITEM = new storage().getItem('SHOPITEM')
        const { registerInfo, bankAccount, address, /* handheldPhoto, shopDetailsPhoto,*/ otherQualifications } = SHOPITEM || {}
        submitForm.value = {
            ...SHOPITEM,
            // shopDetailsPhoto: shopDetailsPhoto?.indexOf(',') > -1 ? shopDetailsPhoto.split(',') : [],
            otherQualifications: otherQualifications?.indexOf(',') > -1 ? otherQualifications.split(',') : [],
        }
        if (registerInfo) {
            const { startDate, endDate, addressCode } = registerInfo
            if (startDate || endDate) {
                await nextTick(() => {
                    submitForm.value.registerInfo.validityPeriod = [startDate, endDate]
                    // submitForm.value.registerInfo.handheldPhoto = handheldPhoto
                })
            }
            if (addressCode) {
                if (addressCode === '710000') {
                    submitForm.value.registerInfo.addressCodes = [addressCode]
                } else {
                    const proviceCode = addressCode.substring(0, 2)
                    // if (['11', '12', '31', '50', '81', '82'].includes(proviceCode)) {
                    //     await nextTick(() => {
                    //         submitForm.value.registerInfo.addressCodes = [proviceCode + '0000', addressCode]
                    //     })
                    // } else {
                    const cityCode = addressCode.substring(0, 4)
                    await nextTick(() => {
                        submitForm.value.registerInfo.addressCodes = [proviceCode + '0000', cityCode + '00', addressCode]
                    })
                    // }
                }
            }
        }

        if (bankAccount) {
            for (let item in bankAccount) {
                if (item !== 'bankAccount') {
                    submitForm.value[item as keyof BankAccountType] = bankAccount[item]
                }
            }
            if (bankAccount.openBankProvince) {
                submitForm.value.bankAccount.openBankPC = [bankAccount.openBankProvince]
                if (bankAccount.openBankCity && bankAccount.openBankCity !== bankAccount.openBankProvince) {
                    submitForm.value.bankAccount.openBankPC = [bankAccount.openBankProvince, bankAccount.openBankCity]
                }
            }
            submitForm.value['address'] = address
            // 结局bankAccount重名问题 重新赋值
            submitForm.value['bankAcc'] = bankAccount.bankAccount
        }

        if (!submitForm.value['subjectType']) {
            submitForm.value['subjectType'] = 'COMPANY'
            submitForm.value['bankAccount']['acctAttr'] = '1'
        }

        // if (handheldPhoto) {
        //     await nextTick(() => {
        //         submitForm.value.registerInfo.handheldPhoto = handheldPhoto
        //     })
        // }
        // 获取对应的类目列表
        const { code, success, data } = await doGetShopSigningCategoryList({ shopId: submitForm.value.id || '' })
        if (success) {
            const signingCategoryList = data?.map((item) => ({
                id: item.id,
                firstName: item.parentName,
                parentId: item.parentId,
                customDeductionRatio: item.customDeductionRatio,
                name: item.currentCategoryName,
                currentCategoryId: item.currentCategoryId,
                deductionRatio: item.deductionRatio,
                supplierDeductionRatio: item.supplierDeductionRatio,
            }))
            submitForm.value.signingCategory = signingCategoryList
            if (!submitForm.value.registerMobile) {
                submitForm.value.registerMobile = submitForm.value.userMobile
            }
        }
    } else {
        const { data, success } = await doGetCategory({
            current: 1,
            size: 1000,
        })
        if (success) {
            const signingCategoryList = data.records?.flatMap(({ name, secondCategoryVos }) =>
                secondCategoryVos.map((item) => ({
                    firstName: name,
                    parentId: item.parentId,
                    customDeductionRatio: '',
                    name: item.name,
                    currentCategoryId: item.id,
                    deductionRatio: item.deductionRatio,
                    supplierDeductionRatio: item.supplierDeductionRatio,
                })),
            )
            submitForm.value.signingCategory = signingCategoryList
        }
    }
}
// 暴露属性
provide('addShops', {
    submitForm,
    dialogVisible,
    parameterId,
})
// FIXME 操作浏览器回退无法生成Message组件
// 离开页面提示
onBeforeRouteLeave((to, from, next) => {
    if (!isManual) {
        ElMessageBox.confirm('确定退出信息编辑页面?退出后，未保存的信息将不会保留!', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })
            .then(() => {
                next()
            })
            .catch(() => {
                next(false)
            })
    } else {
        next()
    }
})
</script>
<style lang="scss">
@include b(shopForm) {
    overflow-y: auto;
    overflow-x: hidden;
    // min-height: 800px;
    @include e(tool) {
        width: 100%;
        @include flex();
        bottom: 10px;
        padding: 15px 0px;
        display: flex;
        justify-content: center;
        box-shadow: 0 0px 10px 0px #d5d5d5;
        background-color: white;
        z-index: 999;
        position: sticky;
        bottom: 0;
    }
    .shopForm__steps {
        position: sticky;
        top: 0;
        z-index: 1000;
    }
}
</style>
