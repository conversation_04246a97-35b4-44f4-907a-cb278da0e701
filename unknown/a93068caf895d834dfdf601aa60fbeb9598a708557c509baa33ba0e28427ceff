<template>
    <el-dialog v-model="_isShow" :title="`${title}词库`" center destroy-on-close @open="handleChangeShow(true)" @close="handleChangeShow(false)">
        <el-form ref="formRef" :model="formData" label-width="80px" :rules="rules">
            <el-form-item prop="content" label="评价词">
                <el-input v-model="formData.content" :maxlength="100" show-word-limit type="textarea" />
            </el-form-item>
            <el-form-item v-if="props.relateType === 'PRODUCT'" prop="productId" label="关联商品" required>
                <q-page-select
                    v-model="formData.product"
                    placeholder="请选择一个关联商品"
                    :options="productOptions"
                    :total="total"
                    remote
                    clearable
                    filterable
                    remote-show-suffix
                    :loading="remoteLoading"
                    :page-info="{
                        size: 10,
                    }"
                    :option-key="'id'"
                    :label-key="'productName'"
                    :value-key="'id'"
                    :remote-method="(pageConfig) => handlePageChange(pageConfig, 'product')"
                    @page-change="(pageConfig) => handlePageChange(pageConfig, 'product')"
                    @change="(val: any) => changeProduct(val)"
                >
                    <template #option="{ item }">
                        <el-space>
                            <el-image :src="item.albumPics?.split(',')[0]" fit="contain" style="width: 24px; height: 24px" />
                            <el-text>{{ item.productName }}</el-text>
                            <el-tag v-if="item.status.includes('SELL_OFF')" type="danger">
                                {{ item.status === 'ILLEGAL_SELL_OFF' ? '违规下架' : '已下架' }}
                            </el-tag>
                        </el-space>
                    </template>
                </q-page-select>
            </el-form-item>
            <el-form-item v-if="props.relateType === 'CATEGORY'" prop="categoryId" label="关联类目" required>
                <el-cascader
                    ref="platformCategoryRef"
                    v-model="formData.category"
                    placeholder="请选择关联类目"
                    :options="categoryList"
                    :props="cascaderProps"
                    :show-all-levels="false"
                    clearable
                    @change="changeCategory"
                />
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" @click="confirmLexicon">确定</el-button>
                <el-button @click="handleChangeShow(false)">取消</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { doGetSupplierList } from '@/apis/good'
import {
    doGetEvaluateLexiconDetail,
    doPostEvaluateLexicon,
    doPutEvaluateLexiconDetail,
    LEXICON_RELATE_TYPE,
    LEXICON_TYPE,
} from '@/apis/order/evaluationLexicon'
import { doGetCategory } from '@/apis/shops'
import { useVModel } from '@vueuse/core'
import { ElMessage } from 'element-plus'
import { PropType } from 'vue'

const props = defineProps({
    modelValue: {
        type: Boolean,
        required: true,
    },
    action: {
        type: String as PropType<'add' | 'edit'>,
        default: 'add',
    },
    type: {
        type: String as PropType<keyof typeof LEXICON_TYPE>,
        default: 'USER',
    },
    relateType: {
        type: String as PropType<keyof typeof LEXICON_RELATE_TYPE>,
        default: 'PRODUCT',
    },
    id: {
        type: String,
        required: false,
    },
})
const emits = defineEmits(['update:modelValue', 'submitSuccess'])
const _isShow = useVModel(props, 'modelValue', emits)
const formRef = ref()
const formData = ref<{
    content: string
    product?: any
    category?: string[]
    productId: string
    categoryId: string
}>({
    content: '',
    productId: '',
    categoryId: '',
})
const title = computed(() => (props.action === 'edit' ? '编辑' : '添加'))
const remoteLoading = ref<boolean>(false)
const productOptions = ref([])
const total = ref<number>(0)

const rules = reactive({
    content: [{ required: true, message: '请输入关键词' }],
    productId: [{ required: true, message: '请选择商品' }],
    categoryId: [{ required: true, message: '请选择类目' }],
})

const categoryList = ref<any[]>([])
const cascaderProps: CascaderProps = {
    expandTrigger: 'hover',
    label: 'name',
    value: 'id',
    checkStrictly: true,
}

/**
 * 获取词库详情
 * @param id
 */
const initLexiconDetail = (id: string) => {
    doGetEvaluateLexiconDetail(id).then((res) => {
        if (res.data) {
            formData.value = res.data as any
            if (res.data.productId) {
                formData.value.product = { id: res.data.productId, productName: res.data.productName }
            }
        }
    })
}

/**
 * 变更弹窗展示
 */
const handleChangeShow = async (show: boolean) => {
    _isShow.value = show
    if (_isShow.value) {
        if (props.action === 'add') {
            formData.value.content = ''
            formData.value.categoryId = ''
            formData.value.productId = ''
            formData.value.category = undefined
            formData.value.product = undefined
        }
        if (props.action === 'edit' && props.id) {
            initLexiconDetail(props.id)
        }
        if (props.relateType === 'CATEGORY') {
            await initCategoryList()
        }
    }
}

/**
 * @LastEditors: lexy
 * @description:初始化列表
 * @param {*} list target
 * @param {*} str attribute
 * @returns {*}
 */
function initList(list: any[], str: string) {
    list.forEach((item) => {
        if (item.id === formData.value.categoryId) {
            if (str === 'secondCategoryVos') {
                formData.value.category = [item.id]
            } else if (str === 'categoryThirdlyVos') {
                formData.value.category = [item.parentId, item.id]
            }
        }
        if (item[str]) {
            item.children = item[str]
            delete item[str]
            if (item.children.length) {
                initList(item.children, 'categoryThirdlyVos')
            }
        }
    })
}
/**
 * 获取类目数据
 */
async function initCategoryList() {
    const { code, data } = await doGetCategory({ current: 1, size: Number.MAX_SAFE_INTEGER })
    if (code === 200 && data?.records?.length) {
        initList(data.records, 'secondCategoryVos')
        categoryList.value = data.records
    } else {
        ElMessage.error('获取商品分类失败')
    }
}

/**
 * 下拉框翻页
 * @param pageConfig
 */
const handlePageChange = async ({ pageNum, pageSize, ...pageConfig }: { pageNum: number; pageSize: number; query?: string }, key: string) => {
    remoteLoading.value = true
    let params = {
        ...pageConfig,
        current: pageNum,
        size: pageSize,
    }

    doGetSupplierList(params).then(({ code, success, data }) => {
        remoteLoading.value = false
        if (code === 200 && success) {
            productOptions.value = data?.records || []
            total.value = data?.total
        }
    })
}

/**
 * 修改关联商品
 * @param val string
 */
const changeProduct = (val: any) => {
    formData.value.product = val
    formData.value.productId = val.id
}

/**
 * 修改关联类目
 * @param val string[]
 */
const changeCategory = (val: string[]) => {
    formData.value.category = val
    formData.value.categoryId = val[val.length - 1]
}

/**
 * 提交词库
 */
const confirmLexicon = () => {
    formRef.value?.validate((valid) => {
        if (valid) {
            const otherKey = props.relateType === 'PRODUCT' ? 'productId' : 'categoryId'
            if (props.action === 'edit' && !!props.id) {
                doPutEvaluateLexiconDetail(props.id, props.type, props.relateType, formData.value.content, otherKey, formData.value[otherKey]).then(
                    (res) => {
                        if (res.code === 200) {
                            _isShow.value = false
                            ElMessage.success('词库更新成功！！')
                            emits('submitSuccess')
                        }
                    },
                )
            } else {
                doPostEvaluateLexicon(props.type, props.relateType, formData.value.content, otherKey, formData.value[otherKey]).then((res) => {
                    if (res.code === 200) {
                        _isShow.value = false
                        ElMessage.success('新增词库成功！！')
                        emits('submitSuccess')
                    }
                })
            }
        }
    })
}
</script>
