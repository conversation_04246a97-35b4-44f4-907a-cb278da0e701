<!--
 * @description: 提现工单
 * @Author: lexy
 * @Date: 2022-11-24 14:10:43
 * @LastEditors: lexy
 * @LastEditTime: 2023-09-19 11:19:07
-->
<script setup lang="ts">
import MCard from '@/components/MCard.vue'
import QTable from '@components/qszr-core/packages/q-table/QTable'
import QTableColumn from '@components/qszr-core/packages/q-table/q-table-column.vue'
import PageManage from '@components/PageManage.vue'
import { doGetWithdrawList, doGetCheckWithdraw } from '@/apis/finance'
import RemarkPopup from '@/components/remark/remark-popup.vue'
import RemarkFlag from '@/components/remark/remark-flag.vue'
import { ElMessage } from 'element-plus'
import useClipboard from 'vue-clipboard3'
import { doPostExportWithdrawData } from '@/apis/overview'
/*
 *variable
 */
const { divTenThousand } = useConvert()
const { toClipboard } = useClipboard()
const cardFlag = ref(false)
const tempDate = ref([])
const searchForm = reactive({
    name: '',
    id: '',
    startDate: '',
    endDate: '',
    status: 'APPLYING',
    type: null,
})
const pageConfig = reactive({
    current: 1,
    size: 10,
    total: 0,
})
const remarkValue = ref('')
const saveRemarkType = ref('single')
const remarkDialog = ref(false)
const selectOption = [
    {
        label: '全部',
        value: null,
    },
    {
        label: '交易提现(店铺)',
        value: 'SHOP',
    },
    {
        label: '交易提现(供应商)',
        value: 'SUPPLIER',
    },
    {
        label: '佣金提现(分销商)',
        value: 'DISTRIBUTOR',
    },
    {
        label: '返利提现(用户)',
        value: 'REBATE',
    },
]
const currentNo = ref('')
const withdrawList = ref([])
const tableSelectedArr = ref([])
const ids = ref<string[]>([])
// 审核表单
const checkForm = reactive({
    showDialog: false,
    pass: false,
    offline: false,
    reason: '',
    isAgree: false,
})
// 银行卡不支持线上打款
const notSupportOnline = ref(false)
/*
 *lifeCircle
 */
initList()
/*
 *function
 */
const handleChangeCurrent = (e: number) => {
    pageConfig.current = e
    initList()
}
const handleChangeSize = (e: number) => {
    pageConfig.current = 1
    pageConfig.size = e
    initList()
}
const handleChangeTab = () => {
    initList()
}
const handleSearch = () => {
    if (tempDate.value.length) {
        searchForm.startDate = tempDate.value[0]
        searchForm.endDate = tempDate.value[1]
    }
    initList()
}

/**
 * @LastEditors: lexy
 * @description: 关闭审核弹窗回调
 * @returns {*}
 */
const handleCloseCheckDialog = () => {
    checkForm.reason = ''
    checkForm.offline = false
}
const handleChangeSelect = () => {
    initList()
}
/**
 * @LastEditors: lexy
 * @description: 重置搜索条件
 */
const handleReset = () => {
    searchForm.type = null
    searchForm.endDate = ''
    searchForm.startDate = ''
    searchForm.id = ''
    searchForm.name = ''
    tempDate.value = []
    initList()
}
/**
 * @LastEditors: lexy
 * @description: 显示备注弹窗记录no
 */
const handleShowRemark = (row: { no: string; remark: string }) => {
    ids.value = [row.no]
    if (row.remark) {
        remarkValue.value = row.remark
    }
    saveRemarkType.value = 'single'
    remarkDialog.value = true
}
/**
 * @LastEditors: lexy
 * @description: 批量备注
 * @returns {*}
 */
const handleBatchRemark = () => {
    if (tableSelectedArr.value.length) {
        ids.value = tableSelectedArr.value.map((item) => item.no)
        saveRemarkType.value = 'batch'
        remarkDialog.value = true
        return
    }
    ElMessage.error('请先选择订单')
}
const handlePass = (no: string, status: string) => {
    // 判断结算是否支持线上打款
    notSupportOnline.value = status === 'BANK_CARD'
    checkForm.offline = status === 'BANK_CARD' ? true : false
    checkForm.showDialog = true
    checkForm.isAgree = true
    checkForm.pass = true
    currentNo.value = no
}
const handleRefuse = (no: string) => {
    checkForm.showDialog = true
    checkForm.isAgree = false
    checkForm.pass = false
    currentNo.value = no
}
const handleCheckConfirm = async () => {
    if (!checkForm.isAgree && !checkForm.reason) return ElMessage.error({ message: '请填写拒绝说明' })
    const { code, msg } = await doGetCheckWithdraw(currentNo.value, {
        pass: checkForm.pass,
        offline: checkForm.offline,
        reason: checkForm.reason,
    })
    if (code === 200) {
        ElMessage.success('操作成功')
        checkForm.showDialog = false
        initList()
    } else {
        ElMessage.error(msg ? msg : '操作失败')
    }
}
async function initList() {
    const { code, data } = await doGetWithdrawList({ ...searchForm, ...pageConfig })
    if (code && code === 200) {
        withdrawList.value = data.records
        pageConfig.total = data.total
    } else {
        ElMessage.error('获取列表失败')
    }
}
function convertType(val: string) {
    const type: { [x: string]: string } = {
        ALIPAY: '支付宝',
        WECHAT: '微信',
        BANK_CARD: '银行卡',
    }
    return type[val]
}
function convertStatus(val: string) {
    const statusType: { [x: string]: string } = {
        APPLYING: '待审核',
        SUCCESS: '已到账',
        CLOSED: '已拒绝',
        FORBIDDEN: '已拒绝',
    }
    return statusType[val]
}
const convertOwnerType = (val: string) => {
    const ownerTypeMap: { [x: string]: string } = {
        CONSUMER: '普通会员',
        DISTRIBUTOR: '佣金提现(分销商)',
        SHOP: '交易提现(店铺)',
        REBATE: '返利提现(用户)',
        SUPPLIER: '交易提现(供应商)',
    }
    return ownerTypeMap[val]
}
const handleRemarkSuccess = () => {
    tableSelectedArr.value = []
    initList()
}
const handleCopy = async (drawType: any) => {
    let str = ''
    if (drawType?.type === 'WECHAT') {
        str = `openid：${drawType?.openid}`
    } else if (drawType?.type === 'ALIPAY') {
        str = `姓名：${drawType?.name}\n支付宝账号：${drawType?.alipayAccount}`
    } else {
        str = `持卡人：${drawType?.name}\n开户行：${drawType?.bank}\n银行卡号：${drawType?.cardNo}`
    }
    try {
        await toClipboard(str)
        ElMessage.success('复制成功')
    } catch (e) {
        ElMessage.error('复制失败')
    }
}
// 导出
const handleExport = async () => {
    let params: any = {}
    if (tableSelectedArr.value.length) {
        params.exportNos = tableSelectedArr.value.map((item) => item.no)
    } else {
        searchForm.startDate = tempDate.value[0]
        searchForm.endDate = tempDate.value[1]
        params = searchForm
    }
    const { code, msg } = await doPostExportWithdrawData(params)
    if (code === 200) {
        ElMessage.success({ message: msg || '导出成功' })
    } else {
        ElMessage.error({ message: msg || '导出失败' })
    }
}
</script>

<template>
    <div style="background: #f9f9f9">
        <m-card v-model="cardFlag">
            <el-form ref="ruleForm" :model="searchForm">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="姓名" prop="name" label-width="50px">
                            <el-input v-model="searchForm.name" placeholder="请填写姓名检索" maxlength="20"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="申请编号" prop="id" label-width="90px">
                            <el-input v-model="searchForm.id" placeholder="请填写申请编号" maxlength="30"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="申请时间" prop="date" label-width="90px">
                            <el-date-picker
                                v-model="tempDate"
                                type="daterange"
                                range-separator="-"
                                start-placeholder="开始时间"
                                value-format="YYYY-MM-DD"
                                end-placeholder="结束时间"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row style="margin-left: 30px">
                    <el-col :span="8">
                        <el-button type="primary" round @click="handleSearch">搜索</el-button>
                        <el-button round @click="handleReset">重置</el-button>
                        <el-button round type="primary" @click="handleExport">导出</el-button>
                    </el-col>
                </el-row>
            </el-form>
        </m-card>
    </div>
    <div class="grey_bar"></div>
    <div class="tab_container">
        <el-tabs v-model="searchForm.status" class="demo-tabs" style="margin: 10px 0 0 0" @tab-change="handleChangeTab">
            <el-tab-pane label="待审核" name="APPLYING" />
            <el-tab-pane label="已打款" name="SUCCESS" />
            <el-tab-pane label="已拒绝" name="FORBIDDEN" />
        </el-tabs>
    </div>
    <div class="handle_container">
        <div class="withdraw__tool">
            <div class="withdraw__tool--btns">
                <el-button round type="primary" plain @click="handleBatchRemark">批量备注</el-button>
            </div>
            <el-select v-model="searchForm.type" class="m-2" placeholder="请选择" @change="handleChangeSelect">
                <el-option v-for="item in selectOption" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
        </div>
    </div>
    <q-table
        v-model:checked-item="tableSelectedArr"
        :data="withdrawList"
        :selection="true"
        class="withdraw__table"
        :class="{ 'withdraw__table-up': cardFlag }"
    >
        <template #header="{ row }">
            <div class="header">
                <div>
                    <span>申请编号：{{ row.no }}</span>
                    <span style="margin-left: 40px">申请时间：{{ row.createTime }}</span>
                    <span v-if="['CLOSED', 'FORBIDDEN', 'SUCCESS'].includes(row?.status)" style="margin-left: 40px">
                        审批时间：{{ row.tradeTime }}
                    </span>
                </div>
                <remark-flag :content="row.remark" @see-remark="handleShowRemark(row)" />
            </div>
        </template>
        <q-table-column label="基本信息" :style="{ justifyContent: 'start' }">
            <template #default="{ row }">
                <div class="info">
                    <el-image class="info__img" :src="row.ownerAvatar" />
                    <div class="info__right">
                        <div class="info__right--name ellipsis">{{ row.ownerName }}</div>
                        <div>{{ row.contract }}</div>
                    </div>
                </div>
            </template>
        </q-table-column>
        <q-table-column label="提现金额">
            <template #default="{ row }">
                <div>{{ divTenThousand(row.drawType.amount) }}</div>
            </template>
        </q-table-column>
        <q-table-column label="提现到">
            <template #default="{ row }">
                <el-popover :width="250">
                    <template #reference>
                        <el-link type="primary">{{ convertType(row.drawType.type) }}</el-link>
                    </template>
                    <div class="draw-type">
                        <div class="draw-type-copy" @click="handleCopy(row.drawType)">复制</div>
                        <template v-if="row.drawType.type === 'WECHAT'">
                            <p class="draw-type-line">openid：{{ row?.drawType?.openid }}</p>
                        </template>
                        <template v-else-if="row.drawType.type === 'ALIPAY'">
                            <p class="draw-type-line">姓名：{{ row?.drawType?.name }}</p>
                            <p class="draw-type-line">支付宝账号：{{ row?.drawType?.alipayAccount }}</p>
                        </template>
                        <template v-else>
                            <p class="draw-type-line">持卡人：{{ row?.drawType?.name }}</p>
                            <p class="draw-type-line">开户行：{{ row?.drawType?.bank }}</p>
                            <p class="draw-type-line">银行卡号：{{ row?.drawType?.cardNo }}</p>
                        </template>
                    </div>
                </el-popover>
            </template>
        </q-table-column>
        <q-table-column label="提现类型">
            <template #default="{ row }">
                <div>{{ convertOwnerType(row.ownerType) }}</div>
            </template>
        </q-table-column>
        <q-table-column v-if="searchForm.status === 'FORBIDDEN'" label="拒绝说明">
            <template #default="{ row }">
                <div>{{ row.reason }}</div>
            </template>
        </q-table-column>
        <q-table-column v-if="searchForm.status === 'APPLYING'" label="操作">
            <template #default="{ row }">
                <el-button type="primary" plain round @click="handlePass(row.no, row.drawType.type)">结算</el-button>
                <el-button type="danger" plain round @click="handleRefuse(row.no)">拒绝</el-button>
            </template>
        </q-table-column>
    </q-table>
    <page-manage
        :page-num="pageConfig.current"
        :page-size="pageConfig.size"
        :total="pageConfig.total"
        @handle-current-change="handleChangeCurrent"
        @handle-size-change="handleChangeSize"
    />
    <remark-popup
        v-model:isShow="remarkDialog"
        v-model:ids="ids"
        v-model:remark="remarkValue"
        remark-type="WITHDRAW"
        @success="handleRemarkSuccess"
    />
    <!-- 审核弹窗 -->
    <el-dialog v-model="checkForm.showDialog" :title="checkForm.isAgree ? '提现审核' : '拒绝说明'" @close="handleCloseCheckDialog">
        <el-form>
            <el-form-item v-if="checkForm.isAgree" label="打款方式">
                <el-radio-group v-model="checkForm.offline">
                    <el-radio :label="false" :disabled="notSupportOnline">线上打款</el-radio>
                    <el-radio :label="true">线下打款</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item v-else>
                <el-input
                    v-model="checkForm.reason"
                    maxlength="200"
                    :autosize="{ minRows: 5, maxRows: 5 }"
                    type="textarea"
                    placeholder="请输入不超过200个字符"
                />
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button @click="checkForm.showDialog = false">取消</el-button>
            <el-button type="primary" @click="handleCheckConfirm"> 确定 </el-button>
        </template>
    </el-dialog>
</template>

<style lang="scss" scoped>
@include b(withdraw) {
    @include e(tool) {
        height: 40px;
        margin-bottom: 10px;
        @include flex(space-between);
    }

    @include e(table) {
        // height: calc(100vh - 350px);
        height: calc(100vh - 310px);
        overflow-y: auto;
        transition: height 0.5s;
        scrollbar-width: none;
        -ms-overflow-style: none;

        &::-webkit-scrollbar {
            display: none;
        }
    }

    @include e(table-up) {
        height: calc(100vh - 450px);
    }
}

@include b(header) {
    width: 100%;
    font-size: 12px;
    color: #000;
    @include flex(space-between);
}

@include b(info) {
    @include flex;

    @include e(img) {
        width: 68px;
        height: 68px;
        margin-right: 6px;
        flex-shrink: 0;
    }

    @include e(right) {
        @include m(name) {
            margin-bottom: 4px;
        }
    }
}

.ellipsis {
    @include utils-ellipsis(2);
    white-space: wrap;
}

.draw-type {
    position: relative;

    &-copy {
        color: #1890ff;
        text-align: right;
        padding-bottom: 8px;
        cursor: pointer;
    }
}
</style>
