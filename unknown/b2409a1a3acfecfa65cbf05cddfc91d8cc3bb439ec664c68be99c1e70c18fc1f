<template>
    <search @change-show="changeSearchShow" @search="handleSearch">
        <el-button round type="primary" @click="handleExport">导出</el-button>
    </search>
    <div class="grey_bar"></div>
    <div class="operate-bar handle_container">
        <el-button type="primary" size="small" @click="handleOpenRemarkDialog">备注</el-button>
        <el-icon class="operate-bar__icon" @click="showDescriptionDialog = true"><QuestionFilled /></el-icon>
    </div>
    <div class="table_container">
        <el-table
            :data="memberRecordsData"
            :header-cell-style="{
                'background-color': '#F6F8FA',
                'font-weight': 'normal',
                color: '#515151',
            }"
            :height="tableHeight"
            @selection-change="handleSelectionChange"
        >
            <el-table-column type="selection" width="55" fixed="left" />
            <el-table-column label="流水编号" prop="no" width="180" />
            <el-table-column label="用户昵称" prop="userNickName" width="130" />
            <el-table-column label="手机号" prop="userPhone" width="120" />
            <el-table-column label="操作类型" prop="operatorTypeStr" width="100" />
            <el-table-column label="变动金额" prop="amountStr" />
            <el-table-column label="期后金额" prop="afterAmountStr" width="150" />
            <el-table-column label="关联订单" prop="orderNo" width="200" />
            <el-table-column label="操作人" prop="operatorUserNickName" width="130" />
            <el-table-column label="操作时间" prop="createTime" width="180" />
            <el-table-column label="备注" fixed="right">
                <template #default="{ row }">
                    <el-link v-if="row?.remark" type="primary" @click="handlePreviewRemark(row?.remark)">查看</el-link>
                </template>
            </el-table-column>
        </el-table>
    </div>
    <page-manage
        :page-size="pagination.pageSize"
        :page-num="pagination.current"
        :total="pagination.total"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
    />
    <el-dialog v-model="showDescriptionDialog" title="储值流水说明" :width="800">
        <description-dialog />
    </el-dialog>

    <el-dialog v-model="remarkDialog.show" title="备注" :width="550">
        <el-input v-model="remarkDialog.remark" type="textarea" placeholder="请填写备注信息" :maxlength="50" :rows="4" />
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="closeRemarkDialog">取消</el-button>
                <el-button type="primary" @click="confirmRemarkDialog">确认</el-button>
            </span>
        </template>
    </el-dialog>
    <el-dialog v-model="remarkPreviewDialog.show" title="备注" :width="550">
        <span>{{ remarkPreviewDialog.remark }}</span>
    </el-dialog>
</template>

<script lang="ts" setup>
import { QuestionFilled } from '@element-plus/icons-vue'
import search from './search.vue'
import PageManage from '@/components/PageManage.vue'
import descriptionDialog from './description-dialog.vue'
import { doGetUserBalanceList, doPostBatchRemarkUserBalance, doPostExportUserBalanceList } from '@/apis/vip'
import { ElMessage } from 'element-plus'
const tableHeight = ref('calc(100vh - 275px)')
const memberRecordsData = ref<any[]>([])
const selectedRecordsData = ref<any[]>([])
const showDescriptionDialog = ref(false)
const searchData = reactive({
    orderNo: '',
    userNickName: '',
    userPhone: '',
    operatorType: '',
    no: '',
    operatorStartTime: '',
    operatorEndTime: '',
})
const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
})

const remarkPreviewDialog = reactive({
    show: false,
    remark: '',
})

const remarkDialog = reactive({
    show: false,
    remark: '',
})
const handleCurrentChange = (current: number) => {
    pagination.current = current
    initialData()
}
const handleSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize
    pagination.current = 1
    initialData()
}
const handleSelectionChange = (selectedData: any[]) => {
    selectedRecordsData.value = selectedData
}
const changeSearchShow = (searchShow: boolean) => {
    tableHeight.value = `calc(100vh - ${searchShow ? 343 : 273}px)`
}
const handleSearch = (searchOptions: typeof searchData) => {
    Object.keys(searchData).forEach((key) => {
        // @ts-ignore
        searchData[key] = searchOptions[key] || ''
    })
    initialData()
}
const initialData = async () => {
    let datas: any[] = [],
        total = 0
    try {
        const { data, code, msg } = await doGetUserBalanceList({ ...searchData, current: pagination.current, size: pagination.pageSize })
        if (code === 200) {
            datas = data?.records || []
            total = +data?.total
        }
    } finally {
        memberRecordsData.value = datas
        pagination.total = total
    }
}

initialData()

const handleExport = async () => {
    let params: any = {}
    if (selectedRecordsData.value.length) {
        params.exportIds = selectedRecordsData.value.map((item) => item.id)
    } else {
        params = searchData
    }
    const { code, msg } = await doPostExportUserBalanceList(params)
    if (code === 200) {
        ElMessage.success({ message: msg || '导出成功' })
    } else {
        ElMessage.error({ message: msg || '导出失败' })
    }
}

const handleOpenRemarkDialog = () => {
    if (!selectedRecordsData.value.length) return ElMessage.error({ message: '请选择备注记录' })
    remarkDialog.show = true
}
const closeRemarkDialog = () => {
    remarkDialog.remark = ''
    remarkDialog.show = false
}
const confirmRemarkDialog = async () => {
    const ids = selectedRecordsData.value?.map((item) => item.id) || []
    const remark = remarkDialog.remark
    const { code, msg } = await doPostBatchRemarkUserBalance({ ids, remark })
    if (code === 200) {
        ElMessage.success({ message: msg || '备注成功' })
        closeRemarkDialog()
        initialData()
    } else {
        ElMessage.success({ message: msg || '备注失败' })
    }
}

const handlePreviewRemark = (info: string) => {
    remarkPreviewDialog.remark = info
    remarkPreviewDialog.show = true
}
</script>

<style lang="scss" scoped>
@include b(operate-bar) {
    @include flex(space-between, center);
    margin: 10px 0;
    @include e(icon) {
        font-size: 28px;
    }
}
</style>
