/*
 * @description: 会员专享配置项
 * @Author: lexy
 * @Date: 2025-02-28 10:34:09
 * @LastEditors: lexy
 * @LastEditTime: 2025-02-28 18:43:34
 */
import { JoinMemberType } from '@/apis/decoration/type'
/**
 * @LastEditors: lexy
 * @description: 头部设置
 * @param  showHeader 是否显示头部 0 无 1 有
 * @param  bgStyle  背景风格 bgStyleType
 * @param  titleStyle 标题类型 titleStyleType
 * @param  headerRight  头部右侧 headerRightType
 * @description: 商品列表
 * @param  showCategory 是否显示分类 1 显示 0 隐藏
 * @param  listStyle 展示方式 1 单列展示 2两列展示（纵向）
 * @description: 商品设置
 * @param  goodNum 商品数量 0 代表无限加载
 * @param  showInfo 展示信息 1 商品名称 2 抢购进度 3 商品价格 4 商品原价 5 截止时间
 * @param  showBtn 是否显示按钮 1 显示 0 隐藏
 */
export default {
    showHeader: 1,
    bgStyle: {
        type: 2,
        startColor: '#fff',
        endColor: '#fff',
        deg: 0,
        url: '',
    },
    titleStyle: {
        type: 2,
        text: '限时特卖',
        url: '',
    },
    headerRight: {
        iconUrl: 'iconfont icon-youjiantou_huaban1',
        text: '更多',
    },
    showCategory: 0,
    listStyle: 1,
    goodNum: 3,
    showInfo: [1, 2, 3, 4, 5, 6],
    showBtn: 1,
}

/**
 * @LastEditors: lexy
 * @description: 头部右侧
 * @param  text       文字  1-4个字符
 * @param  iconUrl    图片地址
 */
export type headerRightType = {
    text?: string
    iconUrl?: string
}

/**
 * @LastEditors: lexy
 * @description: 标题类型
 * @param  type       1图片  2文字
 * @param  url        图片地址
 * @param  text       文字  1-6个字符
 */
export type titleStyleType = {
    type: 1 | 2
    text?: string
    url?: string
}

/**
 * @LastEditors: lexy
 * @description: 背景色
 * @param  type       1背景色  2背景图
 * @param  startColor 开始颜色
 * @param  endColor   结束颜色
 * @param  deg        角度
 * @param  url        背景图地址
 */
export type bgStyleType = {
    type: 1 | 2
    startColor?: string
    endColor?: string
    deg?: number
    url?: string
}

export type OnlyPromotionGoodSkuType = {
    productId: string
    actualPaidPrice: number
    skuStock: number
    stockType: StockType
    commission: number
    stock: number
    joinMember: JoinMemberType[]
    endTime: string
    maxPrice: string
    minPrice: string
    onlyId: string
    productName: string
    productPic: string
    shopId: string
    desc: string
    startTime: string
    shopProductKey: {
        activityId: string
        activityType: string
        productId: string
        shopId: string
    }
}

/**
 * 限购类型
 */
export type StockType = keyof typeof STOCK_TYPES
export enum STOCK_TYPES {
    UNLIMITED,
    LIMITED,
}
