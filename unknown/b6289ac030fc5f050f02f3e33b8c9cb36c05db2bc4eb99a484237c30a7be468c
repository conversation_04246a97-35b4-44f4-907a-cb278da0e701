/*
 * @description:
 * @Author: lexy
 * @Date: 2023-01-30 10:38:20
 * @LastEditors: lexy
 * @LastEditTime: 2023-01-31 17:48:17
 */
export {}
declare global {
    interface ImportMetaEnv {
        VITE_BASE_MAIN_PATH: string
        VITE_BASE_IMAGE_URL: string
        VITE_BASE_URL: string
        VITE_STOMP_CONNECT_URI: string
        VITE_LOCAL_STORAGE_KEY_PREFIX: string
        VITE_REQUEST_TIME_OUT: string
        VITE_MAP_KEY: string
        VITE_MAP_SAFECODE: string
    }
    type FN = (...arg: any[]) => void
    type Fn = () => void
}
