<!--
 * @description: 装修搜索组件展示层
 * @Author: lexy
 * @Date: 2022-08-10 23:38:42
 * @LastEditors: lexy
 * @LastEditTime: 2023-02-27 16:56:24
-->
<script setup lang="ts">
import { PropType } from 'vue'
import defaultSearchData from './search'
const $props = defineProps({
    formData: {
        type: Object as PropType<typeof defaultSearchData>,
        default: defaultSearchData,
    },
})
/*
 *variable
 */
/*
 *lifeCircle
 */
/*
 *function
 */
</script>

<template>
    <div
        class="search"
        :style="{
            color: $props.formData.color,
            background: $props.formData.background,
            'border-color': $props.formData.borderColor,
            'border-radius': `${$props.formData.borderRadius}px`,
        }"
    >
        <i class="search__iconfont el-icon-search"></i>
        <span>{{ $props.formData.keyWord }}</span>
    </div>
</template>

<style lang="scss" scoped>
.search {
    border: 1px solid transparent;
}
</style>
