<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-10-10 09:49:18
 * @LastEditors: lexy
 * @LastEditTime: 2023-09-18 16:24:12
-->
<script setup lang="ts">
import { useRouter } from 'vue-router'
import DateUtil from '@/utils/date'
import { QuestionFilled, Search } from '@element-plus/icons-vue'
import { doGetFinance } from '@/apis/finance/index'
import PageManage from '@/components/PageManage.vue'
import { ElMessage, TabsPaneContext } from 'element-plus'
import DescriptionDialog from './description-dialog.vue'
import { doPostExportStatementData } from '@/apis/overview'
import { useRangeLimitedDate } from '@/hooks'
/**
 * @LastEditors: lexy
 * @description: 交易类型
 * @params ORDER_PAID 订单支付(订单详情)
 * @params ORDER_AFS 售后退款(详情)
 * @params USER_BALANCE_RECHARGE 会员余额充值(会员详情)
 * @params PAID_MEMBER_OPEN 付费会员开通(会员详情)
 * @params PAID_MEMBER_RENEW 付费会员续费(会员详情)
 * @params DISTRIBUTE 分销佣金
 */
enum TRADESTATUS {
    ORDER_PAID,
    ORDER_AFS,
    USER_BALANCE_RECHARGE,
    PAID_MEMBER_OPEN,
    PAID_MEMBER_RENEW,
    DISTRIBUTE,
}
/*
 *variable
 */
const $router = useRouter()
const { divTenThousand } = useConvert()
const activeTab = ref('')
const checkedData = ref<any[]>([])
const tableData = ref([])
const dateTool = new DateUtil()
const showDescriptionDialog = ref(false)
/** zrb:初始化为当前自然月日期段 */
const getMonthStartTimestamp = dateTool.getMonthStartTimestamp(new Date())

/** 搜索条件 */
const query = ref({
    transactionType: null,
    changeType: null, //   ""->全部;INCREASE->收入;REDUCE->支出
    startDate: dateTool.getYMDs(getMonthStartTimestamp),
    endDate: dateTool.getYMDs(+new Date()),
    keyword: '',
    current: 1,
    size: 10,
})

const total = ref(0)

/** 对账单概况 */
const account = ref({
    income: 0,
    incomeCount: 0,
    payout: 0,
    payoutCount: 0,
})

const createTime = ref([dateTool.getMonthStartDate(getMonthStartTimestamp), new Date()])

const options = computed(() => {
    let returnOptions = []
    if (activeTab.value === 'INCREASE') {
        returnOptions = basicOptions.filter((item) => item.type === 'in')
        returnOptions.unshift({ label: '全部', value: null })
    } else if (activeTab.value === 'REDUCE') {
        returnOptions = basicOptions.filter((item) => item.type === 'out')
        returnOptions.unshift({ label: '全部', value: null })
    } else {
        returnOptions = basicOptions
    }
    return returnOptions
})
const basicOptions = [
    { label: '全部', value: null },
    { label: '用户充值', value: 'USER_BALANCE_RECHARGE', type: 'in' },
    { label: '购买付费会员', value: 'PAID_MEMBER_OPEN', type: 'in' },
    { label: '续费付费会员', value: 'PAID_MEMBER_RENEW', type: 'in' },
    { label: '平台服务费', value: 'SYSTEM_SERVICE', type: 'in' },
    { label: '平台服务费(采购)', value: 'PURCHASE_ORDER_SERVICE_CHARGE', type: 'in' },
    { label: '积分交易', value: 'INTEGRAL_GOODS_DEAL', type: 'in' },
    { label: '会员折扣', value: 'MEMBER_DISCOUNT', type: 'out' },
    { label: '会员包邮', value: 'MEMBER_LOGISTICS_DISCOUNT', type: 'out' },
    { label: '平台优惠券', value: 'PLATFORM_DISCOUNT_COUPON', type: 'out' },
    { label: '返利抵扣', value: 'REBATE_DEDUCTION', type: 'out' },
    { label: '充值赠送', value: 'RECHARGE_GIFT', type: 'out' },
]

/*
 *lifeCircle
 */
onMounted(() => {
    init()
})

/*
 *function
 */

/**
 * 初始化
 */
function init() {
    query.value.current = 1
    initReconciliation()
}

/**
 * 切换顶部状态
 */
const clickTab = () => {
    Object.assign(query.value, {
        changeType: activeTab.value || null,
        startDate: dateTool.getYMDs(getMonthStartTimestamp),
        endDate: dateTool.getYMDs(+new Date()),
        transactionType: null,
        keyword: '',
        current: 1,
    })
    createTime.value = [dateTool.getMonthStartDate(getMonthStartTimestamp), new Date()]
    // getFinanceList()
    initReconciliation()
}

/**
 * 下拉框选择
 */
const selectStatus = () => {
    query.value.current = 1
    // getFinanceList()
    initReconciliation()
}

// 设置30天范围限制，同时禁用未来日期
const { disabledDate, handleCalendarChange, handleVisibleChange } = useRangeLimitedDate(90, true, true)

/**
 * 选择时间
 */
const chooseTime = (Date: [Date, Date]) => {
    query.value.startDate = Date ? dateTool.getYMDs(Date[0]) : ''
    query.value.endDate = Date ? dateTool.getYMDs(Date[1]) : ''
    init()
}
/**
 * 选择时间
 */
const Searchlist = () => {
    query.value.current = 1
    // getFinanceList()
    initReconciliation()
}

/**
 * @method handleSizeChange
 * @description 每页 条
 */
const handleSizeChange = (val: number) => {
    query.value.current = 1
    query.value.size = val
    // getFinanceList()
    initReconciliation()
}

/**
 * @method handleCurrentChange
 * @description 当前页
 */
const handleCurrentChange = (val: number) => {
    query.value.current = val
    // getFinanceList()
    initReconciliation()
}
/**
 * 不同类型跳转详情
 */
const handleJump = (row: any) => {
    const navMap = {
        ORDER_PAID: {
            path: 'order/details',
            params: {
                orderNo: row.orderNo,
                shopId: row.shopId,
            },
        },
        ORDER_AFS: {
            path: 'order/afs/details',
            params: {
                afsNo: row.tradeDetail.afsNo || '',
                packageId: row.tradeDetail.packageId || '',
                orderNo: row.orderNo,
            },
        },
        USER_BALANCE_RECHARGE: {
            path: 'vip/base/vip/details',
            params: {
                userId: row.userId,
            },
        },
        PAID_MEMBER_OPEN: {
            path: 'vip/base/vip/details',
            params: {
                userId: row.userId,
            },
        },
        PAID_MEMBER_RENEW: {
            path: 'vip/base/vip/details',
            params: {
                userId: row.userId,
            },
        },
        DISTRIBUTE: {
            path: 'order/details',
            params: {
                orderNo: row.orderNo,
                shopId: row.shopId,
            },
        },
    } as { [x: string]: { path: string; params: any } }
    const currentNavLink = navMap[row.tradeType]
    console.log('currentNavLink', currentNavLink)
    $router.push({
        path: currentNavLink.path,
        query: currentNavLink.params,
    })
}
/**
 * @LastEditors: lexy
 * @description: 状态转换
 */
const convertStatus = (status: keyof typeof TRADESTATUS) => {
    if (!status) return '狗啊，后端不给数据'
    return basicOptions.find((item) => item.value === status)?.label
    // return options.filter((item) => item.value === status)[0].label
}
async function initReconciliation() {
    const { current, size, changeType, startDate, endDate, transactionType, keyword } = query.value
    let tempObj = {
        page: {
            current,
            size,
        },
        changeType,
        startDate,
        endDate,
        keyword,
        transactionType,
    }
    const { code, data } = await doGetFinance(tempObj)
    if (code === 200) {
        tableData.value = data.records
        total.value = data.total
        if (data.statistics) {
            account.value = data.statistics
        }
    } else {
        ElMessage.error('获取数据失败')
    }
}
const handleSelectionChange = (selectionData: any[]) => {
    checkedData.value = selectionData
}
const handleExport = async () => {
    let params: any = {}
    if (checkedData.value?.length) {
        params.exportIds = checkedData.value?.map((item) => item.id) || []
    } else {
        const { changeType, startDate, endDate, keyword, transactionType } = query.value
        params = { changeType, startDate, endDate, keyword, transactionType }
    }
    const { code, msg } = await doPostExportStatementData(params)
    if (code === 200) {
        ElMessage.success({ message: msg || '导出成功' })
    } else {
        ElMessage.error({ message: msg || '导出失败' })
    }
}
</script>

<template>
    <div class="finance">
        <div class="tab_container" style="padding-top: 16px">
            <div class="finance__header">
                <div class="finance__header--income">
                    收入（{{ account.incomeCount }}笔）
                    <br />
                    <span style="color: #4bb1a6; font-size: 18px">+ {{ account.income && divTenThousand(account.income) }}元</span>
                </div>
                <div class="finance__header--expenses">
                    支出（{{ account.payoutCount }}笔）
                    <br />
                    <span style="color: #ff0000; font-size: 18px">-{{ account.payout && divTenThousand(account.payout) }}元</span>
                </div>
            </div>
        </div>
        <div class="tab_container">
            <div class="fiance-tabs">
                <el-tabs v-model="activeTab" @tab-change="clickTab">
                    <el-tab-pane label="全部" name=""></el-tab-pane>
                    <el-tab-pane label="收入" name="INCREASE"></el-tab-pane>
                    <el-tab-pane label="支出" name="REDUCE"></el-tab-pane>
                </el-tabs>
            </div>
        </div>
        <div class="handle_container">
            <div class="fiance-tabs__export">
                <el-button size="small" type="primary" @click="handleExport">导出</el-button>
                <el-icon class="fiance-tabs__export--icon" @click="showDescriptionDialog = true"><QuestionFilled /></el-icon>
            </div>
        </div>
        <div class="finance__form">
            <div style="display: flex; align-content: center">
                <el-date-picker
                    v-model="createTime"
                    style="margin-right: 15px; width: 400px"
                    type="daterange"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :disabled-date="disabledDate"
                    @calendar-change="handleCalendarChange"
                    @visible-change="handleVisibleChange"
                    @change="chooseTime"
                ></el-date-picker>
                <el-select v-model="query.transactionType" placeholder="请选择" @change="selectStatus">
                    <el-option v-for="item in options" :key="String(item.value)" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </div>
            <div style="display: flex">
                <el-input
                    v-model="query.keyword"
                    style="width: 290px"
                    placeholder="输入订单号或店铺名称关键词"
                    maxlength="242"
                    @keypress.enter="Searchlist"
                >
                    <template #append>
                        <el-button :icon="Search" @click="Searchlist"></el-button>
                    </template>
                </el-input>
            </div>
        </div>
        <el-table
            empty-text="暂无数据~"
            :data="tableData"
            style="width: 100%"
            height="calc(100vh - 380px)"
            :header-cell-style="{
                'background-color': '#F6F8FA',
                'font-weight': 'normal',
                color: '#515151',
            }"
            @selection-change="handleSelectionChange"
        >
            <el-table-column type="selection" width="55" fixed="left" />
            <el-table-column label="订单号" width="200px">
                <template #default="{ row }">
                    <div>{{ row.orderNo || '-' }}</div>
                </template>
            </el-table-column>
            <el-table-column label="交易流水号" width="200px">
                <template #default="{ row }">
                    <div>{{ row.tradeNo || '-' }}</div>
                </template>
            </el-table-column>
            <el-table-column label="所属商家" width="150px">
                <template #default="{ row }">
                    <div>{{ row.shopName || '-' }}</div>
                </template>
            </el-table-column>
            <el-table-column label="交易类型" width="120px">
                <template #default="{ row }">
                    <template v-if="row">
                        <div>{{ convertStatus(row.tradeType) }}</div>
                    </template>
                </template>
            </el-table-column>
            <el-table-column label="收支金额（元）" width="140px">
                <template #default="{ row }">
                    <div>{{ row.changeType === 'INCREASE' ? '+' : '-' }}{{ row.amount && divTenThousand(row.amount) }}</div>
                </template>
            </el-table-column>
            <el-table-column label="交易时间" width="170px">
                <template #default="{ row }">
                    <div>{{ row.tradeTime }}</div>
                </template>
            </el-table-column>
            <!-- 操作按钮暂定隐藏  操作逻辑后面有变动 -->
            <!-- <el-table-column label="操作" width="60px" fixed="right">
                <template #default="{ row }">
                    <div style="color: #2e99f3; cursor: pointer" @click="handleJump(row)">详情</div>
                </template>
            </el-table-column> -->
        </el-table>
        <page-manage
            :page-size="query.size"
            :page-num="query.current"
            :total="total"
            @handle-size-change="handleSizeChange"
            @handle-current-change="handleCurrentChange"
        />
    </div>
    <el-dialog v-model="showDescriptionDialog" title="平台对账说明" :width="800">
        <description-dialog />
    </el-dialog>
</template>

<style lang="scss" scoped>
@include b(finance) {
    padding-left: 27px;
    padding-right: 27px;
    @include e(header) {
        display: flex;
        align-items: center;
        background: #f9f9f9;
        height: 77px;
        margin: auto;
        font-size: 14px;
        margin-bottom: 29px;

        @include m(income) {
            flex: 1;
            margin-left: 38px;
        }

        @include m(expenses) {
            flex: 1;
        }
    }

    @include e(form) {
        margin-bottom: 15px;
        display: flex;
        justify-content: space-between;
        align-content: center;
    }
}
@include b(fiance-tabs) {
    position: relative;
    @include e(export) {
        position: absolute;
        top: 10px;
        right: 0;
        @include flex();
        @include m(icon) {
            font-size: 22px;
            margin-left: 10px;
        }
    }
}
</style>
