<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-05-14 15:22:21
 * @LastEditors: lexy
 * @LastEditTime: 2024-04-25 18:24:05
-->
<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { isEqual } from 'lodash'
import Line from './Line.vue'
import { doSaveOSS, doGetOSS } from '@/apis/setting'
import { Select, InfoFilled } from '@element-plus/icons-vue'
import { enumServerFormCn } from '@/views/set/components/fileSet'
type ServeType = 'TENCENT' | 'QUIDWAY' | 'QINIUO' | 'ALIYUN' | 'MINIO' | 'LOCAL'
/*
 *variable
 */
const enumServerForm = reactive([
    // 腾讯云
    {
        type: 'TENCENT',
        // 域名
        qcloudDomain: '',
        // 前缀
        qcloudPrefix: '',
        // appid
        qcloudAppId: '',
        // SecretId"
        qcloudSecretId: '',
        // SecretKey
        qcloudSecretKey: '',
        // BucketName"
        qcloudBucketName: '',
        // 腾讯云COS所属地区
        qcloudRegion: '',
    },
    // 华为云
    {
        type: 'QUIDWAY',
        // 华为appid
        quidwayAccessKeyId: '',
        // SecretId
        quidwayAccessKeySecret: '',
        // endPoint
        quidwayEndpoint: '',
        // bucketname
        obsBucketName: '',
        // 域名
        quidwayDomain: '',
        // 前缀
        quidwayPrefix: '',
    },
    //   七牛
    {
        type: 'QINIUO',
        // 域名
        qiniuDomain: '',
        // 前缀
        qiniuPrefix: '',
        // AccessKey
        qiniuAccessKey: '',
        // SecretKey
        qiniuSecretKey: '',
        // 桶
        qiniuBucketName: '',
    },
    //阿里云
    {
        type: 'ALIYUN',
        // 域名
        aliyunDomain: '',
        // 前缀
        aliyunPrefix: '',
        // endPoint
        aliyunEndPoint: '',
        // AccessKeyId
        aliyunAccessKeyId: '',
        // AccessKeySecret
        aliyunAccessKeySecret: '',
        // BucketName"
        aliyunBucketName: '',
    },
    //minio
    {
        type: 'MINIO', //类型
        minioAccessKey: '', //key
        minioSecretKey: '', //秘钥
        minioEndPoint: '', //端点
        minioBucketName: '', //bucket
        minioDomain: '', //域名
    },
    //local
    {
        type: 'LOCAL',
        localDomain: '', //域名
        localStoragePath: '', //存储路径
    },
])

const currentRadio = ref<keyof typeof widthMap>('TENCENT')
const currentForm = computed(() => {
    return enumServerForm.filter((item) => item.type === currentRadio.value)[0]
})
const using = ref('')
/*
 *lifeCircle
 */
initForm('TENCENT')
/*
 *function
 */
const validFormat = () => {
    const current = { ...toRaw(currentForm.value) }
    Reflect.deleteProperty(current, 'type')
    if (isEqual(current, originData) && using.value === currentRadio.value) {
        ElMessage.error('内容未更改，请更改后提交')
        return false
    }
    if (Object.values(currentForm.value).includes('')) {
        ElMessage.error('请填写完成表单')
        return false
    }
    return true
}
const submitHandle = async () => {
    if (!validFormat()) return
    const { code, success } = await doSaveOSS(currentForm.value)
    if (code === 200 && success) {
        ElMessage.success('保存成功')
        initForm(currentRadio.value)
    } else {
        ElMessage.error('保存失败')
    }
}
let originData: any
async function initForm(val: any) {
    const { code, data, success } = await doGetOSS(val)
    if (code === 200 && success) {
        using.value = data.using
        switch (currentRadio.value as ServeType) {
            case 'TENCENT':
                enumServerForm[0] = { ...enumServerForm[0], ...data.conf }
                break
            case 'QUIDWAY':
                enumServerForm[1] = { ...enumServerForm[1], ...data.conf }
                break
            case 'QINIUO':
                enumServerForm[2] = { ...enumServerForm[2], ...data.conf }
                break
            case 'ALIYUN':
                enumServerForm[3] = { ...enumServerForm[3], ...data.conf }
                break
            case 'MINIO':
                enumServerForm[4] = { ...enumServerForm[4], ...data.conf }
                break
            case 'LOCAL':
                enumServerForm[5] = { ...enumServerForm[5], ...data.conf }
                break
            default:
                break
        }
        originData = { ...data.conf }

        // currentForm = data;
        // let current = enumServerForm.find((item) => item.type === data.type)
        // current = data
    }
}

/**
 * @: 映射每个表单label的宽度
 */
const widthMap = {
    TENCENT: '150px',
    QUIDWAY: '120px',
    QINIUO: '100px',
    ALIYUN: '140px',
    MINIO: '100px',
    LOCAL: '100px',
} as const

const checkUsing = (str: keyof typeof widthMap) => using.value === str
</script>

<template>
    <Line name="服务器设置" color="#08CC00" />
    <el-form label-position="left">
        <el-radio-group v-model="currentRadio" size="large" @change="initForm">
            <el-radio-button label="TENCENT">
                <span class="btn-icon" :class="{ active: checkUsing('TENCENT') }">
                    <el-icon v-show="checkUsing('TENCENT')"><Select /></el-icon>
                </span>
                <span :class="{ 'text-color': checkUsing('TENCENT') }"> 腾讯 </span>
            </el-radio-button>
            <el-radio-button label="QUIDWAY">
                <span class="btn-icon" :class="{ active: checkUsing('QUIDWAY') }">
                    <el-icon v-show="checkUsing('QUIDWAY')"><Select /></el-icon>
                </span>

                <span :class="{ 'text-color': checkUsing('QUIDWAY') }"> 华为 </span>
            </el-radio-button>
            <el-radio-button label="QINIUO">
                <span class="btn-icon" :class="{ active: checkUsing('QINIUO') }">
                    <el-icon v-show="checkUsing('QINIUO')"><Select /></el-icon>
                </span>

                <span :class="{ 'text-color': checkUsing('QINIUO') }"> 七牛 </span>
            </el-radio-button>
            <el-radio-button label="ALIYUN">
                <span class="btn-icon" :class="{ active: checkUsing('ALIYUN') }">
                    <el-icon v-show="checkUsing('ALIYUN')"><Select /></el-icon>
                </span>

                <span :class="{ 'text-color': checkUsing('ALIYUN') }"> 阿里 </span>
            </el-radio-button>
            <el-radio-button label="MINIO">
                <span class="btn-icon" :class="{ active: checkUsing('MINIO') }">
                    <el-icon v-show="checkUsing('MINIO')"><Select /></el-icon>
                </span>

                <span :class="{ 'text-color': checkUsing('MINIO') }"> MINIO </span>
            </el-radio-button>
            <el-radio-button label="LOCAL">
                <span class="btn-icon" :class="{ active: checkUsing('LOCAL') }">
                    <el-icon v-show="checkUsing('LOCAL')"><Select /></el-icon>
                </span>
                <span :class="{ 'text-color': checkUsing('LOCAL') }"> 本地 </span>
            </el-radio-button>
        </el-radio-group>
        <div class="group"></div>
        <div v-for="(val, key, index) in currentForm" :key="index" :data-val="val" class="form-container">
            <el-form-item v-if="key !== 'type'" :label-width="widthMap[currentRadio]">
                <template #label>
                    {{ enumServerFormCn[key as keyof typeof enumServerFormCn].name }}
                    <el-tooltip
                        v-if="enumServerFormCn[key as keyof typeof enumServerFormCn]?.info"
                        effect="dark"
                        :content="enumServerFormCn[key as keyof typeof enumServerFormCn].info"
                        placement="top"
                    >
                        <el-icon class="label-icon"><InfoFilled /></el-icon>
                    </el-tooltip>
                </template>
                <el-input
                    v-model="currentForm[key]"
                    class="cusInput"
                    :placeholder="enumServerFormCn[key as keyof typeof enumServerFormCn].placeholder"
                />
            </el-form-item>
        </div>
        <el-form-item>
            <el-button type="primary" @click="submitHandle">保存</el-button>
        </el-form-item>
    </el-form>
</template>

<style lang="scss" scoped>
@include b(cusInput) {
    width: 300px;
    height: 30px;
}
@include b(btn-icon) {
    position: absolute;
    inset: 0;
}

@include b(text-color) {
    position: relative;
    color: #fff;
    background-color: transparent;
    z-index: 3;
}

@include b(active) {
    background-color: #409eff;
    color: #fff;
    @include b(el-icon) {
        position: absolute;
        top: 0;
        right: 0;
    }
}

@include b(el-radio-group) {
    :deep(.el-radio-button__inner) {
        --el-radio-button-checked-text-color: --el-text-color-regular;
        --el-radio-button-checked-bg-color: #fff;
    }
}

@include b(el-form-item) {
    :deep(.el-form-item__label) {
        justify-content: flex-end;
        display: flex;
        align-items: center;
    }
}

@include b(label-icon) {
    margin: 0 5px;
    font-size: 18px;
    cursor: pointer;
}

.group {
    margin-bottom: 20px;
}
</style>
