<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-04-16 09:26:47
 * @LastEditors: lexy
 * @LastEditTime: 2023-09-20 18:32:13
-->
<template>
    <!-- 操作 -->
    <div class="handle_container">
        <el-button type="primary" round class="mr-20" @click="navToNewShop">添加店铺</el-button>
        <el-button v-if="$prop.currentTabChoose === 'REJECT'" type="primary" round class="mr-20" @click="commandChange('Delete')">
            批量删除
        </el-button>
        <q-dropdown-btn v-if="$prop.currentTabChoose === ' '" title="批量操作" :option="commandList" @right-click="commandChange" />
    </div>
    <!-- table -->
    <ShopTable ref="shopTableRef" :table-list="tableData" @refresh="refreshHandle" />
    <page-manage
        :page-size="pageConfig.size"
        :page-num="pageConfig.current"
        :total="pageConfig.total"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
    />
</template>

<script lang="ts" setup>
import type { PropType } from 'vue'
import ShopTable from './ShopTable.vue'
import { doGetShopList } from '@/apis/shops'
import { ElMessage } from 'element-plus'
import QDropdownBtn from '@/components/q-btn/q-dropdown-btn.vue'
import PageManage from '@/components/PageManage.vue'
type searchParamStatus = 'NORMAL' | 'REJECT'
interface searchParamType {
    no: string
    name: string
    status: searchParamStatus
}
const pageConfig = reactive({
    size: 20,
    current: 1,
    total: 0,
})
const tableData = ref([])
const shopTableRef = ref()
const showDropdown = ref()
const commandList = ref([
    {
        label: '删除',
        name: 'Delete',
    },
    {
        label: '启用',
        name: 'NORMAL',
    },
    {
        label: '禁用',
        name: 'FORBIDDEN',
    },
])
const $prop = defineProps({
    searchParams: {
        type: Object as PropType<searchParamType>,
        default() {
            return {}
        },
    },
    currentTabChoose: {
        type: String,
        required: true,
    },
})
watch(
    () => $prop.currentTabChoose,
    (val) => {
        switch (val) {
            case 'REJECT':
                commandList.value = [{ label: 'Delete', name: '删除' }]
                break
            case 'UNDER_REVIEW':
                commandList.value = [{ label: 'refusedTo', name: '拒绝' }]
                break
            default:
                commandList.value = [
                    {
                        name: 'Delete',
                        label: '删除',
                    },
                    {
                        name: 'NORMAL',
                        label: '启用',
                    },
                    {
                        name: 'FORBIDDEN',
                        label: '禁用',
                    },
                ]
                break
        }
    },
    { immediate: true },
)
const $router = useRouter()
const navToNewShop = () => {
    $router.push({
        name: 'addShop',
    })
}
initList($prop.searchParams)

defineExpose({
    initList,
})
async function initList(param: searchParamType) {
    const params = Object.assign(param, pageConfig)
    const { data } = await doGetShopList(params)
    tableData.value = data.records

    pageConfig.current = +data.current
    pageConfig.total = +data.total
    pageConfig.size = +data.size
}
/**
 * @LastEditors: lexy
 * @description: 点击左边展示下拉
 * @returns {*}
 */
const handleLeftClick = () => {
    // showDropdown.value.showClick()
}
const commandChange = (e: string) => {
    switch (e) {
        case 'Delete':
            batchDeleteShops()
            break
        case 'NORMAL':
            batchChangeShops('NORMAL')
            break
        case 'FORBIDDEN':
            batchChangeShops('FORBIDDEN')
            break
        default:
            batchChangeShops('refusedTo')
            break
    }
}
const batchDeleteShops = () => {
    shopTableRef.value.batchDeleteShop()
}
/**
 * @LastEditors: lexy
 * @description: 批量启用禁用
 * @param {boolean} status
 */
const batchChangeShops = async (status: string) => {
    if ($prop.currentTabChoose === 'REJECT') {
        return ElMessage.error('该商户已被拒绝')
    }
    const res = await shopTableRef.value.batchChangeStatus(status)
    if (!res) return
    initList(Object.assign($prop.searchParams, { status: $prop.currentTabChoose }))
}
const refreshHandle = () => {
    initList($prop.searchParams)
}
const handleSizeChange = (val: number) => {
    // console.log(status)
    pageConfig.current = 1

    pageConfig.size = val
    initList(Object.assign($prop.searchParams, { status: $prop.currentTabChoose }))
}
const handleCurrentChange = (val: number) => {
    pageConfig.current = val
    initList(Object.assign($prop.searchParams, { status: $prop.currentTabChoose }))
}
</script>

<style lang="scss" scoped>
@include b(btns) {
    @include flex;
    justify-content: flex-start;
    background: white;
    padding-left: 16px;
    padding-right: 16px;
    padding-bottom: 16px;
}
@include b(down) {
    height: 18px;
}
.mr-20 {
    margin-right: 20px;
}
.mb-15 {
    margin-bottom: 15px;
}
@include b(group) {
    position: relative;
    @include e(placeholder) {
        position: absolute;
        right: -2px;
        z-index: 999;
        &:hover {
            color: #337ecc !important;
        }
    }
}
@include b(text-center) {
    margin-bottom: 5px;
}
</style>
