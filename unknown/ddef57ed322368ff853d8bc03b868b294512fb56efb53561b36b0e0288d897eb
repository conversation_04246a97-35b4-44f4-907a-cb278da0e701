<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-07-15 13:11:21
 * @LastEditors: lexy
 * @LastEditTime: 2023-07-18 13:22:59
-->
<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { useAdminInfo } from '@/store/modules/admin'
import { useRouter } from 'vue-router'
/*
 *variable
 */
const $useRouter = useRouter()
const count = ref(5)
const time = ref<null | any>(null)
/*
 *lifeCircle
 */
onBeforeUnmount(clearavTimeout)
navtimeout()
function navtimeout() {
    count.value--
    time.value = setTimeout(() => {
        if (count.value === 0) {
            return handleClick()
        }
        navtimeout()
    }, 1000)
}
/*
 *function
 */
const handleClick = () => {
    clearavTimeout()
    let path = '/overview'
    if (!useAdminInfo().getterToken) {
        path = '/sign'
    }
    $useRouter.replace(path)
}
function clearavTimeout() {
    time.value && clearTimeout(time.value)
}
</script>

<template>
    <div style="width: 100%; height: 590px" class="container">
        <el-image style="width: 700px; height: 500px" fit="fill" src="https://devoss.chongyoulingxi.com/system-front/mobile/404_error.png" />
    </div>
    <div class="msg"><span>哟~这都被你找到了，点击按钮，再试一次。</span></div>
    <div class="back">
        <span class="back_send">{{ count }} </span>秒后为您自动跳转
    </div>
    <div class="back"><el-button type="primary" style="width: 150px" size="large" @click="handleClick">返回</el-button></div>
</template>

<style scoped lang="scss">
.container {
    display: flex;
    justify-content: center;
    align-items: center;
}
.msg {
    text-align: center;
    color: rgb(51, 51, 51);
    font-family: PingFang SC;
    font-size: 26px;
    font-weight: 400;
    text-align: center;
}
.back {
    text-align: center;
    margin-top: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.back_send {
    color: chocolate;
    font-size: 30px;
    margin-right: 10px;
}
</style>
