<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-03-15 09:24:01
 * @LastEditors: lexy
 * @LastEditTime: 2024-04-07 16:58:16
-->
<script lang="ts" setup>
import { useRouter } from 'vue-router'
// import QTable from '@/components/qszr-core/packages/q-table/QTable'
// import QTableColumn from '@/components/qszr-core/packages/q-table/q-table-column.vue'
// import QIcon from '@/components/q-icon/q-icon.vue'
import SplitTable from '@/components/order/order-split-table/SplitTable'
import SplitTableColumn from '@/components/order/order-split-table/split-table-column.vue'
import CountDown from '@/components/order/count-down/index.vue'
import { getOrderDeliveryStatus, getPaymentType, getDeliveryMode, getOrderAfterSaleStatus } from './helper'
import { calculate } from './orderDetails/OrderStatusCalculate'
import useClipboard from 'vue-clipboard3'
import { ElMessage } from 'element-plus'
import { ArrowDown, WarningFilled, CopyDocument } from '@element-plus/icons-vue'
import AccordionFrom from '@/components/order/accordion-from.vue'
import PageManage from '@/components/PageManage.vue'
import { queryStatus, TabsName } from '@/composables/useOrderStatus'
import DateUtil from '@/utils/date'
import { orderStatusResponsibility } from '@/libs/orderStatus'
import OrderShipment from './orderShipment/Index.vue'
import { doGetOrderList, importNeedDeliveryOrders } from '@/apis/order'
import RemarkPopup from '@/components/remark/remark-popup.vue'
import RemarkFlag from '@/components/remark/remark-flag.vue'
import type { ApiOrder, OrderReceiver, ShopOrder, ShopOrderItem, ExtraMap } from './types/order'
import { doGetShopDeliveryConfig } from '@/apis/set/platformDelivery'
import { useDeliveryOrderList } from '@/store/modules/order'
import PlatformComp from './platform.vue'
import { doGetExportData } from '@/apis/exportData/index'
/*
 *variable
 */
enum SellTypeEnum {
    CONSIGNMENT = '代销商品',
    PURCHASE = '采购商品',
    OWN = '自有商品',
}
const $deliveryOrderList = useDeliveryOrderList()
const { toClipboard } = useClipboard()
const $router = useRouter()
const { divTenThousand } = useConvert()
const FirstTabsName = ref('全部订单')
const TabNavArr: any[] = []
const dropdownRef = ref()
const isDropdown = ref(false)
const searchStasusData = ref<{ title: string; name: string }[]>([])
// tab切换部分 当前高亮
const activeTabName = ref(' ')
// 当前备注的订单号ids
const currentRemarkIds = ref<string[]>([])
const remarkDialog = ref(false)
// tab表格
const orderInfoArr = ref<ApiOrder[]>([])
const multiSelect = ref<ApiOrder[]>([])
// 备注文本
const textarea = ref('')
const pageConfig = reactive({
    size: 20,
    current: 1,
    total: 0,
})
let extraMap = ref<ExtraMap>({ AllDeliveryCount: '0', miniDeliveryCount: '0' })
const tableStyleUp = ref(false)
const dateTool = new DateUtil()
// 条件查询
const queryConditionsTabs = reactive({
    params: {
        buyerNickname: '',
        receiverName: '',
        orderNo: '',
        productName: '',
        startTime: '', // 开始时间
        endTime: '', // 结束时间}
        distributionMode: '', // 配送方式
        shopType: '', // 店铺类型
        shopId: '',
        platform: '',
        supplierName: '',
    },
})
const deliveryReactive = reactive({
    deliverDialog: false,
    currentNo: '',
    currentShopOrderNo: '',
})
/*
 *lifeCircle
 */
initOrderList()
initTabs()
initSearchStasusData()

/*
 *function
 */
/**
 * @LastEditors: lexy
 * @description: 获取订单列表 UN_RECEIVE
 */
function initOrderList() {
    nextTick(() => {
        console.log(TabNavArr, activeTabName)
        const { params } = queryConditionsTabs
        const query = {
            ...pageConfig,
            ...params,
            status: activeTabName.value,
            isMiniAppTip: true,
        }
        doGetOrderList(query).then(({ data }) => {
            pageConfig.current = data.current
            pageConfig.size = data.size
            pageConfig.total = data.total
            orderInfoArr.value = doGetFlatOrderList(data.records)
            extraMap.value = data.extraMap
        })
    })
}
provide('reloadParentListFn', initOrderList)
/**
 * @description 根据商家信息拆分订单
 * @param { ApiOrder[] } 请求订单列表
 */
const doGetFlatOrderList = (orderList: ApiOrder[] = []) => {
    const newOrderList: ApiOrder[] = []
    orderList.forEach((order) => {
        order.shopOrders?.forEach((shopOrder) => {
            newOrderList.push({ ...order, shopOrders: [shopOrder], checked: false })
        })
    })
    return newOrderList
}

/**
 * @LastEditors: lexy
 * @description: 初始化tab
 * @returns {*}
 */
function initTabs() {
    for (const [key, value] of Object.entries(queryStatus)) {
        TabNavArr.push({
            key,
            value,
        })
    }
}

function initSearchStasusData() {
    const newTabNavArr = TabNavArr.map((item) => ({ title: item.value, name: item.key }))
    newTabNavArr.unshift({ title: '全部订单', name: ' ' })
    searchStasusData.value = newTabNavArr
}

/**
 * @LastEditors: lexy
 * @description: 近一个月/三个月/全部（下拉选择）
 * @param {*} event
 * @returns {*}
 */
const handleDropdownCommand = ($event: string) => {
    FirstTabsName.value = $event
    handleStartTimeAndstartEnd()
}

/**
 * @LastEditors: lexy
 * @description: 处理开始时间开始结束
 * @returns {*}
 */
function handleStartTimeAndstartEnd() {
    if (FirstTabsName.value === '近一个月订单') {
        const startTime = dateTool.getLastMonth()
        loadHandleTabClick(startTime)
    } else if (FirstTabsName.value === '近三个月订单') {
        const startTime = dateTool.getLastThreeMonth()
        loadHandleTabClick(startTime)
    } else {
        queryConditionsTabs.params.startTime = ''
        queryConditionsTabs.params.endTime = ''
        initOrderList()
    }
}

/**
 * @LastEditors: lexy
 * @description: Tabs 条件查询
 * @returns {*}
 */
const loadHandleTabClick = async (startTime: string) => {
    const endTime = dateTool.getYMDs()
    queryConditionsTabs.params.startTime = startTime
    queryConditionsTabs.params.endTime = endTime
    initOrderList()
}
/**
 * @LastEditors: lexy
 * @description: tab栏点击
 * @param {*} tab
 * @param {*} event
 * @returns {*}
 */
const handleTabClick = async () => {
    if (activeTabName.value !== ' ') {
        queryConditionsTabs.params.endTime = ''
        queryConditionsTabs.params.startTime = ''
        initOrderList()
        return
    } else {
        handleDropdownCommand(FirstTabsName.value)
    }
}
/**
 * @LastEditors: lexy
 * @description: 全部/季度/月份下拉
 * @returns {*}
 */
const handleOpen = () => {
    isDropdown.value = !isDropdown.value
    isDropdown.value ? dropdownRef.value.handleOpen() : dropdownRef.value.handleClose()
}
/**
 * @LastEditors: lexy
 * @description:总价计算
 * @param {*} shopOrderItems
 * @returns {*}
 */
const tatolPirce = (shopOrderItems: ShopOrderItem[]) => {
    return shopOrderItems.reduce((pre, item) => divTenThousand(item.dealPrice).mul(item.num).add(pre).toString(), '0')
}
/**
 * @LastEditors: lexy dealPrice
 * @description:默认展示第一个商品的单价
 * @param {*} prc
 * @returns {*}
 */
const unitPrice = computed(() => (shopOrderItems: ShopOrderItem[]) => divTenThousand(shopOrderItems[0].dealPrice))
/**
 * @LastEditors: lexy
 * @description: 商品总数量展示
 * @param {*} prc
 * @returns {*}
 */
const num = computed(() => (shopOrderItems: ShopOrderItem[]) => shopOrderItems.reduce((pre, item) => item.num + pre, 0))
const handleSizeChange = (value: number) => {
    pageConfig.current = 1
    pageConfig.size = value
    initOrderList()
}
const handleCurrentChange = (value: number) => {
    pageConfig.current = value
    initOrderList()
}
/**
 * @LastEditors: lexy
 * @description: 模糊搜索
 * @param {*} params
 * @returns {*}
 */
const handleSearchData = (params: any) => {
    queryConditionsTabs.params = params
    initOrderList()
}
// 批量备注
const handleRemarks = () => {
    if (!multiSelect.value.length) return ElMessage.error('请先选择订单')
    currentRemarkIds.value = multiSelect.value.map((item) => item.no)
    remarkDialog.value = true
}
// 单个备注
const handleRemark = (row: ShopOrder) => {
    currentRemarkIds.value = [row.no]
    textarea.value = JSON.parse(row?.remark)?.platformRemark || ''
    remarkDialog.value = true
}
const handleSuccess = () => {
    multiSelect.value = []
    initOrderList()
}

/**
 * 获取收货人信息
 */
const getOrderReceiver = (order: ApiOrder): OrderReceiver => {
    const shopOrderReceiver = order.shopOrders[0].orderReceiver
    return shopOrderReceiver ? shopOrderReceiver : order.orderReceiver
}
const handleChangeRow = (isChecked: CheckboxValueType, index: any) => {
    orderInfoArr.value[index].checked = isChecked as boolean
    multiSelect.value = orderInfoArr.value.filter((ro: any) => ro.checked)
    console.log(multiSelect.value, orderInfoArr.value, Array.isArray(orderInfoArr.value), 'multiSelect.value')
}
const copyOrderNo = async (orderNo: string) => {
    try {
        await toClipboard(orderNo)
        ElMessage.success('复制成功')
    } catch (e) {
        ElMessage.error('复制失败')
    }
}
/**
 * @LastEditors: lexy
 * @description: 商品总价计算
 * @param {*} shopOrderItems
 * @returns {string} TotalPrice
 */
const calculateTotalPrice = (shopOrderItems: ShopOrderItem[]) => {
    return shopOrderItems.reduce((pre, item) => {
        return Number(
            divTenThousand(item.dealPrice)
                .mul(item.num)
                .add(pre)
                .add(divTenThousand(item?.freightPrice || 0)),
        )
    }, 0)
}
const copyReceiver = async (receiver: OrderReceiver) => {
    try {
        await toClipboard(`${receiver.name}\n${receiver.mobile}\n${receiver.address}`)
        ElMessage.success('复制成功')
    } catch (e) {
        ElMessage.error('复制失败')
    }
}
function initRemark(params: any) {
    if (params?.remark && JSON.parse(params.remark)?.platformRemark) {
        return JSON.parse(params.remark)?.platformRemark
    }
    return ''
}

const isPlatformDelivery = reactive({
    shop: false,
    supplier: false,
})
const initialDeliveryConfig = async () => {
    const { data } = await doGetShopDeliveryConfig()
    if (data?.shopDeliver === 'PLATFORM') isPlatformDelivery.shop = true
    if (data?.supplierDeliver === 'PLATFORM') isPlatformDelivery.supplier = true
}
initialDeliveryConfig()
const filterDeliveryDelieryBtn = (row: ApiOrder) => {
    if (row?.extra?.distributionMode === 'SHOP_STORE') return false
    const shopOrderItems = row?.shopOrders?.[0]?.shopOrderItems || []
    if (isPlatformDelivery.supplier) {
        const consignmentLength = shopOrderItems?.filter((item) => item.sellType === 'CONSIGNMENT' && item.supplierShopType === 'SELF_OWNED').length
        if (consignmentLength > 0) {
            return true
        }
    }
    if (isPlatformDelivery.shop) {
        const notConsignmentLength = shopOrderItems?.filter((item) => item.sellType !== 'CONSIGNMENT').length
        const isSelfOwned = row?.shopOrders?.[0]?.shopType === 'SELF_OWNED'
        if (notConsignmentLength > 0 && isSelfOwned) return true
    }
    return false
}
const showDeliveryBtn = computed(() => (row: ApiOrder) => {
    return filterDeliveryDelieryBtn(row)
})
const handleBatchDelivery = async () => {
    const filterCanDeliveryList = multiSelect.value.filter((item) => filterDeliveryDelieryBtn(item))
    if (!filterCanDeliveryList.length) return ElMessage.error({ message: '请选择可发货的订单数据' })
    const orderNos = filterCanDeliveryList.map((item) => item.no)
    const { data } = await importNeedDeliveryOrders(orderNos)
    $deliveryOrderList.SET_ORDER_LIST(data)
    $router.push({ name: 'deliveryList' })
}
const handleDelivery = (no: string, shopOrderNo: string, row: ApiOrder) => {
    deliveryReactive.currentNo = no
    deliveryReactive.currentShopOrderNo = shopOrderNo
    deliveryReactive.deliverDialog = true
}
// 导出数据
// "orderNo":null,//订单号
// “buyerNickname”：null, //买家昵称
// “productName”:null,//商品名称
// “receiverName”:null,//收货人姓名
// "startTime":null,//下单开始时间
// “endTime”:null,//下单结束时间
// “distributionMode”:null,//配送方式
// “platform”:null,//所属渠道
// “status”：null,//状态  待支付 待发货 待收货 已完成 已关闭
// "exportShopOrderIds":[]//需要导出的店铺订单ids
const exportData = async (SearchFromData: any) => {
    if (multiSelect.value.length) {
        let exportShopOrderIdList = multiSelect.value.map((item) => item.shopOrders)
        let exportShopOrderId = exportShopOrderIdList.map((ite) => {
            let ites = ite.map((it: { no: string }) => it.no)
            return ites
        })
        const newExportShopOrderIds: string[] = []
        exportShopOrderId.forEach((ids) => {
            ids.forEach((idData) => newExportShopOrderIds.push(idData))
        })
        const exportShopOrderIds = Array.from(new Set(newExportShopOrderIds))
        const { code, data, msg } = await doGetExportData({ exportShopOrderIds })
        if (code !== 200) return ElMessage.error(msg || '导出失败')
        else return ElMessage.success('导出成功')
    } else {
        let param = {
            orderNo: SearchFromData.orderNo,
            supplierName: SearchFromData.supplierName,
            buyerNickname: SearchFromData.buyerNickname,
            productName: SearchFromData.productName,
            receiverName: SearchFromData.receiverName,
            startTime: SearchFromData.clinchTime?.[0],
            endTime: SearchFromData.clinchTime?.[1],
            distributionMode: SearchFromData.distributionMode,
            platform: SearchFromData.platform,
            status: activeTabName.value,
            exportShopOrderIds: '',
        }
        param.status = param.status.trim()
        const { code, data, msg } = await doGetExportData(param)
        if (code !== 200) return ElMessage.error(msg || '导出失败')
        else return ElMessage.success('导出成功')
    }
}
</script>

<template>
    <accordion-from :show="false" @search-data="handleSearchData" @change-show="tableStyleUp = $event" @export-data="exportData" />
    <div class="grey_bar"></div>
    <div class="tab_container">
        <el-tabs v-model="activeTabName" style="margin-top: 15px" @tab-change="handleTabClick">
            <el-tab-pane name=" ">
                <template #label>
                    <span>{{ FirstTabsName }}</span>
                    <el-icon class="el-icon--right-top">
                        <arrow-down />
                    </el-icon>
                    <el-dropdown ref="dropdownRef" placement="bottom-end" trigger="click" @command="handleDropdownCommand">
                        <span class="el-dropdown-link" style="height: 40px" @click.stop="handleOpen">
                            <el-icon class="el-icon--right">
                                <arrow-down />
                            </el-icon>
                        </span>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item v-for="NameItem in TabsName" :key="NameItem" :command="NameItem">{{ NameItem }} </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </template>
            </el-tab-pane>
            <el-tab-pane v-for="item in TabNavArr" :key="item.key" :name="item.key">
                <template #label>
                    <el-badge v-if="item.key === 'UN_DELIVERY'" :value="extraMap.AllDeliveryCount" class="item" :max="99">
                        <span>{{ item.value }}</span>
                    </el-badge>
                    <span v-else>{{ item.value }}</span>
                </template>
            </el-tab-pane>
        </el-tabs>
    </div>
    <div class="handle_container">
        <el-button v-if="['UN_DELIVERY', ' '].includes(activeTabName)" type="primary" @click="handleBatchDelivery">批量发货</el-button>
        <!-- <span v-if="['UN_DELIVERY', ' '].includes(activeTabName)" class="notice">自营商家的订单可设置【平台发货】，到店自提订单请在门店端(发货)核销</span> -->
        <!-- <el-button type="text" class="caozuo_btn" plain round bg style="width: 82px; height: 36px; background: #ecf5fd" @click="handleRemarks">
        批量备注
    </el-button> -->
    </div>
    <div v-if="activeTabName === 'UN_DELIVERY' && +extraMap?.AllDeliveryCount >= 1" class="reminder">
        <el-icon size="16" color="#f72020" style="transform: translateY(3px)"><WarningFilled /></el-icon>
        您共有 <span>{{ extraMap?.AllDeliveryCount || 0 }}</span> 笔待发货的订单，其中小程序端订单有
        <span>{{ extraMap?.miniDeliveryCount || 0 }}</span> 笔，超过 48 小时未发货，将触发支付风险提示，直至暂停小程序交易 ！！！
    </div>

    <split-table
        v-model:checkedItem="multiSelect"
        class="orderIndex-table"
        :class="{ tableUp: !tableStyleUp }"
        style="margin-top: 13px"
        :data="orderInfoArr"
        header-selection
    >
        <template #header="{ row }">
            <platform-comp :platform="row?.platform" />
            <el-tag v-if="row.type === 'SPIKE'" type="danger" style="margin-right: 5px">秒杀</el-tag>
            <div class="header-table" :class="{ 'is-complete': row.status === 'SYSTEM_CLOSED_' }">
                <div>
                    <el-tag v-if="row.isPriority" type="success" plain style="margin-right: 6px">优先发货</el-tag>
                    <span>订单号:{{ row.no }}</span>
                    <span class="copy" @click="copyOrderNo(row.no)">
                        <el-icon><CopyDocument /></el-icon>
                    </span>
                </div>
                <div>创建时间:{{ row.createTime }}</div>
                <div class="avatar_text money_text" style="padding-left: 10px">
                    {{ ['待支付', '已关闭'].includes(calculate(row).state.status) ? '应付款' : '实付款' }}
                    <span>￥{{ calculateTotalPrice(row.shopOrders?.[0].shopOrderItems)?.toFixed(2) }}</span>
                </div>
                <div class="payment">{{ getPaymentType(row) }}</div>
                <div class="after-sale">{{ getOrderAfterSaleStatus(row) ? '售后中' : '' }}</div>
                <div class="distribution">{{ getDeliveryMode(row) }}</div>
                <div class="shop-name">
                    <el-tooltip effect="dark" :content="`店铺:${row.shopOrders[0].shopName}`">店铺:{{ row.shopOrders[0].shopName }}</el-tooltip>
                </div>
                <remark-flag :content="initRemark(row.shopOrders[0])" @see-remark="handleRemark(row.shopOrders[0])" />
            </div>
        </template>
        <split-table-column prop="name" label="商品" width="350px">
            <template #default="{ shopOrderItems }">
                <!-- 已拆分数据展示 -->
                <div class="orderIndex-table__img-box">
                    <el-image
                        v-for="item in shopOrderItems.slice(0, 1)"
                        :key="item.id"
                        fits="cover"
                        style="width: 63px; height: 63px"
                        shape="square"
                        size="large"
                        :src="item.image"
                        :title="item.productName"
                    />
                    <span class="order-info">
                        <p class="order-info__name">{{ shopOrderItems?.[0]?.productName }}</p>
                        <p class="order-info__spec">{{ shopOrderItems?.[0]?.specs?.join(',') }}</p>
                        <p class="order-info__selltype">{{ shopOrderItems?.[0]?.supplierName }}</p>
                        <!-- <p class="order-info__selltype">{{ SellTypeEnum[shopOrderItems?.[0]?.sellType!] }}</p> -->
                    </span>
                    <div class="orderIndex-table__img-mask">
                        <span>￥{{ unitPrice(shopOrderItems)?.toFixed(2) }}</span>
                        <span style="color: #838383; font-size: 10px">共{{ num(shopOrderItems) }}件</span>
                    </div>
                </div>
            </template>
        </split-table-column>
        <split-table-column prop="age" label="收货人" :is-mixed="true">
            <template #default="{ row }">
                <div class="customer">
                    <div v-if="row?.extra?.distributionMode === 'SHOP_STORE'" class="customer__notice">到店自提订单可在门店端(发货)核销</div>
                    <div v-else-if="row?.extra?.distributionMode === 'VIRTUAL'" class="customer__notice">无需物流订单可通过商城聊天工具发货</div>
                    <template v-else>
                        <div class="customer__copy copy" @click="copyReceiver(getOrderReceiver(row))">
                            <el-icon><CopyDocument /></el-icon>
                        </div>
                        <p>{{ getOrderReceiver(row).name }}</p>
                        <p>{{ getOrderReceiver(row).mobile }}</p>
                        <p>{{ getOrderReceiver(row).address }}</p>
                    </template>
                </div>
            </template>
        </split-table-column>
        <split-table-column prop="orderStatus" label="订单状态" :is-mixed="true">
            <template #default="{ row }">
                <div style="text-align: center">
                    <p>{{ calculate(row).state.status }}</p>
                    <count-down
                        v-if="calculate(row).state.status === '待支付'"
                        :create-time="row.createTime"
                        :pay-timeout="row?.timeout?.payTimeout"
                    />
                </div>
            </template>
        </split-table-column>
        <split-table-column prop="deliveryStatus" label="发货状态">
            <template #default="{ row, shopOrderItems }">
                <div class="delivery">
                    <p>{{ getOrderDeliveryStatus(row, shopOrderItems) }}</p>
                </div>
            </template>
        </split-table-column>
        <split-table-column prop="sex" label="操作" width="200" :is-mixed="true">
            <template #default="{ row }">
                <el-button
                    v-if="calculate(row).state.status === '待发货' && showDeliveryBtn(row)"
                    round
                    type="primary"
                    @click="handleDelivery(row.no, row.shopOrders[0].no, row)"
                    >发货
                </el-button>
                <el-button
                    type="text"
                    plain
                    round
                    bg
                    style="width: 82px; height: 36px; background: #ecf5fd"
                    class="caozuo_btn"
                    @click="
                        $router.push({
                            name: 'detailsIndex',
                            query: { orderNo: row.shopOrders[0].orderNo, shopId: row.shopOrders[0].shopId },
                        })
                    "
                    >查看详情
                </el-button>
            </template>
        </split-table-column>
    </split-table>
    <el-row justify="end" align="bottom">
        <!-- <el-button type="text" class="caozuo_btn" plain round bg style="width: 82px; height: 36px; background: #ecf5fd" @click="handleRemarks"
            >批量备注
        </el-button> -->
        <page-manage
            :page-size="pageConfig.size"
            :page-num="pageConfig.current"
            :total="pageConfig.total"
            @reload="initOrderList"
            @handle-size-change="handleSizeChange"
            @handle-current-change="handleCurrentChange"
        />
    </el-row>
    <order-shipment
        v-model:isShow="deliveryReactive.deliverDialog"
        :current-no="deliveryReactive.currentNo"
        :current-shop-order-no="deliveryReactive.currentShopOrderNo"
    />
    <remark-popup
        v-model:isShow="remarkDialog"
        v-model:ids="currentRemarkIds"
        v-model:remark="textarea"
        remark-type="GOODS"
        @success="handleSuccess"
    />
    <!-- 备注弹窗e -->
</template>

<style lang="scss" scoped>
@import '@/assets/css/goods/goods.scss';

@include b(shop-name) {
    width: 120px;
    @include utils-ellipsis($line: 1);
}

@include b(orderIndex-table) {
    position: relative;
    margin-top: 10px;
    overflow-x: auto;
    height: calc(100vh - 512px);
    transition: height 0.5s;
    @include e(top) {
        @include flex(space-between);
        width: 100%;
    }
    @include e(top-time) {
        @include flex;
        & > div:nth-child(2) {
            padding: 0 60px;
        }
    }
    @include e(img-box) {
        width: 330px;
        display: flex;
        justify-content: space-between;
    }

    @include e(img) {
        flex-shrink: 0;
        border-radius: 5px;
        position: relative;
    }

    @include e(img-mask) {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 12px;
        color: #000000;
    }
}

.caozuo_btn:hover {
    color: #fff;
    background: #309af3 !important;
}

@include b(money_text) {
    font-size: 12px;
    color: #000000;
    @include utils-ellipsis($line: 1);
}

@include b(avatar_text) {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    @include e(bottom) {
        margin-bottom: 5px;
    }
}

@include b(el-icon--right-top) {
    margin-left: 5px;
}

@include b(el-icon--right) {
    padding-top: -10px;
    position: absolute;
    left: -20px;
    opacity: 0;
}

@include b(tableUp) {
    // height: calc(100vh - 335px);
    height: calc(100vh - 330px);
}
@include b(header-table) {
    width: 100%;
    @include flex(space-between);
}
@include b(order-info) {
    flex: 1;
    margin: 0 8px;
    word-break: break-all;
    line-height: 1.5;
    overflow: hidden;
    width: 0;

    @include e(name) {
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        font-weight: 600;
    }
    @include e(spec) {
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
    }
    @include e(selltype) {
        margin-top: 13px;
    }
}
@include b(customer) {
    text-align: left;
    width: 100%;
    line-height: 1.3;
    @include e(copy) {
        text-align: right;
        margin-bottom: 8px;
    }
    @include e(notice) {
        color: #f00;
    }
}
@include b(copy) {
    color: #1890ff;
    margin-left: 8px;
    cursor: pointer;
}
@include b(reminder) {
    height: 40px;
    width: 100%;
    line-height: 40px;
    background-color: #fdc3c3;
    font-size: 15px;
    padding: 0 10px;
    margin-top: 13px;
    span {
        font-size: 16px;
        color: #f72020;
        font-weight: bold;
    }
}
:deep.el-badge__content,
:deep.is-fixed {
    background-color: transparent;
    color: #f72020;
    font-weight: bold;
    font-size: 14px;
    padding-left: 14px;
    border: transparent;
}
</style>
<style scoped lang="scss">
.example-showcase .el-dropdown-link {
    cursor: pointer;
    color: var(--el-color-primary);
    display: flex;
    align-items: center;
}
.el-popper.is-customized {
    /* Set padding to ensure the height is 32px */
    padding: 6px 12px;
    background: linear-gradient(90deg, rgb(159, 229, 151), rgb(204, 229, 129));
}

.el-popper.is-customized .el-popper__arrow::before {
    background: linear-gradient(45deg, #b2e68d, #bce689);
    right: 0;
}

.notice {
    color: #f00;
    float: right;
    margin-right: 15px;
    line-height: 32px;
}
/* *---------------- */
@include b(item) {
    line-height: 20px;
    height: 20px;
}
</style>
