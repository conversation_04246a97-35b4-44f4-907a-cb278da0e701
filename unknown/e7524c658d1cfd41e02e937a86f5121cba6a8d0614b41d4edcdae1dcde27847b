import { doUpdateSupplierSellStatus } from '@/apis/good'
import auditGoodsVue from '../components/audit-goods.vue'
const usePreviewExamineDetails = () => {
    const auditGoodsRefs = ref<InstanceType<typeof auditGoodsVue> | null>(null)
    const previewVisible = ref(false)
    const commodityInfo = reactive({
        shopId: '',
        id: '',
        auditStatus: '',
        explain: '',
    })
    const handlePreviewExamineDetails = (commodity: any) => {
        commodityInfo.id = commodity.id
        commodityInfo.shopId = commodity.shopId
        commodityInfo.auditStatus = commodity.auditStatus
        commodityInfo.explain = commodity.explain

        previewVisible.value = true
    }
    const handleConfirmAudit = async () => {
        const rs: any = await auditGoodsRefs.value?.validateForm()
        const { code, msg } = await doUpdateSupplierSellStatus(
            {
                explain: rs?.explain,
                productIds: [commodityInfo.id],
            },
            rs?.status,
        )
        if (code === 200) {
            ElMessage.success({ message: msg || '更新状态成功' })
            previewVisible.value = false
        } else {
            ElMessage.error({ message: msg || '更新状态失败' })
        }
    }
    return {
        previewVisible,
        commodityInfo,
        handlePreviewExamineDetails,
        auditGoodsRefs,
        handleConfirmAudit,
    }
}

export default usePreviewExamineDetails
