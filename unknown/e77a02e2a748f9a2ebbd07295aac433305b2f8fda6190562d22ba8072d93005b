{"compilerOptions": {"types": ["node", "vite/client", "vitest/globals", "element-plus/global", "unplugin-vue-define-options/macros-global"], "target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "strictNullChecks": true, "noImplicitAny": true, "noImplicitThis": true, "lib": ["ESNext", "DOM", "DOM.Iterable"], "baseUrl": ".", "paths": {"@/*": ["src/*"], "@components/*": ["src/components/*"], "@views/*": ["src/views/*"], "@apis/*": ["src/apis/*"], "@utils/*": ["src/utils/*"], "@hooks/*": ["src/hooks/*"], "#/*": ["./*"]}, "typeRoots": ["./node_modules/@types/", "./types"]}, "include": ["src/App.vue", "src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "types/*.d.ts"], "exclude": ["node_modules"]}