<template>
    <el-table :data="tableData" row-key="id" border style="width: 100%">
        <el-table-column label="用户信息">
            <template #default="{ row }">
                <div class="user__info">
                    <el-image :src="row?.avatar" style="width: 20px; height: 20px; flex-shrink: 0" />
                    <span>
                        {{ row.nickname || `用户${row.userId.slice(-6)}` }}
                        <template v-if="row.memberInfos">
                            <label
                                v-for="item in row.memberInfos"
                                :key="item.id"
                                class="user__info--label"
                                :style="{ color: item.fontColor, border: `1px solid ${item.labelColor}` }"
                                >{{ item.name }}</label
                            >
                        </template>
                    </span>
                </div>
                <span>评论时间：{{ row?.createTime }}</span>
            </template>
        </el-table-column>
        <el-table-column prop="comment" label="回复内容">
            <template #default="{ row }">
                <div class="user__comment">
                    <p>{{ row.comment }}</p>
                </div>
            </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="100">
            <template #default="scope">
                <el-row justify="center" style="margin-top: 15px">
                    <el-button type="error" link size="small" @click="handleDelete(scope.row.id)">删除</el-button>
                </el-row>
                <el-row justify="center" style="margin-top: 15px">
                    <el-button size="small" type="primary" link @click="handleChoiceness(scope.row)">
                        {{ !scope.row.isDisplay ? '取消隐藏' : '设为隐藏' }}
                    </el-button>
                </el-row>
                <el-row v-if="scope.row.replyList?.length" justify="center" style="margin-top: 15px">
                    <el-button size="small" type="primary" link @click="handlerMore(scope.row)"> 查看更多 </el-button>
                </el-row>
            </template>
        </el-table-column>
    </el-table>
    <el-dialog v-model="showReplyDialog" title="全部回复" width="80%" :align-center="true">
        <appraise-replay :evaluate-list="currentReplyList" />
    </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import AppraiseReplay from './appraise-replay.vue'
import { doGetEvaluateDetail, doDelEvaluateReply, doHideEvaluateReply } from '@/apis/order/appraise'

const props = defineProps({
    evaluateId: {
        type: String,
        default: '',
    },
    evaluateList: {
        type: Array,
        default: () => [],
    },
})
const tableData = ref([])
const showReplyDialog = ref(false)
const currentReplyList = ref([])

const initEvaluateInfo = () => {
    doGetEvaluateDetail(props.evaluateId).then((res) => {
        tableData.value = res.data || []
    })
}

const handleChoiceness = (row: Evaluate) => {
    ElMessageBox.confirm('确定隐藏该回复吗?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(() => {
        doHideEvaluateReply(row.id, row.isDisplay).then(({ code }) => {
            if (code !== 200) return ElMessage.error(`隐藏失败`)
            ElMessage.success(`隐藏成功`)
            initEvaluateInfo()
        })
    })
}

const handleDelete = (id: string) => {
    ElMessageBox.confirm('删除评价内容将不保存，确定删除吗?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            doDelEvaluateReply(id).then(({ code }) => {
                if (code !== 200) return ElMessage.error(`删除失败`)
                ElMessage.success(`删除成功`)
                initEvaluateInfo()
            })
        })
        .catch(() => {})
}

const handlerMore = (row: Evaluate) => {
    currentReplyList.value = row.replyList
    showReplyDialog.value = true
}
watch(
    () => [props.evaluateId, props.evaluateList],
    () => {
        if (props.evaluateId) {
            initEvaluateInfo()
        } else {
            tableData.value = props.evaluateList
        }
    },
    { immediate: true },
)
</script>

<style scoped lang="scss">
@include b(user) {
    @include e(info) {
        @include flex(space-between);
        span {
            flex: 1;
            margin: 0 10px;
            @include utils-ellipsis(1);
        }

        @include m(label) {
            color: #4589f9;
            font-size: 12px;
            height: 16px;
            line-height: 16px;
            padding: 0 4px;
            border-radius: 2px;
            border: 1px solid #4589f9;
            text-align: center;
            margin-right: 9px;
        }
        @include e(comment) {
            @include utils-ellipsis(4);
            min-height: 95px;
            font-size: 12px;
        }
    }
}
</style>
