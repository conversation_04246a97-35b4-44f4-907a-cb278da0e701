<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-03-26 14:52:25
 * @LastEditors: lexy
 * @LastEditTime: 2023-09-20 18:50:22
-->
<template>
    <shop-list-search @search-params="searchHandle" @show-change="showChange" />
    <div class="grey_bar"></div>
    <div class="tab_container">
        <el-tabs v-model="tabChoose" class="tabs" @tab-change="tabChangeHandle">
            <el-tab-pane label="店铺列表" name=""></el-tab-pane>
            <el-tab-pane label="待审核" name="UNDER_REVIEW"></el-tab-pane>
            <el-tab-pane label="已拒绝" name="REJECT"></el-tab-pane>
        </el-tabs>
    </div>
    <list-part ref="listPartRef" :search-params="rv.searchParams" :current-tab-choose="tabChoose" />
</template>

<script lang="ts" setup>
import ShopListSearch from './components/ShopListSearch.vue'
import ListPart from './components/ListPart.vue'
import type { TabsPaneContext } from 'element-plus'
const statuss = ref()
const listPartRef = ref()
const $route = useRoute()
type SearchParamType = Record<'no' | 'name' | 'status', string>
const rv = reactive({
    searchParams: {
        no: '',
        name: '',
        status: $route.query.name || '',
    },
})
const tabChoose = ref($route.query.name || '')
const showChangeStatus = ref(false)
const tabChangeHandle = (tab: TabsPaneContext) => {
    statuss.value = tab
    listPartRef.value.initList({ ...rv.searchParams, status: tab }) //zrb:叠加搜索参数
}
/**
 * @LastEditors: lexy
 * @description: 头部搜索上拉收起状态
 * @param {*} val
 * @returns {*}
 */
const showChange = (val: boolean) => {
    showChangeStatus.value = val
}
/**
 * @LastEditors: lexy
 * @description: 改变tabs切换并重新请求对应数据
 * @param {*} status
 * @returns {*}
 */
const changeTabsEvent = (status: string) => {
    if (status.trim()) {
        tabChoose.value = status
    }
    listPartRef.value.initList({ status })
}
provide('parentTabChangeHandle', changeTabsEvent)
provide('parentTabChoose', tabChoose)
provide('parentShowChangeStatus', showChangeStatus)
const searchHandle = async (params: SearchParamType) => {
    //zrb:更新搜索参数
    rv.searchParams = params
    if (tabChoose.value !== '') {
        listPartRef.value.initList({ ...params, status: tabChoose.value })
        return
    }
    listPartRef.value.initList(params)
}
</script>
