<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-12-01 10:46:45
 * @LastEditors: lexy 
 * @LastEditTime: 2024-01-27 14:57:43
-->
<script setup lang="ts">
import { doGetShopBalance, doPostWithdraw } from '@/apis/finance'
import { ElMessage } from 'element-plus'
import BasicSettle from './settle-components/basic-settle.vue'
import { QuestionFilled } from '@element-plus/icons-vue'
import { doPostExportShopWithdrawData } from '@/apis/overview'
import DecimalInput from '@/components/decimal-input/decimal-input.vue'
const showDescriptionDialog = ref(false)
const balanceInfo = reactive({
    platformBalance: '0',
    shopBalance: '0',
    supplierBalance: '0',
})
const basicSetterRef = ref<InstanceType<typeof BasicSettle> | null>(null)
const tabPaneName = ref('APPLYING')
const { divTenThousand } = useConvert()
const showWithdrawDialog = ref(false)
const handleShowDialog = () => {
    showWithdrawDialog.value = true
}
const withdrawValue = ref(0.01)
async function initBalance() {
    const { code, data, msg } = await doGetShopBalance()
    if (code === 200) {
        balanceInfo.platformBalance = data.undrawn
        balanceInfo.shopBalance = data.shopBalance
        balanceInfo.supplierBalance = data.supplierBalance
    } else {
        ElMessage.error(msg ? msg : '获取余额信息失败')
    }
}
const handleCloseDialog = () => {
    withdrawValue.value = 0.01
}
// 商家端 'SHOP'，供应商 'SUPPLIER'
const type = 'SHOP'
const handleSubmit = async () => {
    try {
        if (withdrawValue.value < 0.01) {
            return ElMessage.error('输入值最小为0.01')
        }
        if (withdrawValue.value > Number(divTenThousand(balanceInfo.platformBalance))) {
            return ElMessage.error('提现金额不可大于余额')
        }
        const { code, msg } = await doPostWithdraw(withdrawValue.value * 10000, type)
        if (code === 200) {
            ElMessage.success('申请成功')
            showWithdrawDialog.value = false
            initBalance()
            basicSetterRef.value?.initWithdrawList()
        } else {
            ElMessage.error(msg ? msg : '申请失败')
        }
    } catch (error) {
        ElMessage.error('申请失败')
    }
}
initBalance()
const checkedData = ref<any[]>([])
const handleExport = async () => {
    const params: any = {}
    if (checkedData.value.length) {
        params.exportIds = checkedData.value.map((item) => item.id)
    }
    const { code, msg } = await doPostExportShopWithdrawData(params)
    if (code === 200) {
        ElMessage.success({ message: msg || '导出成功' })
    } else {
        ElMessage.error({ message: msg || '导出失败' })
    }
}
</script>

<template>
    <div class="settle-content">
        <div class="settle">
            <div class="settle__item">
                <div class="settle__item--title">平台余额</div>
                <div class="settle__item-bottom">
                    <div class="settle__item--price">{{ divTenThousand(balanceInfo.platformBalance) }}</div>
                    <div class="settle__item--btn" @click="handleShowDialog">提现</div>
                </div>
            </div>
            <div class="settle__item">
                <div class="settle__item--title">商家余额</div>
                <div class="settle__item-bottom">
                    <div class="settle__item--price">{{ divTenThousand(balanceInfo.shopBalance) }}</div>
                </div>
            </div>
            <div class="settle__item">
                <div class="settle__item--title">供应商余额</div>
                <div class="settle__item-bottom">
                    <div class="settle__item--price">{{ divTenThousand(balanceInfo.supplierBalance) }}</div>
                </div>
            </div>
        </div>
        <div class="settle__tabs">
            <!-- <el-tabs v-model="tabPaneName">
                <el-tab-pane label="待审核" name="APPLYING" />
                <el-tab-pane label="已到账" name="SUCCESS" />
                <el-tab-pane label="已拒绝" name="FORBIDDEN" />
            </el-tabs> -->
            <div class="settle__tabs--export">
                <el-button size="small" type="primary" @click="handleExport">导出</el-button>
                <el-icon class="export-icon" @click="showDescriptionDialog = true"><QuestionFilled /></el-icon>
            </div>
        </div>
        <basic-settle ref="basicSetterRef" v-model:checked-data="checkedData" :status="tabPaneName" />
        <el-dialog v-model="showWithdrawDialog" @close="handleCloseDialog">
            <template #header="{ titleId, titleClass }">
                <div class="dialog-header">
                    <h4 :id="titleId" :class="titleClass">提现信息</h4>
                    <h5 style="margin-bottom: 2px; color: #f53f3f">手续费：1元/笔</h5>
                </div>
            </template>
            <el-form>
                <el-form-item label="提现金额" required>
                    <decimal-input v-model="withdrawValue" :max="999999" :min="1" :decimal-places="2" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="showWithdrawDialog = false">取消</el-button>
                <el-button type="primary" @click="handleSubmit"> 确定 </el-button>
            </template>
        </el-dialog>
        <el-dialog v-model="showDescriptionDialog" :width="650" title="余额说明">
            <p class="dialog-line">1、余额是指平台从订单销售（订单状态已完成计入）获得的经营收入，平台可发起提现即将资金由托管账号转到银行账户上。</p>
            <p class="dialog-line">2、商家余额是所有商家余额的总计</p>
            <p class="dialog-line">3、供应商余额是所有供应商余额的总计</p>
        </el-dialog>
    </div>
</template>

<style lang="scss" scoped>
@include b(settle) {
    color: #fff;

    @include flex(space-between);
    margin-bottom: 10px;
    @include e(item) {
        width: 270px;
        height: 130px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: center;
        font-size: 13px;
        padding: 0 30px;
        border-radius: 14px;
        @include m(title) {
            margin-bottom: 6px;
        }
        @include m(price) {
            font-size: 30px;
            font-weight: bold;
            &::before {
                content: '￥';
                display: inline-block;
                font-size: 13px;
                vertical-align: baseline;
            }
        }
        @include m(btn) {
            width: 50px;
            height: 24px;
            line-height: 24px;
            text-align: center;
            cursor: pointer;
            border-radius: 10px;
            border: 1px solid #fff;
        }
        &:nth-child(1) {
            background: url('https://devoss.chongyoulingxi.com/system-front/mobile/finance_settle_top_1.png');
        }
        &:nth-child(2) {
            background: url('https://devoss.chongyoulingxi.com/system-front/mobile/finance_settle_top_2.png');
        }
        &:nth-child(3) {
            background: url('https://devoss.chongyoulingxi.com/system-front/mobile/finance_settle_top_3.png');
        }
        &:nth-child(n) {
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
    @include e(item-bottom) {
        @include flex(space-between, flex-end);
    }
    @include e(tabs) {
        position: relative;
        height: 40px;
        margin-bottom: 10px;

        @include m(export) {
            position: absolute;
            top: 10px;
            right: 0;
            @include flex();
            @include b(export-icon) {
                font-size: 22px;
                margin-left: 10px;
            }
        }

        &::after {
            background-color: var(--el-border-color-light);
            bottom: 0;
            content: '';
            height: 2px;
            left: 0;
            position: absolute;
            width: 100%;
            z-index: var(--el-index-normal);
        }
    }
}
@include b(dialog-line) {
    line-height: 2;
}
.dialog-header {
    @include flex(flex-start, flex-end);
    gap: 16px;
}
.settle-content {
    min-height: 561px;
    padding: 20px 15px;
}
</style>
