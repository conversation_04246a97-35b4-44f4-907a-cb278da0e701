<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus'
import PageManage from '@/components/PageManage.vue'
import { doGetRateJobList, doGetRateJobDetail, doDelRateJob } from '@/apis/set/RateJob/index'
import { RateJobType, SCOPE_KEY_TEXT } from '.'
// 导入任务详情组件
import JobDetail from './JobDetail.vue'
import EditRateJob from './EditRateJob.vue'

/**
 * 表格渲染数据
 */
const tableDataRateJob = ref({
    current: 1,
    pages: 1,
    records: [],
    size: 10,
    total: 0,
})

// 表格加载状态
const tableLoading = ref(false)

// 详情弹窗
const jobDetail = ref<RateJobType>()
const showDetail = ref(false)

// 编辑弹窗
const showEditDialog = ref(false)
const editId = ref('')

/*
 * 生命周期
 */
onMounted(() => {
    initTableDataRateJob()
})

/**
 * 初始化执行任务列表
 */
const initTableDataRateJob = async () => {
    tableLoading.value = true
    try {
        const { current, size } = tableDataRateJob.value
        const { code, data } = await doGetRateJobList({ current, size })
        if (code === 200) {
            tableDataRateJob.value = data
        } else {
            ElMessage.error('获取任务列表失败')
        }
    } catch (error) {
        console.error('获取任务列表失败:', error)
        ElMessage.error('获取任务列表失败')
    } finally {
        tableLoading.value = false
    }
}

/**
 * 查看详情
 */
const handleRateJobDetail = async (row: RateJobType) => {
    try {
        if (row.id) {
            const { code, data } = await doGetRateJobDetail(row.id)
            if (code === 200 && data) {
                jobDetail.value = data
            } else {
                jobDetail.value = row
            }
        } else {
            jobDetail.value = row
        }
        showDetail.value = true
    } catch (error) {
        console.error('获取任务详情失败:', error)
        jobDetail.value = row
        showDetail.value = true
    }
}

/**
 * 编辑任务
 */
const handleEditRateJob = (row?: RateJobType) => {
    if (row && row.id) {
        // 检查任务是否已执行
        if (new Date(row.executeTime).getTime() < Date.now()) {
            ElMessage.warning('已执行的任务不能编辑')
            return
        }
        editId.value = row.id
    } else {
        editId.value = ''
    }
    showEditDialog.value = true
}

/**
 * 删除任务
 */
const handleDeleteRateJob = async (item: RateJobType) => {
    if (new Date(item.executeTime).getTime() < Date.now()) {
        ElMessage.warning('已执行的任务不能删除')
        return
    }

    try {
        await ElMessageBox.confirm('确定要删除该任务吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })

        if (item.id) {
            const { code, msg } = await doDelRateJob(item.id)
            if (code === 200) {
                ElMessage.success('删除成功')
                await initTableDataRateJob()
            } else {
                ElMessage.error(msg || '删除失败')
            }
        }
    } catch (error) {
        // 用户取消删除，不做处理
        if (error !== 'cancel') {
            console.error('删除任务失败:', error)
        }
    }
}

/**
 * 表格样式
 */
const TabHeaderStyle = () => {
    return 'font-size: 14px;font-weight: Bold;color: #000000;'
}

const TabRowStyle = () => {
    return 'font-size: 12px;color: #000000; height: 68px;'
}

const TabCellStyle = () => {
    return { border: 'unset' }
}

/**
 * 分页器
 */
const handleSizeChange = (value: number) => {
    tableDataRateJob.value.current = 1
    tableDataRateJob.value.size = value
    initTableDataRateJob()
}

const handleCurrentChange = (value: number) => {
    tableDataRateJob.value.current = value
    initTableDataRateJob()
}

/**
 * 判断任务是否已执行
 */
const isTaskExecuted = (executeTime: string) => {
    return new Date(executeTime).getTime() < Date.now()
}
</script>

<template>
    <div class="handle_container">
        <el-row>
            <el-button type="primary" round style="height: 36px; margin-right: 15px" @click="handleEditRateJob">添加任务</el-button>
        </el-row>
    </div>
    <!-- 表格部分 -->
    <div class="table_container">
        <el-table
            v-loading="tableLoading"
            height="calc(100vh - 300px)"
            :header-row-style="TabHeaderStyle"
            :row-style="TabRowStyle"
            :cell-style="TabCellStyle"
            :data="tableDataRateJob.records"
            style="margin-top: 20px; padding: 15px 20px; background: #eef1f6"
        >
            <el-table-column prop="categoryIds" label="平台类目" align="center">
                <template #default="{ row }">
                    <el-space wrap>
                        <el-tag v-for="item in row.categoryList" :key="item.id" effect="dark">{{ item.name }}</el-tag>
                    </el-space>
                </template>
            </el-table-column>
            <el-table-column prop="scope" label="生效范围" width="120" align="center">
                <template #default="{ row }">
                    {{ SCOPE_KEY_TEXT[row.scope] }}
                </template>
            </el-table-column>
            <el-table-column label="生效商户" prop="shopList" align="center" width="160">
                <template #default="{ row }">
                    <el-space wrap v-if="row.shopList && row.shopList.length">
                        <el-tag v-for="(item, index) in row.shopList" :key="index" effect="light">{{ item.name }}</el-tag>
                    </el-space>
                    <span v-else class="text-gray">全部商户</span>
                </template>
            </el-table-column>
            <el-table-column label="执行扣率" align="center">
                <el-table-column prop="deductionRatio" label="商家" width="80" align="center">
                    <template #default="{ row }">
                        <span>{{ row.deductionRatio }}{{ row.deductionRatio ? '%' : '/' }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="supplierDeductionRatio" label="供应商" width="80" align="center">
                    <template #default="{ row }">
                        <span>{{ row.supplierDeductionRatio }}{{ row.supplierDeductionRatio ? '%' : '/' }}</span>
                    </template>
                </el-table-column>
            </el-table-column>
            <el-table-column label="执行时间" prop="executeTime" width="160" align="center">
                <template #default="{ row }">
                    <div :class="{ 'executed-time': isTaskExecuted(row.executeTime) }">
                        {{ row.executeTime }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="创建时间" prop="createTime" width="160" align="center" />
            <el-table-column prop="address" label="操作" width="160" align="center">
                <template #default="{ row }">
                    <el-button type="text" @click="handleRateJobDetail(row)">查看</el-button>
                    <el-button type="text" @click="handleEditRateJob(row)" :disabled="isTaskExecuted(row.executeTime)">编辑</el-button>
                    <el-button type="danger" link @click="handleDeleteRateJob(row)" :disabled="isTaskExecuted(row.executeTime)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <page-manage
            :load-init="true"
            :page-size="tableDataRateJob.size"
            :page-num="tableDataRateJob.current"
            :total="tableDataRateJob.total"
            @reload="initTableDataRateJob"
            @handle-size-change="handleSizeChange"
            @handle-current-change="handleCurrentChange"
        />

        <!-- 详情弹窗 -->
        <job-detail v-model:show="showDetail" :detail="jobDetail" />

        <!-- 编辑弹窗 -->
        <edit-rate-job v-model:show="showEditDialog" :edit-id="editId" @success="initTableDataRateJob" />
    </div>
</template>

<style lang="scss" scoped>
@include b(table) {
    overflow: auto;
}
@include b(RateJobRateJob) {
    :deep(.el-table__body) {
        border-collapse: separate;
        border-spacing: 0 6px;
    }
}

.executed-time {
    color: #909399;
}

.text-gray {
    color: #909399;
}
</style>
