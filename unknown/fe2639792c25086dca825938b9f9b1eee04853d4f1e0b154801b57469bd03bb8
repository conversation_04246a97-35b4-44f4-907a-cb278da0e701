<!--
 * @description: 
 * @Author: lexy
 * @Date: 2023-02-07 16:51:07
 * @LastEditors: lexy
 * @LastEditTime: 2023-02-07 18:07:05
-->
<script setup lang="ts">
import { doGetPrivateAgreement, doPostPrivateAgreement } from '@/apis/set'
import { ElMessage } from 'element-plus'
import QEdit from '@/components/q-editor/q-edit.vue'
import { PrivacyAgreementType } from '../type'
/*
 *variable
 */
const richStr = ref('')
const richStrId = ref('')
const loadingExist = ref(false)
const currentTab = ref(PrivacyAgreementType.MEMBER)
const headerList = reactive([
    { label: '付费会员协议', value: PrivacyAgreementType.MEMBER },
    { label: '分销商协议', value: PrivacyAgreementType.DISTRIBUTOR },
    { label: '供应商协议', value: PrivacyAgreementType.SUPPLIER },
    { label: '隐私协议', value: PrivacyAgreementType.PRIVATE },
    { label: '用户协议', value: PrivacyAgreementType.USER },
    { label: '注销协议', value: PrivacyAgreementType.CANCEL },
    { label: '积分协议', value: PrivacyAgreementType.POINTS },
])
/*
 *lifeCircle
 */
initPrivateAgreement()
/*
 *function
 */
const handleSaveAgreement = async () => {
    const { code } = await doPostPrivateAgreement(richStr.value, currentTab.value, richStrId.value)
    if (code === 200) {
        ElMessage.success('编辑成功')
    } else {
        ElMessage.error('编辑失败')
    }
}
async function initPrivateAgreement() {
    const { code, data } = await doGetPrivateAgreement(currentTab.value)
    if (code === 200) {
        richStrId.value = data.id
        richStr.value = data.platformPrivacyAgreementText
    } else {
        ElMessage.error('获取协议失败')
    }
}

function changeTab() {
    richStr.value = ''
    richStrId.value = ''
    initPrivateAgreement()
}
</script>

<template>
    <!-- <div class="privateEdit">
        <q-edit v-model:content="richStr" height="400px" />
    </div>
    <el-button type="primary" style="margin: 20px" @click="handleSaveAgreement">保存</el-button> -->

    <el-card :bordered="false" shadow="never" class="ivu-mt" :body-style="{ padding: '16px 10px' }">
        <template #header>
            <el-tabs v-model="currentTab" @tab-change="changeTab">
                <el-tab-pane v-for="(item, index) in headerList" :key="index" :label="item.label" :name="item.value" />
            </el-tabs>
        </template>
        <el-row class="content">
            <el-col :span="14" :style="{ border: '1px solid #e4e7ed', borderRadius: '6px' }">
                <q-edit v-model:content="richStr" height="400px" />
            </el-col>
            <el-col :span="8" style="width: 33%">
                <div class="ifam">
                    <div class="content" v-html="richStr"></div>
                </div>
            </el-col>
            <el-col :span="6" :offset="12" :style="{ marginTop: '20px' }">
                <el-button v-debounce="{ wait: 2000, type: 'click' }" class="bnt" type="primary" :loading="loadingExist" @click="handleSaveAgreement">
                    保存
                </el-button>
            </el-col>
        </el-row>
    </el-card>
</template>

<style lang="scss" scoped>
:deep(.el-card__header) {
    padding-bottom: 0px;
    .el-tabs__header {
        margin: 0;
    }
}

@include b(el-tabs__item) {
    :deep(.el-tabs__item__inner) {
        height: 54px !important;
        line-height: 54px !important;
    }
}
@include b(agreemant) {
    background-color: #fff;
}
@include b(content) {
    padding: 10px 16px;
}
@include b(ifam) {
    width: 380px;
    height: 644px;
    background: url('../../../assets/image/ag-phone.png') no-repeat center top;
    background-size: 380px 644px;
    padding: 40px 20px;
    padding-top: 50px;
    margin: 0 auto 0 20px;

    .content {
        height: 560px;
        scrollbar-width: none; /* firefox */
        -ms-overflow-style: none; /* IE 10+ */
        overflow-x: hidden;
        overflow-y: auto;
        ::-webkit-scrollbar {
            display: none; /* Chrome Safari */
        }
    }
}
@include b(new_tab) {
    :deep(.ivu-tabs-nav .ivu-tabs-tab) {
        padding: 4px 16px 20px !important;
        font-weight: 500;
    }
}
</style>
