<!--
 * @description: 
 * @Author: lexy
 * @Date: 2022-07-23 16:33:17
 * @LastEditors: lexy
 * @LastEditTime: 2022-11-04 14:10:21
-->
<script setup lang="ts">
import { doGetOrderList } from '@/apis/order'
import { ApiOrder } from '@/views/order/types/order'

type ActiveName = 'orderInfo' | 'logisticsInfo'
/*
 *variable
 */
const tabPaneOrderDetails = [
    { label: '订单信息', name: 'orderInfo' },
    { label: '物流信息', name: 'logisticsInfo' },
]
const $route = useRoute()
const reactiveAsyncComponent = reactive({
    orderInfo: defineAsyncComponent(() => import('./components/orderInfo.vue')),
    logisticsInfo: defineAsyncComponent(() => import('./components/logisticsInfo.vue')),
})
const activeName = ref<ActiveName>('orderInfo')
// 订单
const OrderDetailsData = ref<ApiOrder>()
/*
 *lifeCircle
 */
initOrderDetails()

/*
 *function
 */
async function initOrderDetails() {
    const { orderNo, shopId } = $route.query
    if (!orderNo) return
    const { code, data } = await doGetOrderList({ shopId }, orderNo as string)
    if (code === 200) {
        OrderDetailsData.value = data
    }
}
</script>

<template>
    <div class="tab_container">
        <el-tabs v-model="activeName">
            <el-tab-pane v-for="tabPaneItem in tabPaneOrderDetails" :key="tabPaneItem.label" :label="tabPaneItem.label" :name="tabPaneItem.name" />
        </el-tabs>
    </div>
    <div style="height: calc(100vh - 160px); overflow: auto">
        <component :is="reactiveAsyncComponent[activeName]" :order="OrderDetailsData"></component>
    </div>
</template>
